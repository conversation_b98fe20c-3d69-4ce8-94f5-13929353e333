import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
const baseUrl = `/services/whiskerguardviolationservice/api/violation/`
const investigateBaseUrl = `/services/whiskerguardviolationservice/api/problem/investigate/`
const baseApi = `/services/whiskerguardviolationservice/api/`

export default {
    // 获取调查任务列表
    getList(params, page) {
        return request(baseUrl + 'details/user' + `?page=${page.page}&size=${page.size}`, params, 'post')
    },
    
    // 获取问题调查任务列表
    getInvestigateTaskList(params) {
        return request(investigateBaseUrl + 'tasks/search', params, 'post')
    },
    // 创建违规举报处理
    createViolation(post){
        return request(baseUrl + 'deals', post, 'post')
    },
    // 创建问题调查任务
    createInvestigateTask(params) {
        return request(investigateBaseUrl + 'tasks', params, 'post')
    },
    // 获取问题调查任务详情
    getInvestigateTaskDetail(taskId) {
        return request(investigateBaseUrl + `tasks/${taskId}`, {}, 'get')
    },
    // 获取违规处理信息
    getViolationDeal(detailId) {
        return request(baseUrl + `deals/${detailId}`, {}, 'get')
    },
    // 创建责任追究处理任务
    createInvestigateDeal(params) {
        return request(baseApi + 'responsibility/investigate/corrections', params, 'post')
    },
    // 创建持续改进处理任务
    createContinuousImprovementDeal(params) {
        return request(baseApi + 'continuous/improvement/experiences', params, 'post')
    },
    // 获取责任追究整改列表
    getResponsibilityCorrectionList(params, page) {
        const url = baseApi + 'responsibility/investigate/corrections/search' + (page ? `?page=${page.page}&size=${page.size}` : '')
        return request(url, params, 'post')
    },
}