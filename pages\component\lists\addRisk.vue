<template>
	<view class="transform-table">
		<view class="transform-table__form">
			<uv-form labelPosition="top" label-width="120px" :model="riskForm" :rules="rules" ref="form">
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">职责信息区</text>
					</view>
					<uv-form-item prop="businessType" required label="业务类型">
						<!-- valueKey="value" -->
						<picker-input v-model="riskForm.businessType" :columns="businessColumns" displayKey="name"
							valueKey="value" placeholder="请选择业务类型" />
					</uv-form-item>

					<uv-form-item prop="orgUnitId" required label="部门">
						<!-- <picker-input v-model="riskForm.orgUnitId" :columns="[getDepartments]" displayKey="name"
							valueKey="id" placeholder="请选择部门" /> -->
						<tree-picker v-model="riskForm.orgUnitId" :multiple="false" :default-expand-level="1"
							title="选择部门（单选）" placeholder="请选择部门"></tree-picker>
					</uv-form-item>
				</view>
				<!-- 风险点  -->
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">风险信息区</text>
						<view class="ai-tag" @click="handleRiskTypeGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>

					<uv-form-item prop="riskDescription" required label="风险描述">
						<view class="textarea-container">
							<textarea class="own-textarea" autoHeight maxlength="9999" v-model="riskForm.riskDescription"
								placeholder="请输入风险描述" placeholder-style="color:#dadbde"></textarea>
						</view>
					</uv-form-item>
					<uv-form-item prop="riskCause" required label="风险原因">
						<view class="textarea-container">
							<textarea class="own-textarea" autoHeight maxlength="9999" v-model="riskForm.riskCause"
								placeholder="请输入风险原因" placeholder-style="color:#dadbde"></textarea>
						</view>
					</uv-form-item>
					<uv-form-item prop="riskConsequence" required label="风险后果">
						<view class="textarea-container">
							<textarea class="own-textarea" autoHeight maxlength="9999" v-model="riskForm.riskConsequence"
								placeholder="请输入风险后果" placeholder-style="color:#dadbde"></textarea>
						</view>
					</uv-form-item>
				</view>

				<!-- 合规义务填写 -->
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">合规义务信息区/合规依据</text>
						<view class="ai-tag" @click="handleRiskLawGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>
					<uv-form-item prop="lawsRegulations" label="法律规定">
						<view class="textarea-container">
							<uv-textarea autoHeight maxlength="9999" v-model="riskForm.lawsRegulations"
								placeholder="请输入法律规定"></uv-textarea>
						</view>
					</uv-form-item>
					<uv-form-item prop="regulatoryRequirements" label="监管规定">
						<view class="textarea-container">
							<uv-textarea autoHeight maxlength="9999" v-model="riskForm.regulatoryRequirements"
								placeholder="请输入监管规定"></uv-textarea>
						</view>
					</uv-form-item>
					<uv-form-item prop="rulesRegulations" label="规章制度">
						<view class="textarea-container">
							<uv-textarea autoHeight maxlength="9999" v-model="riskForm.rulesRegulations"
								placeholder="请输入规章制度"></uv-textarea>
						</view>
					</uv-form-item>
				</view>
				<!-- 责任主体 -->
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">管理信息区</text>
						<view class="ai-tag" @click="handleRiskControlGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>
					<uv-form-item prop="controlMeasures" required label="风险管控措施">
						<view class="textarea-container">
							<uv-textarea autoHeight maxlength="9999" v-model="riskForm.controlMeasures"
								placeholder="请输入风险管控措施"></uv-textarea>
						</view>
					</uv-form-item>

					<uv-form-item prop="responsibleOrgUnitId" required label="归口部门">
						<view class="checkbox-container">
							<uv-checkbox-group placement="row" v-model="riskForm.responsibleOrgUnitId">
								<view class="checkbox-wrapper">
									<uv-checkbox v-for="(dept, index) in getDepartments" :key="dept.id" :name="dept.id"
										:label="dept.name" :customStyle="checkboxStyle">
									</uv-checkbox>
								</view>
							</uv-checkbox-group>
						</view>
					</uv-form-item>
					<uv-form-item prop="cooperatingOrgUnitId" required label="配合部门">
						<view class="checkbox-container">
							<uv-checkbox-group placement="row" v-model="riskForm.cooperatingOrgUnitId">
								<view class="checkbox-wrapper">
									<uv-checkbox v-for="(dept, index) in getDepartments" :key="dept.id" :name="dept.id"
										:label="dept.name" :customStyle="checkboxStyle">
									</uv-checkbox>
								</view>
							</uv-checkbox-group>
						</view>
					</uv-form-item>
				</view>

				<!-- 风险等级区 -->
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">风险等级区</text>
						<view class="ai-tag" @click="handleRiskLevelGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>
					<uv-form-item prop="riskLevelModel" required label="风险等级">
						<view class="risk-level-display" :class="getRiskLevelClass(riskForm.riskLevelModel)">
							{{ getRiskLevelText(riskForm.riskLevelModel) }}
						</view>
						<!-- <filter-tags v-model="riskForm.riskLevelModel" :fixed="false" :tags="tags" valueKey="value" /> -->
					</uv-form-item>
				</view>
			</uv-form>
		</view>
		<!-- 律师在PENDING状态下可以操作 -->
		<view v-if="isDataLoaded && lawyer && riskForm.approvalStatus === 'PENDING'">
			<FooterBar @click="handleFooterClick" :buttons="footerButtons" />
		</view>
		<!-- 非律师在草稿状态或无状态下可以操作 -->
		<view v-if="isDataLoaded && !lawyer && (riskForm.approvalStatus === 'DRAFT' || !riskForm.approvalStatus)">
			<FooterBar @click="handleFooterClick" :buttons="footerButtons" />
		</view>

		<!-- AI生成进度弹窗 -->
		<uni-popup ref="progressDialog" type="dialog">
			<view class="dialog-content">
				<text class="dialog-title">猫伯伯智能识别中</text>
				<view class="progress-body">
					<image src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
						style="width: 48px; height: 48px;" class="pulse"></image>
					<text class="progress-text">{{ currentProgress }}</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import {
	ref
} from 'vue'
import TreePicker from '@/components/tree-picker.vue'
import pickerInput from '@/components/picker-input.vue'
import filterTags from '@/components/filterTags.vue'
import FooterBar from '@/components/footerBar.vue'
import threeAI from '@/api/threeAI/threeAI.js'
import {
	useUserStore
} from '@/store/pinia.js';
import {
	onLoad
} from '@dcloudio/uni-app';
import addForm from '@/api/duties/addForm.js'
import homeApi from '@/api/home/<USER>'
import getDictData from '@/utils/dict.js'
const userStore = useUserStore();
const getDepartments = userStore.getDepartments()
console.log(getDepartments, 'getDepartments');
const lawyer = ref(false);
lawyer.value = userStore.lawyer

// 控制提交按钮状态的变量
const isSubmitting = ref(false);
// AI生成状态控制
const isGenerating = ref(false);
// 数据加载状态控制
const isDataLoaded = ref(false);
// 进度弹窗相关变量
const progressDialog = ref();
const currentProgress = ref('正在智能识别中...');

// FooterBar按钮配置
const footerButtons = ref([
	{
		text: '保存草稿',
		type: 'draft',
		slotName: 'draft',
		bgColor: '#fff',
		textColor: '#1a73e8',
		border: '1px solid #1a73e8',
		isHidden: lawyer.value
	},
	{
		text: '风险智能识别',
		type: 'ai',
		slotName: 'ai',
		bgColor: '#fff',
		textColor: '#1a73e8',
		border: '1px solid #1a73e8'
	},
	{
		text: lawyer.value ? '发布' : '提交审核',
		type: 'submit',
		slotName: 'submit',
		bgColor: '#1a73e8',
		textColor: '#fff'
	}
]);
const form = ref(null);
const tags = ref([{
	name: '高危',
	value: 'HIGH'
}, {
	name: '中危',
	value: 'MEDIUM'
}, {
	name: '低危',
	value: 'LOW'
},
{
	name: '未知',
	value: 'UNKNOWN'
}
]);
const checkboxStyle = {
	marginRight: '16rpx', // 左右间距
	marginBottom: '20rpx', // 上下间距
	padding: '8rpx 12rpx', // 内边距
	borderRadius: '8rpx', // 圆角
	fontSize: '28rpx' // 字体大小
}
const riskForm = ref({
	orgUnitId: '', //部门id
	businessType: '', //业务类型
	riskLevelModel: 'UNKNOWN', //风险等级
	riskDescription: '', //风险描述
	riskCause: '', //风险原因
	riskConsequence: '', //风险后果
	controlMeasures: '', //风险管控措施
	lawsRegulations: '', //法律规定
	regulatoryRequirements: '', //监管规定
	rulesRegulations: '', //规章制度
	responsibleOrgUnitId: [], //归口部门
	cooperatingOrgUnitId: [], //配合部门
	metadata: '', //拓展信息
	approvalStatus: 'DRAFT',
})
const orgColumns = ref([
	[...userStore.dict.list[4].list]
]) //部门
const businessColumns = ref([]) // 业务类型
const rules = ref({
	orgUnitId: [{
		required: true,
		validator: (rule, value, callback) => {
			if (value === '' || value === null || value === undefined) {
				callback(new Error('请选择部门'));
			} else {
				callback();
			}
		},
		trigger: 'change'
	}],
	businessType: [{
		required: true,
		validator: (rule, value, callback) => {
			if (value === '' || value === null || value === undefined) {
				callback(new Error('请选择业务类型'));
			} else {
				callback();
			}
		},
		trigger: 'change'
	}],
	riskLevelModel: [{
		required: true,
		message: '请选择风险等级',
		trigger: 'change'
	}],
	riskDescription: [{
		required: true,
		message: '请输入风险描述',
		trigger: 'blur'
	}],
	riskCause: [{
		required: true,
		message: '请输入风险原因',
		trigger: 'blur'
	}],
	riskConsequence: [{
		required: true,
		message: '请输入风险后果',
		trigger: 'blur'
	}],
	controlMeasures: [{
		required: true,
		message: '请输入风险管控措施',
		trigger: 'blur'
	}],
	responsibleOrgUnitId: [{
		required: true,
		validator: (rule, value, callback) => {
			if (!Array.isArray(value) || value.length === 0) {
				callback(new Error('请选择归口部门'));
			} else {
				callback();
			}
		},
		trigger: 'change'
	}],
	cooperatingOrgUnitId: [{
		required: true,
		validator: (rule, value, callback) => {
			if (!Array.isArray(value) || value.length === 0) {
				callback(new Error('请选择配合部门'));
			} else {
				callback();
			}
		},
		trigger: 'change'
	}],
})
const isAdd = ref('')
const detailId = ref('')

const conversationId = ref(null)
onLoad(async (options) => {
	const aiID = await threeAI.getSessionId()
	const data = await getDictData('88', '')
	businessColumns.value = [data]
	conversationId.value = aiID.conversationId
	isAdd.value = options.type
	if (options.type === 'detail' && options.id) {
		detailId.value = options.id
		loadDetailData(options.id)
	} else {
		// 非详情页面直接设置数据已加载
		isDataLoaded.value = true
	}
})

// 加载详情数据
function loadDetailData(id) {
	uni.showLoading({
		title: '加载中...'
	})

	homeApi.complianceRiskMains({
		id: id
	}, 'info').then(res => {
		uni.hideLoading()
		if (res) {
			// 将详情数据填充到表单中
			riskForm.value = {
				approvalStatus: res.approvalStatus,
				orgUnitId: res.orgUnitId || '',
				businessType: res.businessType || '',
				riskLevelModel: res.riskLevelModel,
				riskDescription: res.riskDescription || '',
				riskCause: res.riskCause || '',
				riskConsequence: res.riskConsequence || '',
				controlMeasures: res.controlMeasures || '',
				lawsRegulations: res.lawsRegulations || '',
				regulatoryRequirements: res.regulatoryRequirements || '',
				rulesRegulations: res.rulesRegulations || '',
				responsibleOrgUnitId: Array.isArray(res.responsibleOrgUnitId) ? res.responsibleOrgUnitId :
					(res.responsibleOrgUnitId ? [res.responsibleOrgUnitId] : []),
				cooperatingOrgUnitId: Array.isArray(res.cooperatingOrgUnitId) ? res.cooperatingOrgUnitId :
					(res.cooperatingOrgUnitId ? [res.cooperatingOrgUnitId] : []),
				metadata: res.metadata || ''
			}
		}
		// 数据加载完成
		isDataLoaded.value = true
	}).catch(err => {
		uni.hideLoading()
		console.error('加载详情失败:', err)
		uni.showToast({
			title: '加载详情失败',
			icon: 'none',
			duration: 2000
		})
		// 即使加载失败也设置数据已加载，避免界面一直不显示
		isDataLoaded.value = true
	})
}

// 通用提交函数，减少代码冗余
function submitRiskData(type = 'submit') {
	uni.showLoading()

	// 根据类型设置不同的审批状态
	let approvalStatus
	let successMessage
	let apiMethod

	switch (type) {
		case 'draft':
			approvalStatus = 'DRAFT'
			successMessage = '草稿保存成功'
			// 如果存在详情ID，调用更新接口，否则调用草稿接口
			if (detailId.value) {
				apiMethod = homeApi.complianceRiskMains
			} else {
				apiMethod = addForm.riskDraft
			}
			break
		case 'submit':
			approvalStatus = 'PENDING'
			successMessage = '提交成功'
			apiMethod = addForm.riskSubmit
			break
		case 'update':
			approvalStatus = 'APPROVED'
			successMessage = '更新成功'
			apiMethod = homeApi.complianceRiskMains
			break
		default:
			approvalStatus = 'PENDING'
			successMessage = '操作成功'
			apiMethod = addForm.riskSubmit
	}

	// 构建提交数据
	const submitData = {
		...riskForm.value,
		approvalStatus: approvalStatus,
		complianceRiskMainId: detailId.value ? +detailId.value : null
	}

	// 如果是更新操作或草稿保存且有详情ID，添加ID
	if (type === 'update' || (type === 'draft' && detailId.value)) {
		submitData.complianceRiskMainId = detailId.value
	}

	// 调用对应的API
	const apiKey = (type === 'update' || (type === 'draft' && detailId.value)) ? 'update' : 'info'
	apiMethod(submitData, apiKey).then(res => {
		// 草稿保存成功后，将返回的complianceRiskMainId赋值给detailId
		if (type === 'draft' && res) {
			detailId.value = res.complianceRiskMainId
		}

		uni.showToast({
			title: successMessage,
			icon: 'success',
			duration: 2000
		})
		setTimeout(() => {
			if (type !== 'draft') {
				uni.navigateBack({
					delta: 1
				})
			}
			// 成功后重置提交状态
			isSubmitting.value = false
		}, 800)
		// 草稿保存不返回上一页
	}).catch(err => {
		console.error('提交失败:', err)
		uni.showToast({
			title: type === 'draft' ? '保存失败，请重试' : '提交失败，请重试',
			icon: 'none',
			duration: 2000
		})
		// 失败时立即重置提交状态
		isSubmitting.value = false
	}).finally(() => {
		uni.hideLoading()
	})
}

// 新增风险
function addRisk() {
	submitRiskData('submit')
}

// 更新风险
function updateRisk() {
	submitRiskData('update')
}

// 保存草稿
function saveDraftRisk() {
	submitRiskData('draft')
}

// 防抖定时器
let debounceTimer = null;

// FooterBar点击事件处理
const handleFooterClick = (btn) => {
	console.log('FooterBar clicked:', btn.type);

	// 对所有按钮添加2秒防抖
	if (debounceTimer) {
		uni.showToast({
			title: '操作过于频繁，请稍后再试',
			icon: 'none',
			duration: 1500
		});
		return;
	}

	// 设置防抖定时器
	debounceTimer = setTimeout(() => {
		debounceTimer = null;
	}, 2000);

	switch (btn.type) {
		case 'draft':
			// 保存草稿逻辑
			saveDraft();
			break;
		case 'ai':
			// 风险智能识别逻辑
			handleAIRecognition();
			break;
		case 'submit':
			// 提交审核逻辑（防重复点击）
			if (!isSubmitting.value) {
				handleClick();
			}
			break;
		default:
			break;
	}
};

// 保存草稿方法
const saveDraft = () => {
	// 防重复点击
	if (isSubmitting.value) {
		return;
	}
	isSubmitting.value = true;
	saveDraftRisk();
};



// AI智能识别方法
const handleAIRecognition = async () => {
	// 防重复点击判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!riskForm.value.businessType) {
		uni.showToast({
			title: '请先选择业务类型',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	try {
		// 设置生成状态
		isGenerating.value = true;

		// 显示进度弹窗
		progressDialog.value.open();
		currentProgress.value = '正在智能识别中...';

		// 调用AI接口
		const businessTypeName = businessColumns.value[0].find(item => item.value === riskForm.value.businessType)?.name || '';
		const params = {
			businessTypeName: businessTypeName,
			toolKey: 'kimi',
			orgUnitName: getDepartments.find(item => item.id === riskForm.value.orgUnitId)?.name || '',
			riskDescription: riskForm.value.riskDescription || '',
			riskCause: riskForm.value.riskCause || '',
			riskConsequence: riskForm.value.riskConsequence || '',
			conversationId: conversationId.value
		};

		// 更新进度文本
		currentProgress.value = '正在分析业务类型...';

		currentProgress.value = '正在生成风险信息...';
		const result = await threeAI.getRiskAI(params);

		if (result) {

			// 填充表单数据
			if (result.riskDescription) {
				riskForm.value.riskDescription = result.riskDescription;
			}
			if (result.riskCause) {
				riskForm.value.riskCause = result.riskCause;
			}
			if (result.riskConsequence) {
				riskForm.value.riskConsequence = result.riskConsequence;
			}
			if (result.controlMeasures) {
				riskForm.value.controlMeasures = result.controlMeasures;
			}
			if (result.lawsRegulations) {
				riskForm.value.lawsRegulations = result.lawsRegulations;
			}
			if (result.regulatoryRequirements) {
				riskForm.value.regulatoryRequirements = result.regulatoryRequirements;
			}
			if (result.rulesRegulations) {
				riskForm.value.rulesRegulations = result.rulesRegulations;
			}
			if (result.responsibleOrgUnitId) {
				riskForm.value.responsibleOrgUnitId = Array.isArray(result.responsibleOrgUnitId) ? result.responsibleOrgUnitId : [result.responsibleOrgUnitId];
			}
			if (result.cooperatingOrgUnitId) {
				riskForm.value.cooperatingOrgUnitId = Array.isArray(result.cooperatingOrgUnitId) ? result.cooperatingOrgUnitId : [result.cooperatingOrgUnitId];
			}
			if (result.riskLevelModel) {
				riskForm.value.riskLevelModel = result.riskLevelModel;
			}

			// 延迟关闭弹窗
			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({
					title: '智能识别完成',
					icon: 'success'
				});
			}, 1000);
		} else {
			progressDialog.value.close();
			uni.showToast({
				title: '识别失败，请重试',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('AI识别错误:', error);
		uni.showToast({
			title: 'AI识别失败，请重试',
			icon: 'none'
		});
		progressDialog.value.close();
		isGenerating.value = false;
	}
};

// 风险信息智能生成方法
const handleRiskTypeGenerate = async () => {
	// 防重复点击判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!riskForm.value.businessType) {
		uni.showToast({
			title: '请先选择业务类型',
			icon: 'none'
		});
		return;
	}



	// 设置生成状态
	isGenerating.value = true;

	// 显示进度弹窗
	progressDialog.value.open();
	currentProgress.value = '正在生成风险信息...';

	try {
		const businessTypeName = businessColumns.value[0].find(item => item.value === riskForm.value.businessType)?.name || '';
		const params = {
			businessType: riskForm.value.businessType,
			businessTypeName: businessTypeName,
			toolKey: 'kimi',
			orgUnitName: getDepartments.find(item => item.id === riskForm.value.orgUnitId)?.name || '',
			conversationId: conversationId.value
		};

		const result = await threeAI.getRiskTypeAI(params);

		if (result) {
			// 填充风险信息相关字段
			if (result.riskDescription) {
				riskForm.value.riskDescription = result.riskDescription;
			}
			if (result.riskCause) {
				riskForm.value.riskCause = result.riskCause;
			}
			if (result.riskConsequence) {
				riskForm.value.riskConsequence = result.riskConsequence;
			}

			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({
					title: '风险信息生成成功',
					icon: 'success'
				});
			}, 800);
		} else {
			progressDialog.value.close();
			isGenerating.value = false;
			uni.showToast({
				title: '生成失败，请重试',
				icon: 'none'
			});
		}
	} catch (error) {
		progressDialog.value.close();
		isGenerating.value = false;
		console.error('风险信息生成错误:', error);
		uni.showToast({
			title: '生成失败，请重试',
			icon: 'none'
		});
	}
};

// 合规义务智能生成方法
const handleRiskLawGenerate = async () => {
	// 防重复点击判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!riskForm.value.businessType) {
		uni.showToast({
			title: '请先选择业务类型',
			icon: 'none'
		});
		return;
	}


	// 设置生成状态
	isGenerating.value = true;

	// 显示进度弹窗
	progressDialog.value.open();
	currentProgress.value = '正在生成合规义务...';

	try {
		const businessTypeName = businessColumns.value[0].find(item => item.value === riskForm.value.businessType)?.name || '';
		const params = {
			businessType: riskForm.value.businessType,
			businessTypeName: businessTypeName,
			toolKey: 'kimi',
			orgUnitName: getDepartments.find(item => item.id === riskForm.value.orgUnitId)?.name || '',
			riskDescription: riskForm.value.riskDescription || '',
			riskCause: riskForm.value.riskCause || '',
			riskConsequence: riskForm.value.riskConsequence || '',
			conversationId: conversationId.value
		};

		const result = await threeAI.getRiskLawAI(params);

		if (result) {
			// 填充合规义务相关字段
			if (result.lawsRegulations) {
				riskForm.value.lawsRegulations = result.lawsRegulations;
			}
			if (result.regulatoryRequirements) {
				riskForm.value.regulatoryRequirements = result.regulatoryRequirements;
			}
			if (result.rulesRegulations) {
				riskForm.value.rulesRegulations = result.rulesRegulations;
			}

			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({
					title: '合规义务生成成功',
					icon: 'success'
				});
			}, 800);
		} else {
			progressDialog.value.close();
			isGenerating.value = false;
			uni.showToast({
				title: '生成失败，请重试',
				icon: 'none'
			});
		}
	} catch (error) {
		progressDialog.value.close();
		isGenerating.value = false;
		console.error('合规义务生成错误:', error);
		uni.showToast({
			title: '生成失败，请重试',
			icon: 'none'
		});
	}
};

// 管理信息智能生成方法
const handleRiskControlGenerate = async () => {
	// 防重复点击判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!riskForm.value.businessType) {
		uni.showToast({
			title: '请先选择业务类型',
			icon: 'none'
		});
		return;
	}


	// 设置生成状态
	isGenerating.value = true;

	// 显示进度弹窗
	progressDialog.value.open();
	currentProgress.value = '正在生成管理信息...';

	try {
		const businessTypeName = businessColumns.value[0].find(item => item.value === riskForm.value.businessType)?.name || '';
		const params = {
			businessType: riskForm.value.businessType,
			businessTypeName: businessTypeName,
			toolKey: 'kimi',
			orgUnitName: getDepartments.find(item => item.id === riskForm.value.orgUnitId)?.name || '',
			riskDescription: riskForm.value.riskDescription || '',
			riskCause: riskForm.value.riskCause || '',
			riskConsequence: riskForm.value.riskConsequence || '',
			lawsRegulations: riskForm.value.lawsRegulations || '',
			regulatoryRequirements: riskForm.value.regulatoryRequirements || '',
			rulesRegulations: riskForm.value.rulesRegulations || '',
			conversationId: conversationId.value
		};

		const result = await threeAI.getRiskControlAI(params);

		if (result) {
			// 填充管理信息相关字段
			if (result.controlMeasures) {
				riskForm.value.controlMeasures = result.controlMeasures;
			}
			if (result.responsibleOrgUnitId) {
				riskForm.value.responsibleOrgUnitId = Array.isArray(result.responsibleOrgUnitId) ? result.responsibleOrgUnitId : [result.responsibleOrgUnitId];
			}
			if (result.cooperatingOrgUnitId) {
				riskForm.value.cooperatingOrgUnitId = Array.isArray(result.cooperatingOrgUnitId) ? result.cooperatingOrgUnitId : [result.cooperatingOrgUnitId];
			}
			if (result.riskLevelModel) {
				riskForm.value.riskLevelModel = result.riskLevelModel;
			}

			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({
					title: '管理信息生成成功',
					icon: 'success'
				});
			}, 800);
		} else {
			progressDialog.value.close();
			isGenerating.value = false;
			uni.showToast({
				title: '生成失败，请重试',
				icon: 'none'
			});
		}
	} catch (error) {
		progressDialog.value.close();
		isGenerating.value = false;
		console.error('管理信息生成错误:', error);
		uni.showToast({
			title: '生成失败，请重试',
			icon: 'none'
		});
	}
};

// 风险等级智能生成方法
const handleRiskLevelGenerate = async () => {
	// 防重复点击判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!riskForm.value.businessType) {
		uni.showToast({
			title: '请先选择业务类型',
			icon: 'none'
		});
		return;
	}
	//部门判断
	if (!riskForm.value.orgUnitId) {
		uni.showToast({
			title: '请先选择部门',
			icon: 'none'
		});
		return;
	}


	// 设置生成状态
	isGenerating.value = true;

	// 显示进度弹窗
	progressDialog.value.open();
	currentProgress.value = '正在生成风险等级...';

	try {
		const businessTypeName = businessColumns.value[0].find(item => item.value === riskForm.value.businessType)?.name || '';
		const params = {
			businessType: riskForm.value.businessType,
			businessTypeName: businessTypeName,
			toolKey: 'kimi',
			orgUnitName: getDepartments.find(item => item.id === riskForm.value.orgUnitId)?.name || '',
			riskDescription: riskForm.value.riskDescription || '',
			riskCause: riskForm.value.riskCause || '',
			riskConsequence: riskForm.value.riskConsequence || '',
			lawsRegulations: riskForm.value.lawsRegulations || '',
			regulatoryRequirements: riskForm.value.regulatoryRequirements || '',
			rulesRegulations: riskForm.value.rulesRegulations || '',
			controlMeasures: riskForm.value.controlMeasures || '',
			conversationId: conversationId.value
		};

		// 调用风险等级生成接口（这里使用现有的AI接口，主要关注风险等级字段）
		const result = await threeAI.getRiskRankAI(params);

		if (result && result.riskLevelModel) {
			// 只填充风险等级字段
			riskForm.value.riskLevelModel = result.riskLevelModel;

			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({
					title: '风险等级生成成功',
					icon: 'success'
				});
			}, 800);
		} else {
			progressDialog.value.close();
			isGenerating.value = false;
			uni.showToast({
				title: '生成失败，请重试',
				icon: 'none'
			});
		}
	} catch (error) {
		progressDialog.value.close();
		isGenerating.value = false;
		console.error('风险等级生成错误:', error);
		uni.showToast({
			title: '生成失败，请重试',
			icon: 'none'
		});
	}
};



function handleClick() {
	form.value.validate().then(valid => {
		if (!valid) {
			return;
		}
		// 设置提交状态为true，防止重复点击
		isSubmitting.value = true;
		console.log('form', form.value.model)
		// 根据页面类型判断是新增还是更新
		if (lawyer.value) {
			updateRisk()
		} else {
			addRisk()
		}
	}).catch(() => {
		// 验证失败时重置提交状态
		isSubmitting.value = false;
		uni.showToast({
			title: '请填写必填项',
			icon: 'none'
		});
	});
}

// 获取风险等级显示文本
const getRiskLevelText = (level) => {
	const levelMap = {
		'HIGH': '高',
		'MEDIUM': '中',
		'LOW': '低',
		'UNKNOWN': '未知'
	};
	return levelMap[level] || '未知';
};

// 获取风险等级样式类名
const getRiskLevelClass = (level) => {
	const classMap = {
		'HIGH': 'risk-level-high',
		'MEDIUM': 'risk-level-medium',
		'LOW': 'risk-level-low',
		'UNKNOWN': 'risk-level-unknown'
	};
	return classMap[level] || 'risk-level-unknown';
};

</script>

<style lang="scss" scoped>

.transform-table__form {
	margin-bottom: 40rpx;
}

.textarea-container {
	position: relative;
}

/* 复选框容器样式 */
.checkbox-container {
	width: 100%;
}

.checkbox-wrapper {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
}

.checkbox-wrapper :deep(.uv-checkbox) {
	width: calc(33.33% - 16rpx);
	box-sizing: border-box;
	margin-right: 16rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
}

.checkbox-wrapper :deep(.uv-checkbox__label) {
	font-size: 28rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 180rpx;
}

/* 风险等级显示样式 */
.risk-level-display {
	display: inline-block;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	text-align: center;
	min-width: 60rpx;
}

/* 高风险 - 红色 */
.risk-level-high {
	background-color: #fff2f0;
	color: #ff4d4f;
	border: 1px solid #ffccc7;
}

/* 中风险 - 橙色 */
.risk-level-medium {
	background-color: #fff7e6;
	color: #fa8c16;
	border: 1px solid #ffd591;
}

/* 低风险 - 绿色 */
.risk-level-low {
	background-color: #f6ffed;
	color: #52c41a;
	border: 1px solid #b7eb8f;
}

/* 未知风险 - 灰色 */
.risk-level-unknown {
	background-color: #f5f5f5;
	color: #8c8c8c;
	border: 1px solid #d9d9d9;
}



/* 弹窗样式 */
.generate-popup {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 40rpx;
	width: 600rpx;
	max-width: 80vw;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	text-align: center;
	margin-bottom: 40rpx;
	display: block;
}

.dialog-content {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 40rpx;
	width: 80vw;
	max-width: 600rpx;
	box-sizing: border-box;
}

.dialog-title {
	display: block;
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	text-align: center;
	margin-bottom: 30rpx;
}

.progress-body {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 0;
}

.progress-text {
	font-size: 28rpx;
	color: #666;
	margin-top: 30rpx;
}

.pulse {
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.5;
	}

	100% {
		opacity: 1;
	}
}
</style>
