
<template>
    <view class="assessmentNotice-container">
      <!-- 顶部导航栏 -->
      <!-- <view class="nav-bar">
        <view class="nav-left">
          <view class="avatar">
            <uni-icons type="person" size="16" color="#fff"></uni-icons>
          </view>
          <text class="greeting">早上好，陈经理</text>
        </view>
        <view class="nav-right">
          <view class="nav-icon">
            <uni-icons type="search" size="16" color="#666"></uni-icons>
          </view>
          <view class="nav-icon">
            <uni-icons type="notification" size="16" color="#666"></uni-icons>
            <view class="badge">3</view>
          </view>
        </view>
      </view> -->
  
      <!-- 主要内容区域 -->
      <scroll-view class="main-content" scroll-y>
        <!-- 企业公告 Banner -->
        <view class="announcement">
          <view class="announcement-left">
            <view class="announcement-icon">
              <uni-icons type="bullhorn" size="12" color="#f59e0b"></uni-icons>
            </view>
            <text class="announcement-text">关于2023年第四季度合规考核结果的通知</text>
          </view>
          <uni-icons type="arrowright" size="12" color="#999"></uni-icons>
        </view>
  
        <!-- 合规概览卡片区 -->
        <view class="overview-cards">
          <!-- 得分卡 -->
          <view class="card score-card">
            <text class="score">86</text>
            <text class="label">合规得分</text>
          </view>
          
          <!-- 趋势卡 -->
          <view class="card trend-card">
            <view class="chart-container">
              <canvas canvas-id="trendChart" class="chart"></canvas>
            </view>
            <text class="label">近7天趋势</text>
          </view>
          
          <!-- 排名卡 -->
          <view class="card rank-card">
            <text class="score rank">3/10</text>
            <text class="label">本部门排名</text>
          </view>
        </view>
  
        <!-- 核心功能网格 -->
        <view class="core-functions">
          <text class="section-title">核心功能</text>
          <view class="function-grid">
            <!-- 合规制度库 -->
            <view class="function-item">
              <view class="function-icon">
                <uni-icons type="wallet-filled" size="16" color="#1A73E8"></uni-icons>
              </view>
              <text class="function-name">合规制度库</text>
            </view>
            
            <!-- 本岗位职责库 -->
            <view class="function-item">
              <view class="function-icon">
                <uni-icons type="person" size="16" color="#34A853"></uni-icons>
              </view>
              <text class="function-name">本岗位职责库</text>
            </view>
            
            <!-- 三张清单 -->
            <view class="function-item">
              <view class="function-icon">
                <uni-icons type="list" size="16" color="#8B5CF6"></uni-icons>
              </view>
              <text class="function-name">三张清单</text>
            </view>
            
            <!-- 合规评分 -->
            <view class="function-item">
              <view class="function-icon">
                <uni-icons type="star" size="16" color="#F59E0B"></uni-icons>
              </view>
              <text class="function-name">合规评分</text>
            </view>
            
            <!-- 合规驾驶舱 -->
            <view class="function-item">
              <view class="function-icon">
                <uni-icons type="smallcircle" size="16" color="#EF4444"></uni-icons>
              </view>
              <text class="function-name">合规驾驶舱</text>
            </view>
            
            <!-- 学习计划 -->
            <view class="function-item">
              <view class="function-icon">
                <uni-icons type="compose" size="16" color="#6366F1"></uni-icons>
              </view>
              <text class="function-name">学习计划</text>
            </view>
          </view>
        </view>
  
        <!-- 快捷操作区 -->
        <view class="quick-actions">
          <text class="section-title">快捷操作</text>
          <view class="action-buttons">
            <view class="action-item">
              <view class="action-icon">
                <uni-icons type="search" size="20" color="#1A73E8"></uni-icons>
              </view>
              <text class="action-name">发起审查</text>
            </view>
            
            <view class="action-item">
              <view class="action-icon">
                <uni-icons type="flag" size="20" color="#EF4444"></uni-icons>
              </view>
              <text class="action-name">提交举报</text>
            </view>
            
            <view class="action-item">
              <view class="action-icon">
                <uni-icons type="chat" size="20" color="#34A853"></uni-icons>
              </view>
              <text class="action-name">AI 问答</text>
            </view>
            
            <view class="action-item">
              <view class="action-icon">
                <uni-icons type="more" size="20" color="#666"></uni-icons>
              </view>
              <text class="action-name">更多</text>
            </view>
          </view>
        </view>
      </scroll-view>
  
      <goNavitor url="/pages/component/homeSon/compliancePostForm" />
    </view>
  </template>
  
  <script setup>
  import { onMounted } from 'vue';
  import { onReady } from '@dcloudio/uni-app';
  import goNavitor from '@/components/goNavitor.vue';
  
  onReady(() => {
    // 初始化趋势图表
    const trendChart = uni.createCanvasContext('trendChart');
    
    // 绘制折线图
    trendChart.setStrokeStyle('#1A73E8');
    trendChart.setLineWidth(2);
    trendChart.beginPath();
    
    // 模拟数据点
    const data = [82, 83, 84, 85, 84, 85, 86];
    const width = 80;
    const height = 40;
    const padding = 10;
    
    data.forEach((value, index) => {
      const x = padding + (index * (width - 2 * padding)) / (data.length - 1);
      const y = height - padding - ((value - 80) / 6) * (height - 2 * padding);
      
      if (index === 0) {
        trendChart.moveTo(x, y);
      } else {
        trendChart.lineTo(x, y);
      }
      
      // 绘制数据点
      trendChart.setFillStyle('#1A73E8');
      trendChart.beginPath();
      trendChart.arc(x, y, 2, 0, 2 * Math.PI);
      trendChart.fill();
    });
    
    // 绘制区域填充
    trendChart.setFillStyle('rgba(26, 115, 232, 0.2)');
    trendChart.lineTo(width - padding, height - padding);
    trendChart.lineTo(padding, height - padding);
    trendChart.closePath();
    trendChart.fill();
    
    trendChart.stroke();
    trendChart.draw();
  });
  </script>
  
  <style lang="scss" scoped>
  .assessmentNotice-container{
    display: flex;
    flex-direction: column;
    // height: 100%;
    background-color: #F8F9FA;
  }
  
  /* 主要内容区域样式 */
  .main-content {
    flex: 1;
    margin-top: 88rpx;
    margin-bottom: 88rpx;
    overflow: auto;
  }
  
  /* 公告样式 */
  .announcement {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    margin: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  }
  
  .announcement-left {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }
  
  .announcement-icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    background-color: #FEF3C7;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .announcement-text {
    font-size: 28rpx;
    color: #333;
    margin-left: 16rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
  
  /* 概览卡片样式 */
  .overview-cards {
    display: flex;
    justify-content: space-between;
    padding: 0 24rpx;
    margin-top: 24rpx;
  }
  
  .card {
    width: 30%;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .score {
    font-size: 64rpx;
    font-weight: bold;
    color: #1A73E8;
  }
  
  .rank {
    font-size: 64rpx;
    font-weight: bold;
    color: #333;
  }
  
  .label {
    font-size: 24rpx;
    color: #666;
    margin-top: 8rpx;
  }
  
  .chart-container {
    width: 100%;
    height: 80rpx;
  }
  
  .chart {
    width: 100%;
    height: 100%;
  }
  
  /* 核心功能样式 */
  .core-functions {
    padding: 24rpx;
    margin-top: 40rpx;
  }
  
  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .function-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;
  }
  
  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  }
  
  .function-icon {
    width: 64rpx;
    height: 64rpx;
    border-radius: 16rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .function-name {
    font-size: 24rpx;
    color: #333;
    margin-top: 16rpx;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
  
  /* 快捷操作样式 */
  .quick-actions {
    padding: 24rpx;
    margin-top: 40rpx;
  }
  
  .action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
  }
  
  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .action-icon {
    width: 112rpx;
    height: 112rpx;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .action-name {
    font-size: 24rpx;
    color: #333;
    margin-top: 16rpx;
  }
  
  </style>
  
  