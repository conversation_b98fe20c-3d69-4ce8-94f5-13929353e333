<template>
  <view class="wrong-page">
    <!-- 内容区域 -->
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <uni-load-more status="loading" :content-text="{contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载中...'}"></uni-load-more>
      </view>
      <view v-else-if="wrong" class="list-container"> 
      </view>
      <!-- 空状态 -->
      <view v-else-if="wrongQuestions.length === 0 && !loading" class="empty-container">
        <view class="empty-text">恭喜！没有错题</view>
        <view class="empty-desc">您在本次考试中全部答对了</view>
      </view>
      
      <!-- 错题列表 -->
      <view v-else class="question-list">
        <view v-for="(question, index) in wrongQuestions" :key="question.questionId" class="question-card">
          <!-- 题目头部 -->
          <view class="question-header">
            <view class="question-number">第{{ question.questionOrder }}题</view>
            <view class="question-difficulty" :class="getDifficultyClass(question.difficulty)">
              {{ question.difficulty || '普通' }}
            </view>
          </view>
          
          <!-- 题目内容 -->
          <view class="question-content">
            <rich-text :nodes="question.questionContent"></rich-text>
          </view>
          
          <!-- 选项 -->
          <view class="options-container">
            <view v-if="question.optionA" class="option-item" :class="getOptionClass('A', question)">
              <text class="option-label">A.</text>
              <text class="option-text">{{ question.optionA }}</text>
            </view>
            <view v-if="question.optionB" class="option-item" :class="getOptionClass('B', question)">
              <text class="option-label">B.</text>
              <text class="option-text">{{ question.optionB }}</text>
            </view>
            <view v-if="question.optionC" class="option-item" :class="getOptionClass('C', question)">
              <text class="option-label">C.</text>
              <text class="option-text">{{ question.optionC }}</text>
            </view>
            <view v-if="question.optionD" class="option-item" :class="getOptionClass('D', question)">
              <text class="option-label">D.</text>
              <text class="option-text">{{ question.optionD }}</text>
            </view>
          </view>
          
          <!-- 答案对比 -->
          <view class="answer-comparison">
            <view class="answer-item">
              <text class="answer-label">您的答案：</text>
              <text class="user-answer">{{ question.userAnswer || '未作答' }}</text>
            </view>
            <view class="answer-item">
              <text class="answer-label">正确答案：</text>
              <text class="correct-answer">{{ question.correctAnswer }}</text>
            </view>
          </view>
          
          <!-- 题目解析 -->
          <view v-if="question.questionAnalysis" class="question-analysis">
            <view class="analysis-title">
              <uni-icons type="info" size="16" color="#1890ff"></uni-icons>
              <text class="analysis-label">题目解析</text>
            </view>
            <view class="analysis-content">
              <rich-text :nodes="question.questionAnalysis"></rich-text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import assessApi from './assess.js';

// 页面参数
const examRecordId = ref('');
//错题
const wrong = ref(false);
// 错题数据
const wrongQuestions = ref([]);
const loading = ref(false);

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 获取难度等级样式
const getDifficultyClass = (difficulty) => {
  switch (difficulty) {
    case '简单': return 'difficulty-easy';
    case '中等': return 'difficulty-medium';
    case '困难': return 'difficulty-hard';
    default: return 'difficulty-medium';
  }
};

// 获取选项样式
const getOptionClass = (option, question) => {
  const isUserAnswer = question.userAnswer && question.userAnswer.includes(option);
  const isCorrectAnswer = question.correctAnswer && question.correctAnswer.includes(option);
  
  if (isCorrectAnswer) {
    return 'option-correct';
  } else if (isUserAnswer) {
    return 'option-wrong';
  }
  return '';
};

// 获取错题解析
const loadWrongAnalysis = async () => {
  if (!examRecordId.value) {
    uni.showToast({
      title: '缺少考试记录ID',
      icon: 'none'
    });
    return;
  }
  
  loading.value = true;
  
  try {
    const response = await assessApi.wrongAnalysis(examRecordId.value);
    wrong.value = false
    if (response && Array.isArray(response)) {
      wrongQuestions.value = response;
      console.log('错题解析数据:', response);
    } else {
      wrongQuestions.value = [];
    }
  } catch (error) {
    console.error('获取错题解析失败:', error);
    wrong.value = true
    uni.showToast({
      title: '获取错题解析失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options;
  
  if (options.examRecordId) {
    examRecordId.value = options.examRecordId;
    // 加载错题解析
    loadWrongAnalysis();
  } else {
    uni.showToast({
      title: '缺少考试记录ID',
      icon: 'none'
    });
  }
});
</script>

<style>
.wrong-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 32rpx;
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 36rpx;
  color: #52c41a;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

/* 题目列表 */
.question-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.question-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 题目头部 */
.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.question-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.question-difficulty {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.difficulty-easy {
  background-color: #f6ffed;
  color: #52c41a;
}

.difficulty-medium {
  background-color: #fff7e6;
  color: #fa8c16;
}

.difficulty-hard {
  background-color: #fff2f0;
  color: #f5222d;
}

/* 题目内容 */
.question-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

/* 选项样式 */
.options-container {
  margin-bottom: 24rpx;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
  border: 2rpx solid #f0f0f0;
}

.option-label {
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 12rpx;
  color: #666;
  flex-shrink: 0;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.option-correct {
  background-color: #f6ffed;
  border-color: #52c41a;
}

.option-correct .option-label,
.option-correct .option-text {
  color: #52c41a;
  font-weight: 600;
}

.option-wrong {
  background-color: #fff2f0;
  border-color: #f5222d;
}

.option-wrong .option-label,
.option-wrong .option-text {
  color: #f5222d;
  font-weight: 600;
}

/* 答案对比 */
.answer-comparison {
  background-color: #fafafa;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.answer-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.answer-item:last-child {
  margin-bottom: 0;
}

.answer-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
  min-width: 160rpx;
}

.user-answer {
  font-size: 28rpx;
  color: #f5222d;
  font-weight: 600;
}

.correct-answer {
  font-size: 28rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 题目解析 */
.question-analysis {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.analysis-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.analysis-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1890ff;
  margin-left: 8rpx;
}

.analysis-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
}
</style>