<template>
	<view class="hot-questions-container common-questions-container">
		<!-- 热门推荐列表 -->
		<view class="hot-questions common-questions">
			<text class="section-title">热门推荐</text>
			<view v-if="hotQuestions.length > 0" class="question-list">
				<view 
					class="question-item" 
					v-for="(item, index) in hotQuestions" 
					:key="item.id || index"
					@click="handleQuestionClick(item)"
				>
					<view class="question-content">
						<text class="question-text" style="margin-left: 0;">{{index+1}}.</text>
						<text class="question-text">{{item.questionTitle}}</text>
					</view>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view v-else-if="loading" class="loading">
				<text class="loading-text">加载中...</text>
			</view>
			<view v-else class="empty-questions">
				<text class="empty-text">暂无热门推荐</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import qaApi from '@/api/chatAI/qa.js'

// 响应式数据
const hotQuestions = ref([])
const loading = ref(false)

// 获取热门推荐列表
const fetchHotQuestions = async () => {
	loading.value = true
	try {
		const response = await qaApi.queryHotQuestions()
		if (response && Array.isArray(response)) {
			// 全部展示，不截取
			hotQuestions.value = response
		} else {
			hotQuestions.value = []
		}
	} catch (error) {
		console.error('获取热门推荐失败:', error)
		hotQuestions.value = []
		uni.showToast({
			title: '获取热门推荐失败',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 点击问题项
const handleQuestionClick = (question) => {
	uni.navigateTo({
		url: '/pages/component/qa/hotQuestions',
		events: {
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			acceptDataFromOpenedPage: function(data) {
				console.log('接收到数据:', data)
			}
		},
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据
			res.eventChannel.emit('acceptDataFromOpenerPage', { question })
		}
	})
}

// 页面加载时获取数据
onMounted(() => {
	fetchHotQuestions()
})
</script>

<style lang="scss" scoped>
 @import '/static/css/aiChat.scss'; 
</style>