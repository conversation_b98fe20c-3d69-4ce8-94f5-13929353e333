import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
const baseUrl = `/services/whiskerguardtrainingservice/api/course/infos`

export default {
    // 获取研究列表
    courseList(params) {
        return request(
            `${baseUrl}/query`,
            params, 'POST')
    },
    // 获取研究详情
    courseDetail(id) {
        return request(
            `${baseUrl}/${id}`,
            {}, 'GET')
    },
    // 获取课程内容列表
    getCourseList(params) {
        return request(
            `/services/whiskerguardtrainingservice/api/course/contents/query`,
            params, 'POST')
    },
    // 获取单个课程内容详情
    //获取课程章节详情
    getCourseContentDetail(params) {
        return request(
            `/services/whiskerguardtrainingservice/api/course/chapters/query`,
        params, 'POST')
    },
    // 根据课程id获取课程内容
    getCourseContentByCourseId(courseId, params = {}) {
        return request(
            `/services/whiskerguardtrainingservice/api/course/chapters/course/${courseId}/with/lessons`,
            params, 'GET')
    },
    //更新学习进度
    updateProgress(params) {
        return request(
            `/services/whiskerguardtrainingservice/api/learning/progresses/updateOrCreate`,
            {
                 userId: params.userId,
                 courseId: params.courseId,
                 chapterId: params.chapterId,
                 playbackPosition: params.playbackPosition
            }, 'put')
    },
    // 证书相关
    getCertificateList(params) {
        return request(
            `/services/whiskerguardtrainingservice/api/certificate/managements/query`,
            params, 'POST')
    },
    // 获取证书详情
    getCertificateDetail(id) {
        return request(
            `/services/whiskerguardtrainingservice/api/certificate/managements/${id}`,
            {}, 'GET')
    },
    // 获取用户学习进度概览
    getLearningProgressOverview(params = {}) {
        return request(
            `/services/whiskerguardtrainingservice/api/learning/progress/statistics/overview`,
            params, 'GET')
    },
}