<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>授权处理中</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .callback-container {
            background: white;
            border-radius: 16px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .callback-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1A73E8, #4285F4);
        }
        
        .loading-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            position: relative;
        }
        
        .spinner {
            width: 80px;
            height: 80px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1A73E8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success-icon {
            display: none;
            width: 80px;
            height: 80px;
            background: #28a745;
            border-radius: 50%;
            margin: 0 auto 20px;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
        }
        
        .error-icon {
            display: none;
            width: 80px;
            height: 80px;
            background: #dc3545;
            border-radius: 50%;
            margin: 0 auto 20px;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1A73E8, #4285F4);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #1A73E8, #4285F4);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(26, 115, 232, 0.3);
        }
        
        .close-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
            margin-left: 10px;
        }
        
        .close-btn:hover {
            background: #5a6268;
        }
        
        .button-group {
            margin-top: 20px;
        }
        
        @media (max-width: 480px) {
            .callback-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 20px;
            }
            
            .message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="callback-container">
        <div class="loading-icon">
            <div class="spinner" id="spinner"></div>
        </div>
        <div class="success-icon" id="successIcon">✓</div>
        <div class="error-icon" id="errorIcon">✗</div>
        
        <h1 class="title" id="title">正在处理授权...</h1>
        <p class="message" id="message">请稍候，我们正在验证您的授权信息</p>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="button-group">
            <button class="retry-btn" id="retryBtn" onclick="retryAuth()">重试</button>
            <button class="close-btn" id="closeBtn" onclick="closeWindow()">关闭</button>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getQueryParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }
        
        // 显示成功状态
        function showSuccess(message) {
            document.getElementById('spinner').style.display = 'none';
            document.getElementById('successIcon').style.display = 'flex';
            document.getElementById('title').textContent = '授权成功！';
            document.getElementById('message').textContent = message;
            document.getElementById('closeBtn').style.display = 'inline-block';
            updateProgress(100);
        }
        
        // 显示错误状态
        function showError(message) {
            document.getElementById('spinner').style.display = 'none';
            document.getElementById('errorIcon').style.display = 'flex';
            document.getElementById('title').textContent = '授权失败';
            document.getElementById('message').textContent = message;
            document.getElementById('retryBtn').style.display = 'inline-block';
            document.getElementById('closeBtn').style.display = 'inline-block';
            updateProgress(0);
        }
        
        // 重试授权
        function retryAuth() {
            window.location.href = 'wechat-auth.html';
        }
        
        // 关闭窗口
        function closeWindow() {
            if (window.opener) {
                window.close();
            } else {
                // 如果无法关闭，跳转到主页或其他页面
                window.location.href = '/';
            }
        }
        
        // 处理授权回调
        async function handleAuthCallback() {
            const code = getQueryParam('code');
            const state = getQueryParam('state');
            const error = getQueryParam('error');
            const errorDescription = getQueryParam('error_description');
            
            // 检查是否有错误
            if (error) {
                showError(errorDescription || '用户取消授权或授权失败');
                return;
            }
            
            if (!code) {
                showError('未获取到授权码，请重新授权');
                return;
            }
            
            try {
                // 模拟进度更新
                updateProgress(20);
                
                // 调用后端接口处理授权
                const response = await fetch('https://dev.api.mbbhg.com/services/whiskerguardgeneralservice/api/wechat/binding/oauth/callback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        state: state
                    })
                });
                
                updateProgress(60);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                updateProgress(80);
                
                if (result.success || result.code === 200 || result.status === 'success') {
                    updateProgress(100);
                    showSuccess('您现在可以接收服务号消息推送了！');
                    
                    // 向父页面发送成功消息
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'wechat_auth_success',
                            data: result.data || result
                        }, '*');
                    }
                    
                    // 3秒后自动关闭
                    setTimeout(() => {
                        closeWindow();
                    }, 3000);
                    
                } else {
                    showError(result.message || result.msg || '授权处理失败，请重试');
                }
                
            } catch (error) {
                console.error('处理授权回调失败:', error);
                showError('网络错误或服务器异常，请重试');
            }
        }
        
        // 页面加载完成后开始处理
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否在微信浏览器中
            const isWechat = /micromessenger/i.test(navigator.userAgent);
            if (!isWechat) {
                showError('请在微信中打开此页面');
                return;
            }
            
            // 开始处理授权回调
            setTimeout(() => {
                handleAuthCallback();
            }, 500);
        });
        
        // 监听来自父页面的消息
        window.addEventListener('message', function(event) {
            // 这里可以处理来自小程序webview的消息
            console.log('收到消息:', event.data);
        });
    </script>
</body>
</html>
