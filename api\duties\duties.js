import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}

const baseUrl = `/services/compliancelistservice/`
export default {

    dutyList(params, key, getObj) {
        switch (key) {
            case 'info':
                return request(
                    `${baseUrl}api/duty/positions/${getObj.id}`,
                    params, 'get')
            case 'update':
                return request(
                    `${baseUrl}api/duty/positions/update`,
                    params, 'put')
            case 'draft':
                return request(
                    `${baseUrl}api/duty/positions/draft`,
                    params, 'post')
            default:
                return request(
                    `${baseUrl}api/duty/positions/page`,
                    params, 'post')
        }
    },
}

