<template>
  <view class="regulations-detail-container">
    <!-- Main Content -->
    <view class="main-content">
      <!-- Title Section -->
      <view class="card title-section">
        <view class="title-row">
          <text class="title">{{ detailForm.title }}</text>
          <view class="collect-icon" @click="handleCollect">
            <uni-icons :type="detailForm.isCollect ? 'star-filled' : 'star'" 
                       :color="detailForm.isCollect ? '#FFD700' : '#999999'" 
                       size="20"></uni-icons>
          </view>
        </view>
        <view class="status-row">
          <view class="info-left">
            <view class="version-info">版本：v{{ detailForm.version }}</view>
            <view class="publisher-info">发布单位：{{ detailForm.createdBy }}</view>
            <text class="update-info">创建时间： {{ detailForm.createdAt }} </text>
          </view>
          <view class="status-badge" :class="getStatusClass(detailForm.status)">{{ statusText }}</view>
        </view>
      </view>

      <!-- Tab Navigation -->
      <view class="tab-bar">

        <view @click="active = index" class="tab-item" :class="{ active: active === index }"
          v-for="(tab, index) in tabs" :key="index">
          <text class="tab-text">{{ tab }}</text>
        </view>
      </view>
      <!-- 主体内容 -->

      <!-- Content Section -->
      <view class="card content-section">
        <!-- 制度内容 -->
        <view v-if="active === 0" class="tab-content">
          <scroll-view class="scroll-container" scroll-y="true">
            <view class="content-container">
              <!-- v-html="detailForm.content" -->
              <view class="content-item" style="padding-top: 0;">
                <mp-html :content="detailForm.content" />
              </view>
              <!-- <view class="content-item">
                <text class="chapter-title">第一章 总则</text>
                <text class="content-text">第一条 为规范公司合规管理，防范合规风险，保障公司稳健经营，根据《中华人民共和国公司法》等法律法规，制定本细则。</text>
                <text class="content-text">第二条 本细则适用于公司总部及所有分支机构、子公司。</text>
              </view> -->

              <!-- <view class="content-item">
                <text class="chapter-title">第二章 合规管理组织架构</text>
                <text class="content-text">第三条 公司设立合规管理委员会，由总经理担任主任，各部门负责人为成员。</text>
                <text class="content-text">第四条 合规管理部门设合规总监一名，专职合规管理人员若干名。</text>
              </view> -->

              <!-- <view class="content-item">
                <text class="chapter-title">第三章 合规风险管理</text>
                <text class="content-text">第五条 公司建立合规风险识别、评估、监测和报告机制。</text>
                <text class="content-text">第六条 各部门应定期开展合规风险自查，并向合规管理部门报告。</text>
              </view> -->

              <view class="content-item last-item">
                <view class="attachment-header">
                  <text class="chapter-title">附件 ({{ detailForm.attachments.length }})</text>
                </view>
                <view class="divider"></view>
                <view class="attachment-item" v-for="item in detailForm.attachments" :key="item.id">
                  <view class="attachment-left">
                    <text class="attachment-name" style="margin-left: 0;">{{ item.fileName || '未命名文件' }}</text>
                    <text class="attachment-size">{{ item.fileSize || '未知大小' }}</text>
                  </view>
                  <view class="attachment-actions">
                    <FilePreview :item="item" />
                    <FileDownload :item="item" />
                  </view>
                </view>
                <view class="empty-attachment" v-if="!detailForm.attachments || detailForm.attachments.length === 0">
                  <text class="empty-text">暂无附件</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 版本记录 -->
        <view v-if="active === 1" class="tab-content">
          <scroll-view class="scroll-container" scroll-y="true" refresher-enabled="true"
            :refresher-triggered="versionRefreshing" @refresherrefresh="onVersionRefresh"
            @scrolltolower="onVersionLoadMore">
            <view class="workflow-container">
              <!-- <view class="workflow-title">版本历史记录</view> -->
              <view class="timeline">
                <view class="timeline-item" v-for="(version, index) in versionList" :key="index">
                  <view class="timeline-dot active"></view>
                  <view class="timeline-content">
                    <view class="version-header">
                      <text class="version-number">v{{ version.version }}</text>
                      <text v-if="version.status !== 'archived'" class="version-status" :class="version.status">{{ version.statusText }}</text>
                    </view>
                    <text class="version-description">{{ version.description }}</text>
                    <view class="version-meta">
                      <text class="version-author">{{ version.author }}</text>
                      <text class="version-time">{{ version.time }}</text>
                    </view>
                  </view>
                </view>
              </view>
              <!-- <view v-if="versionLoading" class="loading-more">
                <text class="loading-text">加载中...</text>
              </view>
              <view v-if="versionNoMore" class="no-more">
                <text class="no-more-text">没有更多数据了</text>
              </view> -->
            </view>
          </scroll-view>
        </view>

        <!-- 审批记录 -->
        <view v-if="active === 2" class="tab-content">
          <scroll-view class="scroll-container" scroll-y="true" refresher-enabled="true"
            :refresher-triggered="approvalRefreshing" @refresherrefresh="onApprovalRefresh"
            @scrolltolower="onApprovalLoadMore">
            <view class="workflow-container">
              <!-- <view class="workflow-title">审批流程记录</view> -->
              <view class="timeline">
                <view class="timeline-item" v-for="(approval, index) in approvalList" :key="index"
                  @click="handleApprovalReview(approval)">
                  <view class="timeline-dot active"></view>
                  <view class="timeline-content">
                    <view class="approval-header">
                      <text class="approval-step">{{ approval.regulationName || '制度审核' }}</text>
                      <text class="approval-status" :class="approval.status">{{ getStatusText(approval.status) }}</text>
                    </view>
                    <text class="approval-description">审查说明：{{ approval.explain || '暂无说明' }}</text>
                    <view class="approval-meta">
                      <text class="approval-approver">审核类型：{{ getAuditTypeText(approval.auditType) }}</text>
                    </view>
                    <view class="approval-time-row">
                      <text class="approval-time">审批时间：{{ approval.createdAt }}</text>
                    </view>
                    <view v-if="approval.opinion" class="approval-comment">
                      <text class="comment-label">审核意见：</text>
                      <text class="comment-text">{{ approval.opinion }}</text>
                    </view>
                  </view>
                </view>
              </view>
              <!-- <view v-if="approvalLoading" class="loading-more">
                <text class="loading-text">加载中...</text>
              </view>
              <view v-if="approvalNoMore" class="no-more">
                <text class="no-more-text">没有更多数据了</text>
              </view> -->
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
    <FooterBar @review="handleReview" :buttons="buttons" />


  </view>
</template>

<script setup>
import FooterBar from '@/components/footerBar.vue'
import FilePreview from '@/components/FilePreview/FilePreview.vue'
import FileDownload from '@/components/FileDownload/FileDownload.vue'
import regulations from '@/api/regulations/regulations.js'
import complianceApi from '@/api/compliance/index.js'
import collectApi from './regulations.js'
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { onLoad, onShow, onUnload } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/pinia.js';
import getDictData from '@/utils/dict.js'

const id = ref('');
// 收藏相关状态
const collectLoading = ref(false);
let collectDebounceTimer = null;

// 防抖相关状态
const lastClickTime = ref(0); // 记录上次点击时间
const debounceDelay = 5000; // 5秒防抖延迟

const buttons = computed(() => {
  // 根据状态动态设置按钮样式
  const isPublished = detailForm.value.status === 'PUBLISHED';
    // 如果是已发布状态，显示禁用的按钮
    if (isPublished) {
      return [
        {
          text: '制度已发布',
          type: 'disabled',
          slotName: 'draft',
          bgColor: '#f5f5f5',
          textColor: '#999999',
          border: '1px solid #e0e0e0',
          disabled: true
        }
      ];
    } else {
      return [
        {
          text: '一键合规审查',
          type: 'review',
          slotName: 'draft',
          bgColor: '#fff',
          textColor: '#1a73e8',
          border: '1px solid #1a73e8'
        }
      ];
    }
})

const tabs = ref(['制度内容', '版本记录', '审批记录']);
const detailForm = ref({});
const active = ref(0);

// 字典数据
const dictData = ref([]);

// 获取字典数据
onMounted(async () => {
  try {
    const res = await getDictData('4');
    if (res && res.length > 0) {
      dictData.value = res.map(item => ({
        name: item.name,
        value: item.value
      }));
    }
    console.log('dictData', dictData.value);
  } catch (error) {
    console.error('获取字典数据失败:', error);
  }
});

// 状态映射计算属性
const statusText = computed(() => {
  if (!dictData.value || dictData.value.length === 0) {
    return detailForm.value.status || '';
  }
  const statusItem = dictData.value.find(item => item.value === detailForm.value.status);
  return statusItem ? statusItem.name : detailForm.value.status || '';
});

// 监听tab切换，初始化对应数据
watch(active, (newVal, oldVal) => {
  if (newVal === 1 && versionList.value.length === 0) {
    // 切换到版本记录时，如果没有数据则加载
    loadVersionData(true);
  } else if (newVal === 2 && approvalList.value.length === 0) {
    // 切换到审批记录时，如果没有数据则加载
    loadApprovalData(true);
  }
});

// 版本记录相关数据
const versionList = ref([]);
const versionRefreshing = ref(false);
const versionLoading = ref(false);
const versionNoMore = ref(false);
const versionPage = ref(1);
const versionPageSize = ref(10);

// 审批记录相关数据
const approvalList = ref([]);
const approvalRefreshing = ref(false);
const approvalLoading = ref(false);
const approvalNoMore = ref(false);
const approvalPage = ref(1);
const approvalPageSize = ref(10);

// 审批相关变量已移除，改为跳转页面方式

// 版本记录数据现在通过动态加载，移除静态数据


// 审批记录数据已移除静态数据，改为动态加载
// 获取制度详情
const getRegulationDetail = () => {
  uni.showLoading({
    title: '加载中...'
  });
  regulations.enterpriseDetail({ id: id.value }).then(res => {
    detailForm.value = res;
    // 检查收藏状态
    checkCollectStatus();
    uni.hideLoading();
  }).catch(err => {
    uni.hideLoading();
    console.error('获取制度详情失败:', err);
  });
};

// 检查收藏状态
const checkCollectStatus = () => {
  // 收藏状态现在通过detailForm.isCollect字段获取，无需额外处理
  console.log('当前收藏状态:', detailForm.value.isCollect);
};

// 处理收藏/取消收藏
const handleCollect = () => {
  // 防抖处理，防止重复点击
  if (collectLoading.value) {
    return;
  }
  
  // 清除之前的定时器
  if (collectDebounceTimer) {
    clearTimeout(collectDebounceTimer);
  }
  
  // 设置防抖定时器
  collectDebounceTimer = setTimeout(() => {
    performCollectAction();
  }, 300); // 300ms防抖
};

// 执行收藏操作
const performCollectAction = async () => {
  if (collectLoading.value) {
    return;
  }
  
  collectLoading.value = true;
  
  try {
    if (detailForm.value.isCollect) {
      // 取消收藏
      const params = {
        type: 1, // 1表示内部制度
        regulationId: id.value
      };
      
      await collectApi.cancelCollect(params);
      detailForm.value.isCollect = false;
      
      uni.showToast({
        title: '取消收藏成功',
        icon: 'success',
        duration: 1500
      });
    } else {
      // 添加收藏
      const params = {
        regulationId: parseInt(id.value),
        regulationTitle: detailForm.value.title || '',
        type: 1 // 1表示内部制度
      };
      
      await collectApi.addCollect(params);
      detailForm.value.isCollect = true;
      
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
        duration: 1500
      });
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    
    // 根据错误信息给出不同的提示
    let errorMessage = '操作失败，请重试';
    if (error.message && error.message.includes('重复')) {
      errorMessage = detailForm.value.isCollect ? '已取消收藏' : '已收藏';
      // 如果是重复操作，更新状态
      detailForm.value.isCollect = !detailForm.value.isCollect;
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 1500
    });
  } finally {
    collectLoading.value = false;
  }
};

// 组件卸载时清除定时器
onUnload(() => {
  if (collectDebounceTimer) {
    clearTimeout(collectDebounceTimer);
  }
});

onLoad((options) => {
  if (options.id) {
    id.value = options.id;
    // 获取制度详情
    // getRegulationDetail();
    // // 初始化版本记录数据
    // loadVersionData(true);
  }
});
onShow(() => {
  // 获取制度详情
  getRegulationDetail();
  // 初始化版本记录数据
  loadVersionData(true);
});
const handleReview = () => {
  if (detailForm.value.status === 'PUBLISHED') {
    return;
  }

  // 防抖检查：第一次点击不限制，后续点击需要间隔5秒
  const currentTime = Date.now();
  if (lastClickTime.value > 0 && (currentTime - lastClickTime.value) < debounceDelay) {
    const remainingTime = Math.ceil((debounceDelay - (currentTime - lastClickTime.value)) / 1000);
    uni.showToast({
      title: `请等待${remainingTime}秒后再试`,
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 二次确认提醒
  uni.showModal({
    title: '确认操作',
    content: '确定要进行合规审查吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        if (detailForm.value.status === 'PUBLISHED') {
          uni.showToast({
            title: '制度已发布',
            icon: 'none'
          });
          return;
        }
        
        // 更新最后点击时间
        lastClickTime.value = currentTime;
        
        // 跳转到转化表页面，传入制度信息
        const obj = {
          id: id.value,
          title: detailForm.value.title || '制度文件'
        };
        uni.navigateTo({
          url: `/pages/component/regulations/transformTable?obj=${encodeURIComponent(JSON.stringify(obj))}`,
        });
      }
    }
  });
};

// 版本记录数据加载
const loadVersionData = (isRefresh = false) => {
  if (isRefresh) {
    versionPage.value = 1;
    versionNoMore.value = false;
    versionList.value = [];
  }

  if (versionLoading.value || versionNoMore.value) return;

  versionLoading.value = true;

  // 调用真实API获取版本历史记录
  complianceApi.getRegulationVersion(id.value).then((res) => {
    // 处理API返回的数据
    const apiData = res.content || [];

    // 转换API数据格式为前端需要的格式
    const formattedData = apiData.map(item => ({
      version: item.version || '1.0',
      status: item.version === detailForm.value.version ? 'current' : 'archived',
      statusText: item.version === detailForm.value.version ? '当前版本' : '已归档',
      description: item.changeLog || item.summary || '版本更新',
      author: item.updatedBy || '未知',
      time: item.updatedAt
    }));

    // 模拟分页处理（如果API不支持分页）
    const startIndex = (versionPage.value - 1) * versionPageSize.value;
    const endIndex = startIndex + versionPageSize.value;
    const pageData = formattedData.slice(startIndex, endIndex);

    if (isRefresh) {
      versionList.value = pageData;
    } else {
      versionList.value.push(...pageData);
    }

    if (pageData.length < versionPageSize.value || endIndex >= formattedData.length) {
      versionNoMore.value = true;
    } else {
      versionPage.value++;
    }

    versionLoading.value = false;
    versionRefreshing.value = false;
  }).catch((err) => {
    console.error('获取版本记录失败:', err);
    versionLoading.value = false;
    versionRefreshing.value = false;
    uni.showToast({
      title: '加载版本记录失败',
      icon: 'none'
    });
  });
};

// 审批记录数据加载
const loadApprovalData = (isRefresh = false) => {
  if (isRefresh) {
    approvalPage.value = 1;
    approvalNoMore.value = false;
    approvalList.value = [];
  }

  if (approvalLoading.value || approvalNoMore.value) return;

  approvalLoading.value = true;

  const userStore = useUserStore();
  const params = {
    regulationId: id.value
  };

  complianceApi.companyReview({ page: approvalPage.value - 1, size: approvalPageSize.value }, params).then((res) => {
    if (isRefresh) {
      approvalList.value = res.content || [];
    } else {
      approvalList.value.push(...(res.content || []));
    }

    if (!res.content || res.content.length < approvalPageSize.value) {
      approvalNoMore.value = true;
    } else {
      approvalPage.value++;
    }

    approvalLoading.value = false;
    approvalRefreshing.value = false;
  }).catch((err) => {
    approvalLoading.value = false;
    approvalRefreshing.value = false;
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  });
};

// scroll-view事件处理
const onVersionRefresh = () => {
  versionRefreshing.value = true;
  loadVersionData(true);
};

const onVersionLoadMore = () => {
  loadVersionData(false);
};

const onApprovalRefresh = () => {
  approvalRefreshing.value = true;
  loadApprovalData(true);
};

const onApprovalLoadMore = () => {
  loadApprovalData(false);
};



// 处理审批（保留原有逻辑但简化）
const handleApproval = (status) => {
  // 刷新对应的列表数据
  if (active.value === 1) {
    loadVersionData(true);
  } else if (active.value === 2) {
    loadApprovalData(true);
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PASS': '通过',
    'CONDITIONALPASS': '有条件通过',
    'NOPASS': '不通过',
    'REVIEWING': '待审查',
    'EXPIRED': '过期'
  };
  return statusMap[status] || status;
};


// 获取审核类型文本
const getAuditTypeText = (type) => {
  const typeMap = {
    'CONVENTION': '常规审查',
    'EMERGENCY': '紧急审查'
  };
  return typeMap[type] || type;
};

// 获取状态对应的CSS类名
const getStatusClass = (status) => {
  const statusClassMap = {
    'DRAFT': 'draft',
    'PUBLISHED': 'published',
    'REVIEWING': 'reviewing',
    'SAVE': 'save',
    'EXPIRED': 'expired',
    'EFFECTIVE': 'effective'
  };
  return statusClassMap[status] || '';
};

// 处理审批记录点击事件
const handleApprovalReview = (approval) => {
  console.log('点击审批记录:', approval);
  // 这里可以添加具体的处理逻辑，比如显示详情弹窗或跳转到详情页面
  uni.showToast({
    title: '查看审批详情',
    icon: 'none'
  });
};
</script>

<style lang="scss" scoped>
.regulations-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* 设置全屏高度 */
  background-color: #F5F7FA;

  .nav-bar {
    height: 88rpx;
    background-color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32rpx;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .nav-left {
    display: flex;
    align-items: center;
  }

  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-left: 16rpx;
  }

  .nav-right {
    display: flex;
    align-items: center;

    .nav-btn {
      display: flex;
      align-items: center;
      margin-left: 30rpx;
    }

    .btn-text {
      font-size: 26rpx;
      color: #1A73E8;
      margin-left: 8rpx;
    }
  }


  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* 防止外层滚动 */
  }

  .card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 32rpx;
    padding: 32rpx;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .title-section {
    .title-row {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 24rpx;
      
      .title {
        font-size: 36rpx;
        color: #333333;
        flex: 1;
        margin-right: 16rpx;
        line-height: 1.4;
        font-weight: 550;
      }
      
      .collect-icon {
           display: flex;
           align-items: center;
           justify-content: center;
           width: 48rpx;
           height: 48rpx;
           border-radius: 50%;
           background-color: #f8f9fa;
           transition: all 0.3s ease;
           
           &:active {
             background-color: #e9ecef;
             transform: scale(0.95);
           }
         }
    }
  }

  .status-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16rpx;
  }

  .info-left {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .version-info {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 8rpx;
  }

  .publisher-info {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 8rpx;
  }

  .update-info {
    font-size: 24rpx;
    color: #666666;
  }

  .status-badge {
    border-radius: 16rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    align-self: center;
    font-weight: 500;
    
    // 根据状态类型设置不同的背景色和文字色
    &.draft {
      background-color: #F5F5F5;
      color: #666666;
    }
    
    &.published {
      background-color: #E8F5E8;
      color: #4CAF50;
    }
    
    &.reviewing {
      background-color: #FFF8E1;
      color: #FFC107;
    }
    
    // 默认样式（兜底）
    &:not(.draft):not(.published):not(.reviewing) {
      background-color: #E6F4FF;
      color: #1A73E8;
    }
  }

  .tab-bar {
    height: 88rpx;
    background-color: #FFFFFF;
    display: flex;
    margin: 0 32rpx;
  }

  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tab-item.active {
    border-bottom: 2rpx solid #1A73E8;
  }

  .tab-text {
    font-size: 28rpx;
  }

  .tab-item.active .tab-text {
    color: #1A73E8;
  }

  .tab-item:not(.active) .tab-text {
    color: #666666;
  }

  .content-section {
    margin-top: 16rpx;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    /* 防止内容溢出 */
  }

  .content-item {
    padding: 32rpx 0;
    border-bottom: 1px solid #E0E0E0;
  }

  .last-item {
    border-bottom: none;
  }

  .chapter-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    display: block;
    margin-bottom: 16rpx;
  }

  .content-text {
    font-size: 28rpx;
    color: #333333;
    line-height: 48rpx;
    display: block;
    margin-top: 16rpx;
  }

  .attachment-item {
    height: 96rpx;
    display: flex;
    align-items: center;
    padding: 0 16rpx;
  }

  .attachment-name {
    font-size: 28rpx;
    color: #333333;
    margin-left: 24rpx;
  }

  // .bottom-tab-bar {
  //   height: 112rpx;
  //   background-color: #FFFFFF;
  //   border-top: 1px solid #E0E0E0;
  //   display: flex;
  //   position: fixed;
  //   bottom: 0;
  //   left: 0;
  //   right: 0;
  // }

  // .bottom-tab-bar .tab-item {
  //   flex: 1;
  //   display: flex;
  //   flex-direction: column;
  //   justify-content: center;
  //   align-items: center;
  // }

  // .tab-label {
  //   font-size: 24rpx;
  //   color: #666666;
  //   margin-top: 8rpx;
  // }

  // .bottom-tab-bar .tab-item.active .tab-label {
  //   color: #1A73E8;
  // }

  /* tab内容容器样式 */
  .tab-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* scroll-view容器样式 */
  .scroll-container {
    flex: 1;
    width: 100%;
    min-height: 0;
    /* 允许flex子元素收缩 */
  }

  /* 内容容器样式 */
  .content-container {
    padding: 20rpx;
    min-height: 600rpx;
    /* 确保内容有足够高度触发滚动 */
  }

  /* 工作流样式 */
  .workflow-container {
    padding: 20rpx;
    min-height: 600rpx;
    /* 确保内容有足够高度触发滚动 */
  }

  .workflow-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
    text-align: center;
  }

  .timeline {
    position: relative;
    padding-left: 40rpx;
  }

  .timeline::before {
    content: '';
    position: absolute;
    left: 20rpx;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background: #e0e0e0;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 40rpx;
    padding-left: 40rpx;
  }

  .timeline-dot {
    position: absolute;
    left: -28rpx;
    top: 10rpx;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background: #e0e0e0;
    border: 4rpx solid #fff;
    box-shadow: 0 0 0 2rpx #e0e0e0;
  }

  .timeline-dot.active {
    background: #007aff;
    box-shadow: 0 0 0 2rpx #007aff;
  }

  .timeline-dot.pending {
    background: #ff9500;
    box-shadow: 0 0 0 2rpx #ff9500;
  }

  .timeline-content {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 24rpx;
    border-left: 4rpx solid #007aff;
  }

  .version-header,
  .approval-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .version-number,
  .approval-step {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
  }

  .version-status,
  .approval-status {
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    color: #fff;
  }

  .version-status.current,
  .approval-status.completed {
    background: #34c759;
  }

  .version-status.archived {
    background: #8e8e93;
  }

  .approval-status.pending {
    background: #ff9500;
  }

  /* 审批状态样式 */
  .approval-status.PASS {
    background: #34c759;
    /* 绿色 - 通过 */
  }

  .approval-status.CONDITIONALPASS {
    background: #ff9500;
    /* 橙色 - 有条件通过 */
  }

  .approval-status.NOPASS {
    background: #ff3b30;
    /* 红色 - 不通过 */
  }

  .approval-status.REVIEWING {
    background: #007aff;
    /* 蓝色 - 待审查 */
  }

  .approval-status.EXPIRED {
    background: #8e8e93;
    /* 灰色 - 过期 */
  }

  .version-description,
  .approval-description {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
    line-height: 1.5;
  }

  .version-meta,
  .approval-meta {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
  }

  .approval-comment {
    margin-top: 16rpx;
    padding: 16rpx;
    background: #fff;
    border-radius: 8rpx;
    border: 1rpx solid #e0e0e0;
  }

  .comment-label {
    font-size: 24rpx;
    color: #666;
    font-weight: bold;
  }

  .comment-text {
    font-size: 24rpx;
    color: #333;
    margin-left: 16rpx;
  }

  /* 附件列表样式 */
  .attachment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .divider {
    height: 2rpx;
    background: #f0f0f0;
    margin-bottom: 24rpx;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f5f5f5;
  }

  .attachment-item:last-child {
    border-bottom: none;
  }

  .attachment-left {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .attachment-name {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 8rpx;
  }

  .attachment-size {
    font-size: 24rpx;
    color: #999;
  }

  .attachment-actions {
    display: flex;
    font-size: 26rpx;
    gap: 16rpx;
  }

  .empty-attachment {
    text-align: center;
    padding: 40rpx 0;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 审批弹窗样式 */
.approval-modal {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;

  .modal-header {
    padding: 40rpx 32rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .modal-content {
    padding: 32rpx;

    .form-item {
      margin-bottom: 24rpx;

      .form-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        font-weight: 500;
      }
    }
  }

  .modal-footer {
    padding: 20rpx 32rpx 40rpx;

    .btn-group {
      display: flex;
      gap: 24rpx;

      .uv-button {
        flex: 1;
      }
    }
  }
}
</style>