<template>
	<view class="transform-table">
		<view class="transform-table__form">
			<uv-form labelPosition="top" label-width="200px" :model="resForm" :rules="rules" ref="form">
				<uv-form-item label="文件导入">
					<uploadThree v-model="fileData" :auto-upload="true" :service-name="'compliancelistservice'"
						:category-name="'ai'" tip-text="支持上传图片、文档等文件，单个文件不超过10MB"
						@upload-success="handleUploadSuccess" />
				</uv-form-item>
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">职责信息区</text>
						<view class="ai-tag" @click="handleDutyGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>
					<uv-form-item prop="orgUnitId" required label="部门">
						<!-- <picker-input v-model="resForm.orgUnitId" :columns="[getDepartments]" displayKey="name"
							placeholder="请选择部门" @confirm="handleDepartmentChange"></picker-input> -->
						<tree-picker v-model="resForm.orgUnitId" :multiple="false" :default-expand-level="1"
							title="选择部门（单选）" placeholder="请选择部门" @change="handleDepartmentChange"></tree-picker>
					</uv-form-item>
					<uv-form-item prop="postId" required label="岗位">
						<picker-input v-model="resForm.postId" :columns="[postList]" displayKey="name"
							placeholder="请选择岗位" />
					</uv-form-item>

					<uv-form-item prop="basicDuty" required label="基本职责">
						<view class="textarea-container">
							<textarea class="own-textarea" autoHeight maxlength="9999" v-model="resForm.basicDuty"
								placeholder="请描述该岗位的主要职能" placeholder-style="color:#dadbde"></textarea>
							<view class="ai-icon-bottom" @click="generateAIContent('basicDuty')">
								<!-- <uv-icon name="edit-pen" size="20" color="#409eff"></uv-icon> --><text
									class="iconfont icon-robot-line" style="font-size: 22px; color: #FFFFFF;"></text>
							</view>
						</view>
					</uv-form-item>
				</view>


				<!-- <uv-form-item prop="riskLevel" label="岗位风险等级">
					<filter-tags v-model="resForm.riskLevel" :fixed="false" :tags="tags" valueKey="value" />
				</uv-form-item> -->
				<!-- <uv-form-item prop="postStatus" label="岗位状态">
                    <filter-tags v-model="resForm.postStatus" :fixed="false" :tags="statusTags" valueKey="value" />
                </uv-form-item> -->
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">合规信息区</text>
						<view class="ai-tag" @click="handleComplianceGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>
					<!-- 下面都是律师填写的区域 -->
					<uv-form-item prop="complianceInfo.positiveComplianceRequirement" label="正面合规要求">
						<view class="textarea-container">
							<textarea autoHeight maxlength="9999" class="own-textarea" placeholder-style="color:#dadbde"
								v-model="resForm.complianceInfo.positiveComplianceRequirement"
								placeholder="请输入正面合规要求"></textarea>
							<view class="ai-icon-bottom" @click="generateAIContent('positiveComplianceRequirement')">
								<text class="iconfont icon-robot-line" style="font-size: 22px; color: #FFFFFF;"></text>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item prop="complianceInfo.negativeComplianceRequirement" label="负面合规要求">
						<view class="textarea-container">
							<textarea autoHeight maxlength="9999" class="own-textarea" placeholder-style="color:#dadbde"
								v-model="resForm.complianceInfo.negativeComplianceRequirement"
								placeholder="请输入负面合规要求"></textarea>
							<view class="ai-icon-bottom" @click="generateAIContent('negativeComplianceRequirement')">
								<text class="iconfont icon-robot-line" style="font-size: 22px; color: #FFFFFF;"></text>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item prop="complianceBasis" label="合规依据">
						<textarea autoHeight class="own-textarea" placeholder-style="color:#dadbde" maxlength="9999"
							v-model="resForm.complianceInfo.complianceBasis" placeholder="合规依据"></textarea>
					</uv-form-item>
				</view>

				<!-- 风险防控区 -->
				<view class="card-section">
					<view class="section-title">
						<text class="section-title-text">风险防控区</text>
						<view class="ai-tag" @click="handleRiskGenerate">
							<uni-icons type="star" size="14" color="#67C23A"></uni-icons>
							<text class="ai-tag-text">智能生成</text>
						</view>
					</view>
					<uv-form-item prop="riskControlInfo.riskTypes" label="风险来源（八项权力识别）">
						<uv-checkbox-group v-model="resForm.riskControlInfo.riskTypes" wrap>
							<view class="checkbox-container">
								<uv-checkbox v-for="item in riskSource" :key="item.value" :name="item.value"
									:label="item.name" shape="square" icon-size="22">
									{{ item.name }}
								</uv-checkbox>
							</view>
						</uv-checkbox-group>
					</uv-form-item>

					<uv-form-item prop="riskControlInfo.riskLevel" label="岗位风险等级">
						<filter-tags v-model="resForm.riskControlInfo.riskLevel" :fixed="false" :tags="tags"
							valueKey="value" />
					</uv-form-item>
					<uv-form-item prop="riskControlInfo.controlMeasures" label="防控措施">
						<uv-textarea autoHeight v-model="resForm.riskControlInfo.controlMeasures" maxlength="9999"
							placeholder="请输入防控措施或使用智能生成功能" />
					</uv-form-item>
				</view>

			</uv-form>
		</view>
		<!-- v-if="resForm.approvalStatus !== 'APPROVED'" -->
		<!-- 律师在PENDING状态下可以操作 -->
		<view v-if="isDataLoaded && lawyer && resForm.approvalStatus === 'PENDING'">
			<FooterBar @click="handleFooterClick" :buttons="footerButtons" />
		</view>
		<!-- 非律师在草稿状态或无状态下可以操作 -->
		<view v-if="isDataLoaded && !lawyer && (resForm.approvalStatus === 'DRAFT' || !resForm.approvalStatus)">
			<FooterBar @click="handleFooterClick" :buttons="footerButtons" />
			<!-- <uv-button v-if="pageType === 'add' || lawyer" type="primary" @click="handleClick">{{ lawyer ? '更新' : '提交'
			}}</uv-button> -->
		</view>

		<!-- 智能识别进度弹窗 -->
		<uni-popup ref="progressDialog" type="dialog">
			<view class="dialog-content">
				<text class="dialog-title">猫伯伯智能识别中</text>
				<view class="progress-body">
					<image src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
						style="width: 48px; height: 48px;" class="pulse"></image>
					<text class="progress-text">{{ currentProgress }}</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import {
	ref,
	computed,
	onMounted
} from 'vue'
import TreePicker from '@/components/tree-picker.vue'
import pickerInput from '@/components/picker-input.vue'
import filterTags from '@/components/filterTags.vue'
import addRes from '@/api/duties/addForm.js'
import duties from '@/api/duties/duties.js'
import org from '@/api/org/index.js'
import threeAI from '@/api/threeAI/threeAI.js'
// import UploadFile from '@/components/uploadFile.vue'
import uploadThree from '@/components/uploadThree.vue'
import FooterBar from '@/components/footerBar.vue'
import {
	useUserStore
} from '@/store/pinia.js'
import getDictData from '@/utils/dict.js'
const userStore = useUserStore()
const lawyer = ref(false);
lawyer.value = userStore.lawyer
const fileData = ref([])
const isSubmitting = ref(false)
// AI生成状态控制
const isGenerating = ref(false)
const currentProgress = ref('')
const progressDialog = ref()
// 数据加载状态控制
const isDataLoaded = ref(false)

// FooterBar按钮配置
const footerButtons = ref([
	{
		text: '保存草稿',
		type: 'draft',
		slotName: 'draft',
		bgColor: '#fff',
		textColor: '#1a73e8',
		border: '1px solid #1a73e8',
		isHidden: lawyer.value
	},
	{
		text: '风险智能识别',
		type: 'ai',
		slotName: 'ai',
		bgColor: '#fff',
		textColor: '#1a73e8',
		border: '1px solid #1a73e8'
	},
	{
		text: lawyer.value ? '发布' : '提交审核',
		type: 'submit',
		slotName: 'submit',
		bgColor: '#1a73e8',
		textColor: '#fff'
	}
]);

// 防抖定时器
let debounceTimer = null;

function handleFooterClick(btn) {
	console.log('FooterBar clicked:', btn.type);

	// 对所有按钮添加2秒防抖
	if (debounceTimer) {
		uni.showToast({
			title: '操作过于频繁，请稍后再试',
			icon: 'none',
			duration: 1500
		});
		return;
	}

	// 设置防抖定时器
	debounceTimer = setTimeout(() => {
		debounceTimer = null;
	}, 2000);

	switch (btn.type) {
		case 'draft':
			// 保存草稿逻辑
			saveDraft();
			break;
		case 'ai':
			// 风险智能识别逻辑
			handleAIRecognition();
			break;
		case 'submit':
			// 提交审核逻辑（防重复点击）
			if (!isSubmitting.value) {
				handleClick();
			}
			break;
		default:
			break;
	}
};




// 字典数据
const riskSource = ref([])

// 岗位列表
const postList = ref([])

// 获取页面参数
const pageType = ref('add') // 页面类型：add-新增，edit-编辑，detail-详情
const itemId = ref('') // 详情ID

const form = ref(null);
const conversationId = ref(null)
// 页面初始化
onMounted(async () => {
	// 获取字典数据
	try {
		const aiID = await threeAI.getSessionId()
		conversationId.value = aiID.conversationId
		const result = await getDictData(80)
		riskSource.value = result
	} catch (error) {
		console.error('获取字典数据失败:', error)
	}

	// 获取页面参数
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	const options = currentPage.options
	console.log('options', options)
	if (options.type) {
		pageType.value = options.type
	}
	if (options.id) {
		itemId.value = options.id

		// 如果是详情或编辑页面，加载数据
		if (pageType.value === 'detail' || lawyer.value) {
			loadDetailData()
		} else {
			// 非详情页面直接设置数据已加载
			isDataLoaded.value = true
		}
	} else {
		// 新增页面直接设置数据已加载
		isDataLoaded.value = true
	}
})

// 加载详情数据
function loadDetailData() {
	uni.showLoading({
		title: '加载中...'
	})

	duties.dutyList({}, 'info', {
		id: itemId.value
	}).then(res => {
		uni.hideLoading()
		if (res) {
			console.log('详情数据:', res)
			// 回填表单数据
			resForm.value = {
				approvalStatus: res.approvalStatus,
				orgUnitId: res.orgUnitId || '',
				orgUnitName: res.orgUnitName || '',
				postId: res.postId || '',
				postName: res.postName || '',

				basicDuty: res.basicDuty || '',
				riskLevel: res.riskLevel || '',
				complianceInfo: {
					positiveComplianceRequirement: res.complianceInfo?.positiveComplianceRequirement || '',
					negativeComplianceRequirement: res.complianceInfo?.negativeComplianceRequirement || '',
					complianceBasis: res.complianceInfo?.complianceBasis || ''
				},
				riskControlInfo: {
					riskTypes: res.riskControlInfo?.riskTypes || [],
					riskLevel: res.riskControlInfo?.riskLevel || '',
					controlMeasures: res.riskControlInfo?.controlMeasures || ''
				}
			}

			// 如果有部门ID，需要加载对应的岗位列表，确保postId能正确回填
			if (res.orgUnitId) {
				console.log('加载部门岗位列表:', res.orgUnitId)
				getPostsByDepartment(res.orgUnitId)
			}
		}
		// 数据加载完成
		isDataLoaded.value = true
	}).catch(err => {
		uni.hideLoading()
		console.error('加载详情失败:', err)
		uni.showToast({
			title: '加载详情失败',
			icon: 'none',
			duration: 2000
		})
		// 即使加载失败也设置数据已加载，避免界面一直不显示
		isDataLoaded.value = true
	})
}
// 部门
const getDepartments = userStore.getDepartments()
console.log('部门列表:', getDepartments)
// 角色判断
const isEmployee = computed(() => {
	// 假设用户角色存储在 userStore 中，这里需要根据实际情况调整
	return userStore.userInfo?.role === 'employee' || userStore.userInfo?.roleType === 'employee' || true
})

//岗位类型获取不对
const postColumns = ref([
	[...userStore.dict.list[6].list]
])

const tags = ref([{
	name: '高危',
	value: 'HIGH'
}, {
	name: '中危',
	value: 'MEDIUM'
}, {
	name: '低危',
	value: 'LOW'
}]);

const statusTags = ref([{
	name: '激活',
	value: 'ACTIVE'
}, {
	name: '停用',
	value: 'INACTIVE'
}]);

// 合规依据文本处理
const complianceBasisText = computed({
	get() {
		return resForm.value.complianceInfo.complianceBasis.join('\n')
	},
	set(value) {
		resForm.value.complianceInfo.complianceBasis = value.split('\n').filter(item => item.trim())
	}
})

const resForm = ref({
	orgUnitId: '', //部门ID
	orgUnitName: '', //部门名称
	postId: '', //岗位ID
	postName: '', //岗位名称
	currentEmployeeId: '', //当前任职人员ID
	employeeName: '', //任职人员姓名
	basicDuty: '', //基本职责
	riskLevel: '', //岗位风险等级
	// postStatus: 'ACTIVE', //岗位状态
	complianceInfo: {
		positiveComplianceRequirement: '', //正面合规要求
		negativeComplianceRequirement: '', //负面合规要求
		complianceBasis: '' //合规依据
	},
	riskControlInfo: {
		riskTypes: [], //风险来源（多选） 八项权力
		riskLevel: '', //风险等级
		controlMeasures: '' //防控措施
	}
})
const rules = computed(() => {
	return {
		orgUnitId: [{
			required: true,
			validator: (rule, value, callback) => {
				if (value === '' || value === null || value === undefined) {
					callback(new Error('请选择部门'));
				} else {
					callback();
				}
			},
			trigger: 'change'
		}],
		postId: [{
			required: true,
			validator: (rule, value, callback) => {
				if (value === '' || value === null || value === undefined) {
					callback(new Error('请选择岗位'));
				} else {
					callback();
				}
			},
			trigger: 'change'
		}],
		basicDuty: [{
			required: true,
			message: '请输入基本职责',
			trigger: 'blur'
		}],
		'complianceInfo.positiveComplianceRequirement': lawyer.value ? [{ required: true, message: '请输入正面合规要求', trigger: 'blur' }] : [],
		'complianceInfo.negativeComplianceRequirement': lawyer.value ? [{ required: true, message: '请输入负面合规要求', trigger: 'blur' }] : [],
		'complianceInfo.complianceBasis': lawyer.value ? [{ required: true, message: '请输入合规依据', trigger: 'blur' }] : []
	}
})
// 通用提交函数，减少代码冗余
function submitResponsibilityData(type = 'submit') {
	uni.showLoading({
		title: type === 'draft' ? '保存中...' : type === 'update' ? '更新中...' : '提交中...'
	})

	// 根据类型设置不同的审批状态和API方法
	let approvalStatus
	let successMessage
	let apiMethod
	let apiKey

	switch (type) {
		case 'draft':
			approvalStatus = 'DRAFT'
			successMessage = '草稿保存成功'
			// 如果存在详情ID，调用更新接口，否则调用草稿接口
			if (itemId.value) {
				apiMethod = duties.dutyList
				apiKey = 'update'
			} else {
				apiMethod = duties.dutyList
				apiKey = 'draft'
			}
			break
		case 'submit':
			approvalStatus = 'PENDING'
			successMessage = '添加成功'
			apiMethod = addRes.dutyForm
			apiKey = 'info'
			break
		case 'update':
			approvalStatus = 'APPROVED'
			successMessage = '更新成功'
			apiMethod = duties.dutyList
			apiKey = 'update'
			break
		default:
			approvalStatus = 'PENDING'
			successMessage = '操作成功'
			apiMethod = addRes.dutyForm
			apiKey = 'info'
	}

	// 构建提交数据
	const submitData = {
		...resForm.value,
		approvalStatus: approvalStatus,
		dutyMainId: itemId.value ? itemId.value : null,
		// 获取部门和岗位名称
		orgUnitName: getDepartments.find(item => item.id === resForm.value.orgUnitId)?.name || '',
		postName: postList.value.find(item => item.id === resForm.value.postId)?.name || ''
	}

	// 如果是更新操作或草稿保存且有详情ID，添加ID
	if (type === 'update' || (type === 'draft' && itemId.value)) {
		submitData.dutyMainId = itemId.value
	}

	// 调用对应的API
	const apiParams = type === 'draft' ? [submitData, apiKey, null] : [submitData, apiKey]
	apiMethod(...apiParams).then(res => {
		// 草稿保存成功后，将返回的dutyMainId赋值给itemId
		if (type === 'draft' && res) {
			itemId.value = res.dutyMainId
		}

		uni.showToast({
			title: successMessage,
			icon: 'success',
			duration: 2000
		})
		uni.hideLoading()
		isSubmitting.value = false
		// 草稿保存不返回上一页
		if (type !== 'draft') {
			setTimeout(() => {
				if (type !== 'draft') {
					uni.navigateBack({
						delta: 1
					})
				}
			}, 800)
		}
	}).catch(err => {
		console.error('提交失败:', err)
		uni.hideLoading()
		isSubmitting.value = false
		uni.showToast({
			title: type === 'draft' ? '保存失败，请重试' : type === 'update' ? '更新失败，请重试' : '添加失败，请重试',
			icon: 'none',
			duration: 2000
		})
	})
}

// 保存草稿
function saveDraft() {
	// 防重复点击
	if (isSubmitting.value) {
		return
	}

	// 先进行表单验证
	//  resForm.value.postId = 1
	form.value.validate().then(valid => {
		if (!valid) {
			return
		}
		isSubmitting.value = true
		submitResponsibilityData('draft')
	}).catch(() => {
		uni.showToast({
			title: '请填写必填项',
			icon: 'none'
		})
		// 验证失败时重置提交状态
		isSubmitting.value = false
	})
}
function handleUploadSuccess() {
	uni.showToast({
		title: '上传成功',
		icon: 'none',
		duration: 2000
	});
}
// 智能审查
async function handleAIRecognition() {
	// 防重复点击判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!fileData.value || fileData.value.length === 0) {
		uni.showToast({
			title: '请先上传文件',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	// 显示进度弹窗
	progressDialog.value.open();

	// 更新进度文本
	const updateProgress = (message) => {
		currentProgress.value = message;
	};

	try {
		// 设置生成状态
		isGenerating.value = true;

		updateProgress('正在分析文件内容...');

		const params = {
			conversationId: conversationId.value,
			fileUrl: fileData.value[0].key,
			toolKey: 'kimi'
		};

		updateProgress('正在智能识别职责信息...');
		const result = await threeAI.analysisAI(params);

		if (result) {
			updateProgress('正在填充表单数据...');

			// 根据接口返回的字段填充表单
			if (result.basicDuty) {
				resForm.value.basicDuty = result.basicDuty;
			}

			// 处理部门和岗位信息
			await handleDepartmentAndPositionIds(result);
			if (result.positiveRequirements) {
				resForm.value.complianceInfo.positiveComplianceRequirement = result.positiveRequirements;
			}
			if (result.negativeRequirements) {
				resForm.value.complianceInfo.negativeComplianceRequirement = result.negativeRequirements;
			}
			if (result.complianceBasis) {
				resForm.value.complianceInfo.complianceBasis = result.complianceBasis;
			}
			if (result.identifiedPowers) {
				resForm.value.riskControlInfo.riskTypes = result.identifiedPowers;
			}

			if (result.preventiveMeasures) {
				resForm.value.riskControlInfo.controlMeasures = result.preventiveMeasures;
			}
			if (result.riskLevel) {
				resForm.value.riskControlInfo.riskLevel = result.riskLevel;
			}

			updateProgress('识别完成！');

			// 延迟关闭弹窗，让用户看到完成状态
			setTimeout(() => {
				progressDialog.value.close();
				uni.showToast({
					title: '智能识别完成',
					icon: 'success',
					duration: 2000
				});
				isGenerating.value = false;
			}, 1000);
		} else {
			progressDialog.value.close();
			uni.showToast({
				title: '识别失败，请重试',
				icon: 'none',
				duration: 2000
			});
		}
	} catch (error) {
		progressDialog.value.close();
		isGenerating.value = false;
		console.error('智能识别错误:', error);
		uni.showToast({
			title: '识别失败，请重试',
			icon: 'none',
			duration: 2000
		});
	}
}
// 提交
function handleClick() {
	if (isSubmitting.value) {
		return;
	}

	form.value.validate().then(valid => {
		if (!valid) {
			return;
		}
		isSubmitting.value = true;
		if (lawyer.value) {
			updateResponsibility()
		} else {
			addResponsibility()
		}
	}).catch(() => {
		// 验证失败时重置提交状态
		isSubmitting.value = false;
	});
}

// 新增职责
function addResponsibility() {
	submitResponsibilityData('submit')
}

// 更新职责数据
function updateResponsibility() {
	submitResponsibilityData('update')
}

// 职责信息区智能生成
async function handleDutyGenerate() {
	console.log('handleDutyGenerate', isGenerating.value)
	try {
		// 防重复点击判断
		if (isGenerating.value) {
			uni.showToast({
				title: '正在生成中，请稍候...',
				icon: 'none',
				duration: 2000
			});
			return;
		}

		// 判断是否上传了文件
		// if (!fileData.value || fileData.value.length === 0) {
		// 	uni.showToast({
		// 		title: '请先上传相关文件',
		// 		icon: 'none'
		// 	});
		// 	return;
		// }

		// 设置生成状态
		isGenerating.value = true;

		// 显示进度弹窗
		progressDialog.value.open();
		currentProgress.value = '正在分析内容...';

		const params = {
			conversationId: conversationId.value,
			toolKey: 'kimi',
			fileUrl: fileData.value[0].key
		};

		// 更新进度文本
		currentProgress.value = '正在生成职责信息...';

		const result = await threeAI.getDutyPosition(params);
		if (result) {
			// 填充职责信息
			if (result.basicDuty) {
				resForm.value.basicDuty = result.basicDuty;
			}

			// 处理部门和岗位信息
			await handleDepartmentAndPositionIds(result);

			// 延迟关闭弹窗
			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({ title: '职责信息生成成功', icon: 'success' });
			}, 800);
		} else {
			progressDialog.value.close();
			// uni.showToast({ title: result.message || '生成失败', icon: 'none' });
		}
	} catch (error) {
		console.error('职责信息生成失败:', error);
		progressDialog.value.close();
		isGenerating.value = false;
		uni.showToast({ title: '生成失败，请重试', icon: 'none' });
	} finally {
		// 重置生成状态
		// isGenerating.value = false;
	}
};

// 合规信息区智能生成
const handleComplianceGenerate = async () => {
	try {
		// 防重复点击判断
		if (isGenerating.value) {
			uni.showToast({
				title: '正在生成中，请稍候...',
				icon: 'none',
				duration: 2000
			});
			return;
		}
		console.log('handleComplianceGenerate', isGenerating.value);

		// if (!fileData.value || fileData.value.length === 0) {
		// 	uni.showToast({
		// 		title: '请先上传相关文件',
		// 		icon: 'none'
		// 	});
		// 	return;
		// }
		if (!resForm.value.orgUnitName) {
			uni.showToast({
				title: '请先选择部门',
				icon: 'none'
			});
			return
		}
		// 设置生成状态
		isGenerating.value = true;

		// 显示进度弹窗
		progressDialog.value.open();
		currentProgress.value = '正在分析内容...';

		const params = {
			fileUrl: fileData.value[0].key,
			conversationId: conversationId.value,
			toolKey: 'kimi',
			departmentName: resForm.value.orgUnitName,
			positionName: resForm.value.postName,
			basicDuty: resForm.value.basicDuty
		};

		// 更新进度文本
		currentProgress.value = '正在生成合规信息...';

		const result = await threeAI.getCompliance(params);
		if (result) {
			// 处理部门和岗位信息（如果返回）
			await handleDepartmentAndPositionIds(result);

			// 填充合规信息
			if (result.positiveRequirements) {
				resForm.value.complianceInfo.positiveComplianceRequirement = result.positiveRequirements;
			}
			if (result.negativeRequirements) {
				resForm.value.complianceInfo.negativeComplianceRequirement = result.negativeRequirements;
			}
			if (result.complianceBasis) {
				resForm.value.complianceInfo.complianceBasis = result.complianceBasis;
			}

			// 延迟关闭弹窗
			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;

				uni.showToast({ title: '合规信息生成成功', icon: 'success' });
			}, 800);
		} else {
			progressDialog.value.close();
			// uni.showToast({ title: result.message || '生成失败', icon: 'none' });
		}
	} catch (error) {
		console.error('合规信息生成失败:', error);
		progressDialog.value.close();
		uni.showToast({ title: '生成失败，请重试', icon: 'none' });
		isGenerating.value = false;
	}
};

// 风险防控区智能生成
const handleRiskGenerate = async () => {
	try {
		// 防重复点击判断
		if (isGenerating.value) {
			uni.showToast({
				title: '正在生成中，请稍候...',
				icon: 'none',
				duration: 2000
			});
			return;
		}

		// if (!fileData.value || fileData.value.length === 0) {
		// 	uni.showToast({
		// 		title: '请先上传相关文件',
		// 		icon: 'none'
		// 	});
		// 	return;
		// }

		// 设置生成状态
		isGenerating.value = true;

		// 显示进度弹窗
		progressDialog.value.open();
		currentProgress.value = '正在分析文件内容...';

		const params = {
			fileUrl: fileData.value[0].key,
			conversationId: conversationId.value,
			toolKey: 'kimi',
			departmentName: resForm.value.orgUnitName,
			positionName: resForm.value.postName,
			basicDuty: resForm.value.basicDuty,
			positiveRequirements: resForm.value.complianceInfo.positiveComplianceRequirement,
			negativeRequirements: resForm.value.complianceInfo.negativeComplianceRequirement,
			externalRegulations: resForm.value.complianceInfo.complianceBasis
		};

		// 更新进度文本
		currentProgress.value = '正在生成风险防控信息...';

		const result = await threeAI.getEight(params);
		if (result) {
			// 处理部门和岗位信息（如果返回）
			await handleDepartmentAndPositionIds(result);

			// 填充风险防控信息
			if (result.identifiedPowers) {
				resForm.value.riskControlInfo.riskTypes = result.identifiedPowers;
			}
			if (result.preventiveMeasures) {
				resForm.value.riskControlInfo.controlMeasures = result.preventiveMeasures;
			}
			if (result.riskLevel) {
				resForm.value.riskLevel = result.riskLevel;
			}

			// 延迟关闭弹窗
			setTimeout(() => {
				progressDialog.value.close();
				isGenerating.value = false;
				uni.showToast({ title: '风险防控信息生成成功', icon: 'success' });
			}, 800);
		} else {
			progressDialog.value.close();
			// uni.showToast({ title: result.message || '生成失败', icon: 'none' });
		}
	} catch (error) {
		console.error('风险防控信息生成失败:', error);
		isGenerating.value
		progressDialog.value.close();
		uni.showToast({ title: '生成失败，请重试', icon: 'none' });
	}
};
// AI生成内容方法
function generateAIContent(fieldType) {
	// 防重复调用判断
	if (isGenerating.value) {
		uni.showToast({
			title: '正在生成中，请稍候...',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	if (!resForm.value.postId) {
		uni.showToast({
			title: '请先选择岗位',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	try {
		isGenerating.value = true;

		// 根据字段类型设置不同的生成点
		const pointMap = {
			'basicDuty': '基本职责',
			'positiveComplianceRequirement': '正面合规要求',
			'negativeComplianceRequirement': '负面合规要求',
			'complianceBasis': '合规依据',
			'controlMeasures': '防控措施'
		};
		const params = {
			toolKey: 'kimi', // AI工具类型
			type: 2, // 生成类型：2-重点岗位职责清单
			keywords: postList.value.find(item => item.id === resForm.value.postId)?.name || '', // 岗位名称作为关键词
			point: pointMap[fieldType] // 内容描述点
		};

		uni.showLoading({
			title: 'AI生成中...',
			mask: true
		});

		addRes.aiGenerate(params).then(res => {
			if (res) {
				// 将AI生成的内容填入对应字段
				if (fieldType === 'positiveComplianceRequirement') {
					resForm.value.complianceInfo.positiveComplianceRequirement = res;
				} else if (fieldType === 'negativeComplianceRequirement') {
					resForm.value.complianceInfo.negativeComplianceRequirement = res;
				} else if (fieldType === 'complianceBasis') {
					complianceBasisText.value = res;
				} else if (fieldType === 'controlMeasures') {
					resForm.value.riskControlInfo.controlMeasures = res;
				} else {
					resForm.value[fieldType] = res;
				}
				uni.showToast({
					title: 'AI生成成功',
					icon: 'success',
					duration: 2000
				});
			} else {
				uni.showToast({
					title: '生成失败，请重试',
					icon: 'none',
					duration: 2000
				});
			}
		}).catch(error => {
			console.error('AI生成失败:', error);
			uni.showToast({
				title: '生成失败，请重试',
				icon: 'none',
				duration: 2000
			});
		}).finally(() => {
			uni.hideLoading();
			isGenerating.value = false;
		});
	} catch (error) {
		console.error('AI生成异常:', error);
		uni.hideLoading();
		isGenerating.value = false;
		uni.showToast({
			title: '生成失败，请重试',
			icon: 'none',
			duration: 2000
		});
	}
}

// 监听部门选择变化
function handleDepartmentChange(item) {
	console.log('部门选择变化:', item);
	// 清空岗位选择
	resForm.value.orgUnitName = item.nodes[0].name;
	resForm.value.postId = ''
	resForm.value.postName = ''
	// 获取部门对应的岗位列表
	getPostsByDepartment(item.value)
}

// 处理AI返回的部门和岗位ID，转换为对应的名称和ID
async function handleDepartmentAndPositionIds(data) {
	// 处理部门信息
	if (data.departmentId) {
		const department = getDepartments.find(dept => dept.id === data.departmentId);
		if (department) {
			resForm.value.orgUnitId = data.departmentId;
			resForm.value.orgUnitName = department.name;
			// 获取该部门的岗位列表
			await getPostsByDepartment(data.departmentId);
		}
	} else if (data.departmentName) {
		resForm.value.orgUnitName = data.departmentName;
	}

	// 处理岗位信息
	if (data.positionId) {
		const position = postList.value.find(post => post.id === data.positionId);
		if (position) {
			resForm.value.postId = data.positionId;
			resForm.value.postName = position.name;
		}
	} else if (data.positionName) {
		resForm.value.postName = data.positionName;
	}
}

// 根据部门ID获取岗位列表
async function getPostsByDepartment(deptId) {
	if (!deptId) return

	uni.showLoading({
		title: '加载岗位中...'
	})

	try {
		const res = await org.getDeptByPostId({
			id: deptId
		})
		uni.hideLoading()
		if (res && Array.isArray(res)) {
			postList.value = res.map(item => ({
				id: item.id,
				name: item.name,
				value: item.id
			}))
		} else {
			postList.value = []
			uni.showToast({
				title: '未获取到岗位数据',
				icon: 'none',
				duration: 2000
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('获取岗位失败:', error)
		uni.showToast({
			title: '获取岗位失败',
			icon: 'none',
			duration: 2000
		})
	}
}
</script>

<style lang="scss" scoped>
.transform-table__form {
	margin-bottom: 40rpx;
}


.compliance-basis-container {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
}

.regulation-section {
	margin-bottom: 20rpx;
}

.regulation-section:last-child {
	margin-bottom: 0;
}

.regulation-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #495057;
	margin-bottom: 12rpx;
	display: block;
}

.regulation-item {
	padding: 12rpx 16rpx;
	margin-bottom: 8rpx;
	background-color: #ffffff;
	border-radius: 6rpx;
	border: 1rpx solid #dee2e6;
	display: flex;
	flex-direction: column;
}

.regulation-name {
	font-size: 26rpx;
	color: #212529;
	font-weight: 500;
	margin-bottom: 4rpx;
}

.regulation-code {
	font-size: 22rpx;
	color: #6c757d;
}

.preventive-measures-container {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
}

.measure-item {
	padding: 12rpx 16rpx;
	margin-bottom: 8rpx;
	background-color: #ffffff;
	border-radius: 6rpx;
	border: 1rpx solid #dee2e6;
}

.measure-item:last-child {
	margin-bottom: 0;
}

.measure-text {
	font-size: 26rpx;
	color: #212529;
	line-height: 1.5;
}

.textarea-container {
	position: relative;
}

.ai-icon-bottom {
	width: 60rpx;
	height: 60rpx;
	position: absolute;
	bottom: 10rpx;
	right: 10rpx;
	z-index: 10;
	padding: 5rpx;
	background-color: #1A73E8;
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
	cursor: pointer;
	transition: all 0.3s ease;
	display: none;
	/* 隐藏AI功能按钮 */
	align-items: center;
	justify-content: center;

	&:hover {
		background-color: #1A73E8;
		transform: scale(1.1);
	}

	&:active {
		transform: scale(0.95);
	}
}

.checkbox-container {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-top: 10rpx;

	:deep(.uv-checkbox) {
		background-color: #f8f9fa;
		border-radius: 8rpx;
		transition: all 0.2s ease;

		&:hover {
			background-color: #e9f5ff;
		}
	}
}

/* 进度弹窗样式 */
.dialog-content {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 40rpx;
	width: 80vw;
	max-width: 600rpx;
	box-sizing: border-box;
}

.dialog-title {
	display: block;
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	text-align: center;
	margin-bottom: 30rpx;
}

.progress-body {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 0;
}

.progress-text {
	font-size: 28rpx;
	color: #666;
	margin-top: 30rpx;
}

.pulse {
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.5;
	}

	100% {
		opacity: 1;
	}
}
</style>