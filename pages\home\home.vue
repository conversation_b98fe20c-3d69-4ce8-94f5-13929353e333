<template>
	<view class="home-container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-left">
				<view class="avatar">
					<image :src="avatarImg"
						mode="aspectFill" class="avatar-img" />
				</view>
				<text class="greeting">{{ greeting }}，{{ userStore.userDetail?.realName }}</text>
			</view>
			<view class="header-right">
				<view class="search-box" @click="goToSearch">
					<!-- <uni-icons type="search" size="16" color="#999"></uni-icons> -->
					<input disabled type="text" placeholder="搜索制度/案例/流程/问答" class="search-input" />
				</view>
				<view class="notification-wrapper" @click="goToNotifications">
						<uni-icons type="notification" size="24" color="#666"></uni-icons>
						<view v-if="unreadCount > 0" class="notification-badge">
							<text class="badge-text">{{ unreadCount > 99 ? '99+' : unreadCount }}</text>
						</view>
					</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<scroll-view class="main-content" scroll-y>
			<!-- 合规评分 Banner -->
			<!-- <view class="score-card">
				<view class="score-header">
					<view>
						<text class="score-title" @click="goRateDetail">合规得分</text>
						<view class="score-value">
							<text class="score-number">85</text>
							<text class="score-unit">分</text>
						</view>
					</view>
					<view class="score-detail" @click="GainNode">
						<text>查看评分详情</text>
						<uni-icons type="arrow-up-right" size="12" color="#1A73E8"></uni-icons>
					</view>
				</view>
			</view> -->
			<!-- 一体三翼 核心模块 -->
			<view class="section">
				<view class="section-title title-top">一体三翼</view>
				<view class="core-grid">
					<view class="core-item" @click="goRegulations">
						<view class="core-icon blue">
							<uni-icons type="folder-add" size="24" color="#1A73E8"></uni-icons>
						</view>
						<view class="core-text">
							<text class="core-title">外规内化</text>
							<text class="core-desc">管理/转化法规</text>
						</view>
					</view>
					<view class="core-item">
						<view class="core-icon green">
							<uni-icons type="list" size="24" color="#34A853"></uni-icons>
						</view>
						<view @click="goList" class="core-text">
							<text class="core-title">三张清单</text>
							<text class="core-desc">识别岗位/流程风险</text>
						</view>
					</view>
					<view class="core-item" @click="$tools.routeJump(`/pages/component/respond/complianceReview`)">
						<!-- <view class="core-item" @click="jump(`/respond/complianceReview`)"> -->
						<view class="core-icon purple">
							<uni-icons type="search" size="24" color="#8A2BE2"></uni-icons>
						</view>
						<view class="core-text">
							<text class="core-title">合规审查</text>
							<text class="core-desc">合同/全流程</text>
						</view>
					</view>
					<!-- @click="$tools.routeJump(`/pages/component/respond/continueOptimize`)" -->
					<view class="core-item" @click="optimize">
						<view class="core-icon orange">
							<uni-icons type="loop" size="24" color="#FF8C00"></uni-icons>
						</view>
						<view class="core-text">
							<text class="core-title">持续优化</text>
							<text class="core-desc">调查/整改/报告</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 快捷功能 -->
			<view class="section">
				<view class="section-title">快捷功能</view>
				<view class="quick-func">
					<!-- <view class="func-item">
						<view class="func-icon blue">
							<uni-icons type="locked" size="28" color="#1A73E8"></uni-icons>
						</view>
						<text class="func-title">智能审查</text>
						<text class="func-desc">一键合规检查</text>
					</view> -->
				
					<view class="func-item" @click="goDetail('/pages/component/complianceDashboard/index')">
						<view class="func-icon green">
							<text class="iconfont icon-tousujianyi" style="font-size: 26px; color: #34A853;"></text>
						</view>
						<text class="func-title">合规驾驶舱</text>
						<text class="func-desc">驾驶舱</text>
					</view>
					<view class="func-item" @click="goQa('/pages/qa/qa')">
						<view class="func-icon purple">
							<text class="iconfont icon-AI" style="font-size: 26px; color: #8A2BE2;"></text>
						</view>
						<text class="func-title">AI 问答</text>
						<text class="func-desc">智能助手</text>
					</view>
					<!-- <view @click="goDetail('/pages/component/study/studyPlan')" class="func-item">
						<view class="func-icon orange">
							<uni-icons type="calendar" size="28" color="#FF8C00"></uni-icons>
						</view>
						<text class="func-title">学习计划</text>
						<text class="func-desc">定制课程</text>
					</view> -->
					<!-- <view class="func-item">
						<view class="func-icon pink">
							<uni-icons type="bars" size="28" color="#FF1493"></uni-icons>
						</view>
						<text class="func-title">学习进度</text>
						<text class="func-desc">查看进度</text>
					</view> -->
					<view @click="goDetail('/pages/component/study/Certificate')" class="func-item">
						<view class="func-icon yellow">
							<text class="iconfont icon-zhengshulist" style="font-size: 26px; color: #f4c021;"></text>
						</view>
						<text class="func-title">证书管理</text>
						<text class="func-desc">我的证书</text>
					</view>
						<view class="func-item" @click="goComplain">
						<view class="func-icon blue">
							<text class="iconfont icon-tousu" style="font-size: 26px; color: #1A73E8;"></text>
						</view>
						<text class="func-title">违规应对</text>
						<text class="func-desc">一键建议</text>
					</view>
				</view>
			</view>

			<!-- 公司最新动态 -->
			<view class="section">
				<view class="section-header">
					<view>咨讯动态</view>
					<view class="section-more" @click="$tools.routeJump(`/pages/companyNews/companyNews`)">
						<text  style="color:#1A73E8;font-size:24rpx">更多</text>
						<uni-icons type="right" size="12" color="#1A73E8"></uni-icons>
					</view>
				</view>
				<view class="news-list">
					<view class="news-item" v-for="(item, index) in newsList" :key="item.id || index"
						@click="goNewsDetail(item)">
						<view class="news-content">
							<!-- <text class="news-date">{{ formatDate(item.publishTime || item.createdAt) }}</text> -->
							<text class="news-title">{{ item.title || '暂无标题' }}</text>
						</view>
						<!-- <uni-icons type="arrow-right" size="16" color="#ccc"></uni-icons> --><text
							class="news-date">{{ formatDate(item.publishTime || item.createdAt) }}</text>
					</view>
					<!-- 如果没有数据，显示默认内容 -->
					<view v-if="newsList.length === 0" class="news-item">
						<view class="news-content">
							<text class="news-date">--</text>
							<text class="news-title">暂无动态</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import {
		ref,
		getCurrentInstance,
		nextTick,
		watch,
		computed,
	} from 'vue';
	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app'
	import {
		useUserStore
	} from '@/store/pinia.js';
	import {
		storeToRefs
	} from 'pinia'
	import { formatDateToMMDD } from '@/utils/dateUtils.js'
	const userStore = useUserStore();
	import homeApi from '@/api/home/<USER>'
	
	import newsApi from '@/api/news/news.js'
	import orgApi from '@/api/org/index.js'
	import uploadApi from '@/api/upload.js'
	// const userStore = ref(null)
	const {
		proxy
	} = getCurrentInstance()
  const avatarImg = ref("")
	// 新闻列表数据
	const newsList = ref([])
	// 未读消息数量
	const unreadCount = ref(0)

	// 根据时间动态显示问候语
	const greeting = computed(() => {
		const hour = new Date().getHours()
		if (hour >= 6 && hour < 12) {
			return '上午好'
		} else if (hour >= 12 && hour < 18) {
			return '下午好'
		} else {
			return '晚上好'
		}
	})

	onLoad((e) => {
		// userStore.value = useUserStore();
		initialDictionary() //初始化字典信息
	    getNewsList() // 获取新闻列表
		getOrgTree() // 获取组织树数据
		getPostList() // 获取岗位列表
		getAvatar()
	})
	onShow(() => {
	   if(userStore['avatarUpdatedHome']){
		  getAvatar()
		}
		userStore['avatarUpdatedHome'] = false
		getUnreadCount() // 获取未读消息数量
		// 获取头像
		// nextTick(() => {
		// 	if (!userStore.token) {
		// 		proxy.$tools.routeJump(`/pages/login/login`)
		// 	}
		// })
	})

	function optimize() {
		uni.navigateTo({
			url: '/pages/component/continuousOpt/index'
		});
	}
	// 获取新闻列表
	const getNewsList = async () => {
		try {
			const res = await newsApi.getList({
				page: 0,
				size: 4,
				eagerload: true
			})
			if (res && res.content) {
				newsList.value = res.content
			}
		} catch (error) {
			console.error('获取新闻列表失败:', error)
		}
	}

	// 获取未读消息数量
	const getUnreadCount = async () => {
		try {
			const res = await newsApi.getNotice()
			// 根据API返回格式处理数据
			if (typeof res === 'number') {
				// 如果直接返回数字
				unreadCount.value = res
			} else if (res && typeof res === 'object') {
				// 如果返回对象，可能包含count字段或其他字段
				unreadCount.value = res.count || res.unreadCount || res.totalCount || 0
			} else {
				unreadCount.value = 0
			}
		} catch (error) {
			console.error('获取未读消息数量失败:', error)
			unreadCount.value = 0
		}
	}

	// 跳转到新闻详情
	const goNewsDetail = (newsItem) => {
		uni.navigateTo({
			url: `/pages/companyNews/detail?id=${newsItem.id}`
		})
	}

	// 跳转到消息通知页面
	const goToNotifications = () => {
		// 跳转到消息列表页面
		proxy.$tools.routeJump('/pages/message/index')
	}

	// 格式化日期
	const formatDate = (dateStr) => {
		return formatDateToMMDD(dateStr)
	}

	function goComplain() {
		uni.navigateTo({
			url: '/pages/component/monitor/myReport'
		});
	}

	function goDetail(page) {
		uni.navigateTo({
			url: page
		});
	}

	function goQa(page) {
		uni.switchTab({
			url: page
		});
	}

	// 跳转到搜索页面
	const goToSearch = () => {
		uni.navigateTo({
			url: '/pages/home/<USER>'
		});
	}
	
	// 跳转到消息页面
	const goToMessage = () => {
		uni.navigateTo({
			url: '/pages/message/index'
		});
	}
	
	// 跳转到测试页面
	const goToTest = () => {
		uni.navigateTo({
			url: '/pages/home/<USER>'
		});
	}
	const goRegulations = () => {
		uni.navigateTo({
			url: '/pages/regulations/regulations'
		});
	}
	const goList = () => {
		uni.navigateTo({
			url: '/pages/lists/index'
		});
	}
	const GainNode = () => {
		uni.navigateTo({
			url: '/pages/component/homeSon/complianceRateDetail'
		});
	}

	const goRateDetail = () => {
		uni.navigateTo({
			url: '/pages/component/homeSon/complianceRateDetail'
		});
	}


	/**
	 * 获取组织树数据
	 */
	const getOrgTree = async () => {
		console.log('获取组织树数据')
		try {
			const res = await orgApi.unitTree()
			if (res) {
				userStore.setOrgTree(res)
				// 筛选出type为DEPARTMENT的数据
				const departments = filterDepartments(res)
				userStore.setDepartments(departments)
				// 筛选出type为SUBSIDIARY的数据
				// const subsidiary = filterSubsidiary(res)
				// userStore.setSubsidiary(subsidiary)
			}
		} catch (error) {}
	}
	// 获取所有岗位
	const getPostList = async () => {
		try {
			const res = await orgApi.getPostList()
			if (res) {
				userStore.setPostList(res.content)
				console.log('岗位数据获取成功:', res)
			}
		} catch (error) {
			console.error('获取岗位数据失败:', error)
		}
	}
	// 获取头像
	const getAvatar = async () => {
		try {
			if(userStore.userDetail.avatar){
              const res = await uploadApi.getFileUrl(userStore.userDetail.avatar)
			  console.log('头像数据获取成功:', res)
			  avatarImg.value = res
			}else{
			   avatarImg.value = "https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
			}
		} catch (error) {
			  avatarImg.value = "https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
		}
	}
	// 递归筛选出type为SUBSIDIARY的数据,数据在children里面，所以你要取children

	const filterSubsidiary = (data) => {
		const result = []
		// 递归查找所有type为SUBSIDIARY的节点
		const findSubsidiaries = (nodes) => {
			if (!Array.isArray(nodes)) return
			nodes.forEach(node => {
				if (node.type === 'SUBSIDIARY') {
					// 从SUBSIDIARY的children中筛选出type为DEPARTMENT的数据
					if (node.children && Array.isArray(node.children)) {
						const departments = node.children.filter(child => child.type === 'DEPARTMENT')
						
						if (departments.length > 0) {
							// 创建新的结构，保持SUBSIDIARY的信息但type改为DEPARTMENT，children为筛选出的部门
							result.push({
								...node,
								type: 'DEPARTMENT',
								children: departments
							})
						}
					}
				}

				// 继续递归查找子节点中的SUBSIDIARY
				if (node.children && Array.isArray(node.children)) {
					findSubsidiaries(node.children)
				}
			})
		}

		findSubsidiaries(Array.isArray(data) ? data : [data])
		console.log(result, 'ooooooooooooooo')
		return result
	}
	
		/**
	 * 递归筛选出type为DEPARTMENT的数据
	 */
	const filterDepartments = (data) => {
		const departments = []
		const traverse = (nodes) => {
			if (!Array.isArray(nodes)) return

			nodes.forEach(node => {
				if (node.type === 'DEPARTMENT' || node.type === 'TEAM') {
					departments.push(node)
				}

				// 如果有子节点，继续递归
				if (node.children && Array.isArray(node.children)) {
					traverse(node.children)
				}
			})
		}

		traverse(Array.isArray(data) ? data : [data])
		return departments
	}
	/**
	 * 初始化获取字典信息
	 */
	const initialDictionary = async () => {
		const {
			dict
		} = storeToRefs(userStore)
		console.log('字典初始化')
		let arr = ['义务类型', '风险等级', '来源类型', '状态', '责任部门', '领域类型', '案例来源', '审查项', '通知触发条件', '通知方式', '通知触发状态',
			'匿名举报限制',
			'适用范围', '发布范围', '审核状态', '审查类型', '制度类型', '模板类型', '优先级', '转换状态'
		]
		// 加个定时,可以避免频繁调取
		if (!userStore.dict) {
			var list = [];
			for (let i = 1; i <= arr.length; i++) {
				try {
					const list_ = await getDictList(i);
					list.push({
						id: i,
						name: arr[i - 1],
						list: list_,
					})
				} catch (err) {}
			}
			userStore.dict = {
				upTime: Math.floor(Date.now() / 1000),
				list,
			}
		}

		function getDictList(types) {
			return new Promise(async (resolve, reject) => {
				homeApi.dict({
					types,
				}).then((res) => {
					resolve(res)
				}).catch((err) => {})
			});
		}
	}
</script>

<style lang="scss" scoped>
	@import "@/static/iconfont.css";

	.home-container {
		display: flex;
		flex-direction: column;
		background-color: #F8F9FA;

		.header {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			height: 88rpx;
			padding: 0 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background-color: #fff;
			z-index: 100;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		}

		.header-left {
			display: flex;
			align-items: center;
		}

		.avatar {
			width: 72rpx;
			height: 72rpx;
			border-radius: 50%;
			overflow: hidden;
			background-color: #eee;
		    flex-shrink: 0;
		}

		.avatar-img {
			width: 100%;
			height: 100%;
		}

		.greeting {
			font-size: 14px;
			font-weight: 500;
			color: #333;
			margin-left: 16rpx;
		}

		.header-right {
			display: flex;
			align-items: center;
		}

		.notification-wrapper {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.notification-badge {
			position: absolute;
			top: -8rpx;
			right: -8rpx;
			min-width: 32rpx;
			height: 32rpx;
			background-color: #ff4757;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0 8rpx;
		}

		.badge-text {
			font-size: 20rpx;
			color: #fff;
			font-weight: 500;
			line-height: 1;
		}

		.search-box {
			position: relative;
			margin-right: 32rpx;
		}

		.search-input {
			width: 192rpx;
			height: 64rpx;
			padding-left: 24rpx;
			padding-right: 24rpx;
			background-color: #f5f5f5;
			border-radius: 32rpx;
			font-size: 12px;
			color: #333;
		}

		.main-content {
			flex: 1;
			padding-top: 88rpx;
			padding-bottom: 30rpx;
		}

		// .score-card {
		// 	margin: 32rpx;
		// 	padding: 32rpx;
		// 	background-color: #fff;
		// 	border-radius: 16rpx;
		// 	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		// }

		// .score-header {
		// 	display: flex;
		// 	justify-content: space-between;
		// 	align-items: flex-start;
		// }

		// .score-title {
		// 	font-size: 12px;
		// 	color: #666;
		// }

		// .score-value {
		// 	display: flex;
		// 	align-items: flex-end;
		// 	margin-top: 8rpx;
		// }

		// .score-number {
		// 	font-size: 28px;
		// 	font-weight: bold;
		// 	color: #1A73E8;
		// }

		// .score-unit {
		// 	font-size: 18px;
		// 	color: #666;
		// 	margin-left: 8rpx;
		// }

		// .score-detail {
		// 	display: flex;
		// 	align-items: center;
		// 	font-size: 12px;
		// 	color: #1A73E8;
		// }

		.section {
			margin: 0 32rpx 32rpx;
		}

		.section-title {
			font-size: 14px;
			font-weight: 500;
			color: #333;
			margin-bottom: 16rpx;
		}

		.title-top {
			margin-top: 16rpx;
		}

		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
		}

		.core-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 24rpx;
		}

		.core-item {
			padding: 24rpx;
			background-color: #fff;
			border-radius: 16rpx;
			display: flex;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		}

		.core-icon {
			width: 96rpx;
			height: 96rpx;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.core-icon.blue {
			background-color: rgba(26, 115, 232, 0.1);
		}

		.core-icon.green {
			background-color: rgba(52, 168, 83, 0.1);
		}

		.core-icon.purple {
			background-color: rgba(138, 43, 226, 0.1);
		}

		.core-icon.orange {
			background-color: rgba(255, 140, 0, 0.1);
		}

		.core-text {
			margin-left: 24rpx;
			flex: 1;
						display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.core-title {
			font-size: 14px;
			font-weight: 500;
			color: #333;
			line-height: 1.4;
		}

		.core-desc {
			font-size: 11px;
			color: #999;
			margin-top: 8rpx;
			line-height: 1.4;
			word-wrap: break-word;
			white-space: normal;
		}

		.quick-func {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 16rpx 10rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			display: flex;
			justify-content: space-around;
			align-items: center;
		}

		.func-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			flex: 1;
			padding: 0 8rpx;
		}

		.func-icon {
			width: 112rpx;
			height: 112rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.func-icon.blue {
			background-color: rgba(26, 115, 232, 0.1);
		}

		.func-icon.green {
			background-color: rgba(52, 168, 83, 0.1);
		}

		.func-icon.purple {
			background-color: rgba(138, 43, 226, 0.1);
		}

		.func-icon.orange {
			background-color: rgba(255, 140, 0, 0.1);
		}

		.func-icon.pink {
			background-color: rgba(255, 20, 147, 0.1);
		}

		.func-icon.yellow {
			background-color: rgba(255, 215, 0, 0.1);
		}

		.func-title {
			font-size: 12px;
			color: #333;
			margin-top: 16rpx;
		}

		.func-desc {
			font-size: 11px;
			color: #999;
			margin-top: 8rpx;
		}

		.news-list {
			background-color: #fff;
			border-radius: 16rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		}

		.news-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 32rpx;
			border-bottom: 1rpx solid #f5f5f5;
		}

		.news-content {
			flex: 1;
		}

		.news-date {
			font-size: 11px;
			color: #999;
		}

		.news-title {
			font-size: 14px;
			color: #333;
			margin-top: 8rpx;
		}

		.tab-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			height: 112rpx;
			display: flex;
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		}

		.tab-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}

		.tab-item.active .tab-text {
			color: #1A73E8;
		}

		.tab-text {
			font-size: 11px;
			color: #999;
			margin-top: 8rpx;
		}
	}
</style>