<template>
	<!-- 使用z-paging-swiper为根节点可以免计算高度 -->
	<z-paging-swiper>
		<!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
		<template #top>
			<z-tabs ref="tabs" :list="tabList" :current="current" @change="tabsChange" />
		</template>
		<!-- swiper必须设置height:100%，因为swiper有默认的高度，只有设置高度100%才可以铺满页面  -->
		<swiper class="swiper" :current="current" @transition="swiperTransition"
			@animationfinish="swiperAnimationfinish">
			<swiper-item v-show="current === 0">
				<contractReview></contractReview>
			</swiper-item>
			<swiper-item v-show="current === 1">
				<decisionReview></decisionReview>
			</swiper-item>
			<swiper-item v-show="current === 2">
				<supplementalReview></supplementalReview>
			</swiper-item>
		</swiper>
	</z-paging-swiper>
</template>

<script>
	import contractReview from './contractReview.vue'
	import decisionReview from './decisionReview.vue'
	import supplementalReview from './supplementalReview.vue'

	export default {
		components: {
			contractReview,
			decisionReview,
			supplementalReview,
		},
		data() {
			return {
				tabList: ['合同审查', '重大决策审查', '其他审查'],
				current: 0, // tabs组件的current值，表示当前活动的tab选项
			};
		},
		onShow() {
			// 在页面onShow的时候，刷新当前列表（不是必须的）
			// this.$refs.listItem && this.reloadCurrentList();
		},
		methods: {
			// tabs通知swiper切换
			tabsChange(index) {
				console.log('tabs通知swiper切换', index);
				this.current = index;
			},
			// swiper滑动中
			swiperTransition(e) {
				this.$refs.tabs.setDx(e.detail.dx);
			},
			// swiper滑动结束
			swiperAnimationfinish(e) {
				this.current = e.detail.current;
				this.$refs.tabs.unlockDx();
			},
			// 如果要通知当前展示的z-paging刷新，请调用此方法
			reloadCurrentList() {
				// this.$refs.listItem[this.current].reload();
			}
		}
	}
</script>

<style>
	.swiper {
		height: 100%;
	}
</style>