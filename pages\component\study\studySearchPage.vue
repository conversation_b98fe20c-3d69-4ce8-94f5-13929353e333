<template>
    <view class="study-search-container">
        <!-- 搜索框 -->
         <z-paging class="paging-content" ref="paging" v-model="courses" @query="queryList">
        <view class="search-container">
            <view class="search-input">
                <!-- <input type="text" placeholder="在此搜索课程或关键字..." placeholder-class="placeholder-style"
                    @confirm="handleSearch" /> -->
                 <uv-input v-model="inputValue" @clear="clearSearch" @confirm="handleSearch" placeholder="在此搜索课程或关键字..." clearable shape="circle" prefixIcon="search" v-bind="attrs" />
            </view>
        </view>

        <!-- 热搜/历史记录 -->
        <view class="tag-section">
            <text class="section-title">热搜标签</text>
            <view class="tag-container">
                <view v-for="(tag, index) in hotTags" :key="'hot-' + index" class="tag" @click="handleTagClick(tag)">
                    {{ tag }}
                </view>
            </view>
        </view>

        <view class="tag-section">
            <text class="section-title">历史记录</text>
            <view class="tag-container">
                <view v-for="(tag, index) in historyTags" :key="'history-' + index" class="tag"
                    @click="handleTagClick(tag)">
                    {{ tag }}
                </view>
            </view>
        </view>

        <!-- 搜索结果列表 -->
        <view class="result-section">
            <text class="section-title">搜索结果</text>
            <view v-for="(course, index) in courses" :key="index" class="course-card"
                @click="handleCourseClick(course.id)">
                <image class="thumbnail" :src="course.coverImageUrl" mode="aspectFill" />
                <view class="course-info">
                    <text class="course-title">{{ course.courseName }}</text>
                    <text class="course-desc">{{ course.instructor }} • {{ course.durationMinutes }} 分钟</text>
                </view>
                <uni-icons type="right" size="18" color="#ccc"></uni-icons>
            </view>
        </view>
        </z-paging>
    </view>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import https from '@/api/study/index.js';
import uploads from '@/api/upload.js';

const paging = ref(null);
const inputValue = ref('');
const hotTags = ref([
    '法规解读', '合规审查', '风险管理',
    '内控流程', '反洗钱', '数据隐私'
]);

const historyTags = ref([
    '培训报名', '风险清单', '合规测试', '审计流程'
]);

const courses = ref([]);

const tabs = ref([
    { icon: 'home', text: '首页' },
    { icon: 'search', text: '搜索' },
    { icon: 'book', text: '课程' },
    { icon: 'person', text: '我的' }
]);

function clearSearch() {
    reload();
}

function handleSearch (e) {
    reload();
};

function reload() {
  nextTick(() => {
    // 刷新列表数据(如果不希望列表pageNo被重置可以用refresh代替reload方法)
    paging.value && paging.value.reload();
  })
}

async function queryList(pageNo, pageSize) {
    var params = {
        page: pageNo - 1,
        size: pageSize,
        searchTerm: inputValue.value,
        // keyword: keyword.value,
        // status: 1,
        // tenantId: userStore.tenantId
    }
    try {
        const res = await https.courseList(params);
        // 为每个课程项获取真实的图片URL
        const processedContent = await Promise.all(res.content.map(async (item) => {
            if (item.coverImageUrl) {
                try {
                    const imageUrlRes = await uploads.getFileUrl(item.coverImageUrl);
                    item.coverImageUrl = imageUrlRes; // 用真实URL替换key
                } catch (error) {
                    console.error('获取图片URL失败:', error);
                    // 如果获取失败，保持原有的key或设置默认图片
                }
            }
            return item;
        }));
        paging.value.complete(processedContent);
    } catch (err) {
        paging.value.complete(false);
    }
}
const handleTagClick = (tag) => {
    console.log('点击标签:', tag);
    inputValue.value = tag;
    reload();
};

const handleCourseClick = (id) => {
      uni.navigateTo({
        url: `/pages/component/study/courseDetail?id=${id}`
    });
};
</script>

<style lang="scss" scoped>
.study-search-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-bottom: 100rpx;

    .search-container {
        padding: 30rpx 30rpx 0;
    }

    // .search-input {
    //     height: 72rpx;
    //     background-color: #EFEFF4;
    //     border-radius: 36rpx;
    //     display: flex;
    //     align-items: center;
    //     padding: 0 30rpx;
    // }

    // .search-input input {
    //     flex: 1;
    //     height: 100%;
    //     font-size: 28rpx;
    //     margin-left: 20rpx;
    // }

    // .placeholder-style {
    //     color: #999;
    // }

    .tag-section {
        padding: 30rpx;
    }

    .section-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #666;
        margin-bottom: 20rpx;
        display: block;
    }

    .tag-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
    }

    .tag {
        padding: 12rpx 24rpx;
        background-color: #EFEFF4;
        border-radius: 32rpx;
        font-size: 24rpx;
        color: #333;
    }

    .result-section {
        padding: 0 30rpx;
    }

    .course-card {
        height: 160rpx;
        background-color: #fff;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        padding: 20rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    }

    .thumbnail {
        width: 160rpx;
        height: 120rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
    }

    .course-info {
        flex: 1;
    }

    .course-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
    }

    .course-desc {
        font-size: 24rpx;
        color: #999;
        display: block;
    }
}
</style>