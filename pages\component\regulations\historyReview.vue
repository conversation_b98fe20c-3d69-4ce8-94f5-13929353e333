<template>
    <view class="regulations-container">
        <!--  @query="queryList -->
        <z-paging class="paging-content" ref="paging" v-model="listData" @query="queryList">
            <template #top>
                <header-bar shape="circle" prefixIcon="search" clearable v-model="keyword" :fixed="false">
                    <template #right>
                        <!-- 自定义操作按钮 -->
                        <view class="nav-right">
                            <!-- <view class="nav-btn" @click.stop="handleSearch">
                <uni-icons type="search" size="20" />
              </view> -->
                            <view class="nav-btn" @click="handleAdd">
                                <text class="btn-text">＋新增</text>
                            </view>
                        </view>
                    </template>
                </header-bar>
            </template>
            <view class="list-public-container">
                <view>
                    <view @click="navigateToDetail" class="list-public-item" v-for="(item, index) in listData"
                        :key="index">
                        <view class="item-public-content">
                            <text class="item-public-title">{{ item.title }}</text>
                            <text class="item-public-desc">{{ item.status }} {{ item.effectiveDate }} {{ item.department
                                }}</text>
                        </view>
                        <uni-icons type="right" size="16" color="#999"></uni-icons>
                    </view>
                </view>
            </view>
        </z-paging>
    </view>
</template>

<script setup>
import headerBar from '@/components/headerBar.vue'
import { ref } from 'vue'

const paging = ref(null)
const keyword = ref('')

const listData = ref([
    {
        title: '员工考勤管理制度（2025年修订版）',
        status: '已通过',
        date: '2023-05-12',
        department: '人力资源部',
        id: 1
    },
    {
        title: '信息安全管理办法',
        status: '待审',
        date: '2023-06-15',
        department: 'IT部门',
        id: 2
    },
    {
        title: '财务报销实施细则',
        status: '已通过',
        date: '2023-04-20',
        department: '财务中心'
    },
    {
        title: '项目管理制度（2022年版）',
        status: '待更新',
        date: '2022-11-30',
        department: '项目管理办公室'
    },
    {
        title: '新员工入职培训手册',
        status: '已通过',
        date: '2023-03-08',
        department: '培训发展部'
    },
    {
        title: '办公用品领用管理规定',
        status: '待审',
        date: '2023-06-18',
        department: '行政部'
    },
    {
        title: '员工绩效考核管理办法',
        status: '已通过',
        date: '2023-01-15',
        department: '人力资源部'
    },
    {
        title: '公司车辆使用管理规定',
        status: '待更新',
        date: '2022-09-25',
        department: '行政部'
    },
    {
        title: '公司车辆使用管理规定2',
        status: '待更新',
        date: '2022-09-25',
        department: '行政部'
    },
    {
        title: '公司车辆使用管理规定3',
        status: '待更新',
        date: '2022-09-25',
        department: '行政部'
    }
]);
// 搜索
function handleSearch() {
}

//跳转详情
function navigateToDetail(id) {
    uni.navigateTo({
        url: '/pages/component/regulations/detail?id=' + id,
    })
}

// 新增
function handleAdd() {
    uni.navigateTo({
        url: '/pages/component/regulations/transformTable',
    })
}
//区域滚动
function queryList(pageNo, pageSize) {
    paging.value.complete(listData.value)
}

</script>


<style lang="scss" scoped>
.nav-right {
    display: flex;
    align-items: center;

    .nav-btn {
        display: flex;
        align-items: center;
        margin-left: 30rpx;
    }

    .btn-text {
        font-size: 26rpx;
        color: #1A73E8;
        margin-left: 8rpx;
    }
}
</style>