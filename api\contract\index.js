import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 1000000
    })
}

const baseUrl = `/services/whiskerguardcontractservice/`

export default {
    // 智能审查
    aiContract(contractId, params) {
        return request(
            `${baseUrl}api/contract/message/review/ai/review/${contractId}`,
            {}, 'get')
    },
    // 智能审查结果其他
    aiContractOther(params) {
        return request(
            `${baseUrl}api/compliance/reviews/ai/review?reviewId=${params.reviewId}&reviewType=${params.reviewType}`,
            {}, 'get')
    },
    // 查询所有审查记录
    queryAllReviews(contractId, params = {}) {
        return request(
            `${baseUrl}api/contract/message/review/list/${contractId}`,
            params, 'get')
    },
    //查询所有其他审查记录
    queryAllOtherReviews(params) {
        return request(
            `${baseUrl}api/compliance/review/records?page=${params.page}&size=${params.size}&reviewType=${params.reviewType}&reviewId=${params.reviewId}`,
            {}, 'get')
    },
    // 根据id获取单个审查记录
    queryReviewById(id) {
        return request(
            `${baseUrl}api/contract/message/review/${id}`,
            {}, 'get')
    },
    // 创建合同审查记录（就是当complianceReview不为null时进入的页面提交的接口）
    createReview(params) {
        return request(
            `${baseUrl}api/compliance/reviews`,
            params, 'post')
    },


    /*
      流程相关接口
    */
    // 创建审批流程
    createApproval(params) {
        return request(
            `/services/whiskerguardapprovalservice/api/approval/processes`,
            params, 'post')
    },
    // 根据流程类型获取审批流程
    getApprovalProcess(processType) {
        return request(
            `/services/whiskerguardapprovalservice/api/approval/processes/processType?processType=${processType}`,
            {}, 'get')
    },
    // 获取合规审查审批流程信息
    getComplianceProcess(params) {
        return request(
            `/services/whiskerguardcontractservice/api/compliance/reviews/process`,
            params, 'get')
    },
    // 
    contractMessageReview(params) {
        return request(
            `${baseUrl}api/contract/message/review`,
            params, 'post')
    },
    otherMessageReview(params) {
        return request(
            `/services/whiskerguardapprovalservice/api/approval/contract/task/complete`,
            params, 'post')
    }
}