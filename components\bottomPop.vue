<template>
	<uv-popup 
		ref="popup" 
		mode="bottom" 
		:round="24"
		:overlay="true"
		:close-on-click-overlay="true"
		:safe-area-inset-bottom="true"
		@maskClick="handleClose"
		@change="handlePopupChange"
	>
		<view class="bottom-pop-content">
			<!-- 拖拽柄 -->
			<view class="drag-handle-container" v-if="showDrag">
				<view @tap="handleClose" class="drag-handle"></view>
			</view>

			<!-- 标题区 -->
			<view class="header" v-if="title">
				<text class="title">{{ title }}</text>
			</view>

			<!-- 内容区 -->
			<scroll-view class="content" scroll-y>
				<view class="content-wrapper">
					<!-- 岗位信息块 -->
					<slot v-if="$slots.default" />
					<template v-else>
						<view class="info-card">
							<text class="card-title">岗位信息</text>
							<text class="info-text">部门：{{ form.orgUnitName }}</text>
							<text class="info-text">岗位：{{  }}</text>
							<text class="info-text">登记日期：{{ form.createdAt }}</text>
						</view>

						<!-- 责任人 & 联系方式块 -->
						<view class="info-card">
							<text class="card-title">责任人 & 联系方式</text>
							<text class="info-text">责任人：{{ form.principalName }}</text>
							<view class="info-row">
								<text class="info-text">手机：{{ form.principalPhone }}</text>
							</view>
							<view class="info-row">
								<text class="info-text">邮箱：{{ form.principalEmail }}</text>
							</view>
						</view>

						<!-- 职责描述块 -->
						<view class="info-card">
							<text class="card-title">职责描述</text>
							<view class="bullet-point">{{ form.basicDuty }}</view>
						</view>

						<!-- 附件列表块 -->
						<view class="info-card">
							<view class="attachment-header">
								<text class="card-title">附件列表 (1)</text>
							</view>
							<view class="divider"></view>
							<view class="attachment-item" v-for="item in form.files" :key="item.fileId">
								<view class="attachment-left">
									<uni-icons type="paperclip" size="20" color="#E53E3E"></uni-icons>
									<text class="attachment-name">{{ item.fileName }}.{{ item.fileName }}</text>
								</view>
								<uni-icons type="right" size="14" color="#9b9b9b"></uni-icons>
							</view>
						</view>
					</template>
				</view>
			</scroll-view>

			<!-- 操作区 -->
			<slot name="footerBtn" />
		</view>
	</uv-popup>
</template>

<script setup>
	import {
		ref,
		watch,
		nextTick
	} from 'vue';

	const props = defineProps({
		visible: {
			type: Boolean,
			required: true
		},
		title: {
			type: String,
			default: ''
		},
		showDrag: {
			type: Boolean,
			default: true
		},
		form: {
			type: Object,
			default: () => ({})
		}
	});

	const emit = defineEmits(['close'])
	const popup = ref(null)

	// 监听visible变化，控制uv-popup的显示隐藏
	watch(() => props.visible, (newVal) => {
		nextTick(() => {
			if (popup.value) {
				if (newVal) {
					popup.value.open()
				} else {
					popup.value.close()
				}
			}
		})
	}, {
		immediate: true
	})

	// uv-popup的change事件处理
	const handlePopupChange = (e) => {
		// 当弹窗关闭时，触发父组件的close事件
		if (!e.show) {
			emit('close')
		}
	}

	// 关闭处理
	const handleClose = () => {
		emit('close')
	}

	const handleBack = () => {
		uni.navigateBack();
	};

	const handleEdit = () => {
		uni.showToast({
			title: '编辑功能',
			icon: 'none'
		});
	};

	const handleDelete = () => {
		uni.showModal({
			title: '提示',
			content: '确定要删除该岗位职责吗？',
			success: (res) => {
				if (res.confirm) {
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			}
		});
	};

	const copyText = (text) => {
		uni.setClipboardData({
			data: text,
			success: () => {
				uni.showToast({
					title: '已复制',
					icon: 'success'
				});
			}
		});
	};
</script>

<style lang="scss" scoped>
	.bottom-pop-content {
		width: 100%;
		height: 80vh;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		background-color: #ffffff;

		.drag-handle-container {
			width: 100%;
			padding-top: 12rpx;
			display: flex;
			justify-content: center;
		}

		.drag-handle {
			width: 64rpx;
			height: 8rpx;
			background-color: #CCCCCC;
			border-radius: 4rpx;
		}

		.header {
			height: 88rpx;
			padding: 0 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
		}

		.title {
			font-size: 16px;
			font-weight: bold;
			color: #4a4a4a;
		}

		.content {
			flex: 1;
			overflow: auto;
			min-height: 0;
		}

		.content-wrapper {
			padding: 0 32rpx;
		}

		.info-card {
			background-color: #ffffff;
			border-radius: 12rpx;
			padding: 32rpx;
			margin-bottom: 24rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		}

		.card-title {
			font-size: 14px;
			font-weight: bold;
			color: #1A73E8;
			margin-bottom: 16rpx;
			display: block;
		}

		.info-text {
			font-size: 14px;
			color: #4a4a4a;
			margin-bottom: 16rpx;
			display: block;
		}

		.info-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
		}

		.copy-button {
			background-color: transparent;
			width: 32rpx;
			height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0;
			padding: 0;
		}

		.bullet-point {
			font-size: 14px;
			color: #4a4a4a;
			margin-bottom: 16rpx;
			display: block;
			word-break: break-all;
		}

		.bullet-point::before {
			content: "•";
			margin-right: 16rpx;
		}

		.attachment-header {
			padding-bottom: 16rpx;
		}

		.divider {
			height: 2rpx;
			background-color: #f0f0f0;
			margin: 0 -32rpx;
		}

		.attachment-item {
			padding: 24rpx 0;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.attachment-left {
			display: flex;
			align-items: center;
		}

		.attachment-name {
			font-size: 14px;
			color: #1A73E8;
			margin-left: 24rpx;
		}
	}
</style>