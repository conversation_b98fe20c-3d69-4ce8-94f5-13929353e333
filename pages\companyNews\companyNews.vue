<template>
	<view class="list-responsibility-container">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<filter-tags v-model="activeIndex" :fixed="false" :tags="tags" />
			</template>
			<view class="pl-32 pr-32">
				<view class="mt-32 flex aic bg-fff br-16 p-24" @click="$tools.routeJump(`/pages/companyNews/detail?id=${i.id}`)"
					v-for="i,j in dataList" :key="j">
					<view>
						<view class="item-title uv-line-1">{{ i.title || i.department }}</view>
						<view class="info-text mt-10 uv-line-1">{{ i.summary || i.department }}</view>
					<view class="info-text mt-10">{{ formatDate(i.publishedAt) || i.date }}</view>
					<view class="info-text mt-10" v-if="i.category">
						<text class="category-tag">{{ i.category.name }}</text>
					</view>
					</view>
					<uv-icon name="arrow-right" color="#999" size="28rpx"></uv-icon>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import {
	ref,
	watch,
} from 'vue';
import filterTags from '@/components/filterTags.vue'
import newsApi from './news.js'
import {
	onLoad
} from '@dcloudio/uni-app'
import { useUserStore } from '@/store/pinia.js';

const userStore = useUserStore();
const activeIndex = ref(null);
const tags = ref([{
	id: 0,
	name: '全部',
	value: null,
}]);
onLoad(() => {
	getNewsCategories()
})
function getNewsCategories(){
	newsApi.getNewsCategories().then(res => {
		res.content.map(item => {
			tags.value.push({
				id: item.id,
				name: item.name,
				value: item.id
			})
		})
	}).catch(err => {
		console.error('获取新闻分类失败', err);
	})
}
// 监听
watch(() => activeIndex.value, (newVal) => {
	paging.value.reload()
}, {
	deep: true,
	// immediate: true
})

const paging = ref(null)
const dataList = ref([])

// 格式化日期
const formatDate = (dateObj) => {
	if (!dateObj) return '';
	if (dateObj.seconds) {
		const date = new Date(dateObj.seconds * 1000);
		return date.toLocaleDateString('zh-CN');
	}
	return dateObj;
}

// 列表查询
function queryList(pageNo, pageSize) {
console.log('查询列表', pageNo, pageSize);
	// 构建请求参数
	const params = {
		page: pageNo - 1,
		size: pageSize,
	};
	if(activeIndex.value){
		params.categoryId = activeIndex.value;
	}
	// 根据筛选条件添加参数
	if (activeIndex.value > 1) {
		// 这里可以根据实际需要添加分类筛选逻辑
		// params.categoryType = tags.value[activeIndex.value - 1].value
	}
	uni.showLoading({
		title: '加载中'
	});
	
	newsApi.getList(params).then((res) => {
		// 根据接口返回的数据结构处理
		let arr = Array.isArray(res) ? res : (res.content || []);
		console.log('新闻列表', arr);
		paging.value.complete(arr);
		uni.hideLoading();
	}).catch((err) => {
		console.error('获取新闻列表失败', err);
		paging.value.complete(false);
		uni.hideLoading();
	})
}
</script>


<style lang="scss" scoped>
.list-responsibility-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #F5F7FA;

	.item-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #1a1a1a;
	}

	.info-text {
		font-size: 24rpx;
		color: #666;
	}
	
	.category-tag {
		background-color: #E6F4FF;
		color: #1A73E8;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 20rpx;
	}
}
</style>