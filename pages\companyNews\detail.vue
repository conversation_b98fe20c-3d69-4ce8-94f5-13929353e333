<template>
	<view>
		<view class="p-32">
			<view class="fw-36 fw-bold" style="color: #111827;">
				{{ newsDetail.title || '新版财务审批制度正式上线' }}
			</view>
			
			<!-- 新闻信息 -->
			<view class="news-info mt-24">
				<view class="info-item" v-if="newsDetail.author">
					<text class="info-label">作者：</text>
					<text class="info-value">{{ newsDetail.author.realName || newsDetail.author.username }}</text>
				</view>
				<view class="info-item" v-if="newsDetail.publishedAt">
					<text class="info-label">发布时间：</text>
					<text class="info-value">{{ formatDate(newsDetail.publishedAt) }}</text>
				</view>
				<view class="info-item" v-if="newsDetail.category">
					<text class="info-label">分类：</text>
					<text class="info-value">{{ newsDetail.category.name }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">阅读量：</text>
					<text class="info-value">{{ newsDetail.viewCount || 0 }}</text>
				</view>
			</view>
			
			<!-- 新闻内容 -->
			<view class="news-content mt-32" v-if="newsDetail.content">
				<mp-html :content="newsDetail.content" />
			</view>
			
			<!-- 附件列表 -->
			<view class="mt-32 bg-fff br-16 p-24" v-if="attachmentList.length > 0">
				<view class="attachment-title mb-16">
					<text class="fw-bold">相关附件</text>
				</view>
				<view class="" v-for="i,j in attachmentList" :key="j">
					<uv-divider v-if="j!=0" lineColor="#999" />
					<view class="flex aic jcsb">
						<view class="f-28 fw-bold">
							<uv-text :lines="1" type="primary" :text="i.fileName || '财务审批制度.pdf'"></uv-text>
						</view>
						<view class="flex aic ml-24" style="color: #3c9cff;">
							<view @click="preview(i)" class="flex aic f-24">
								<text class="wsn">预览</text>
								<uv-icon name="eye" color="#3c9cff" size="28rpx" bold />
							</view>
							<view @click="download(i)" class="flex aic f-24 ml-6">
								<text class="wsn">下载</text>
								<uv-icon name="download" color="#3c9cff" size="28rpx" bold />
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import {
	ref,
	watch,
} from 'vue';
import {
	onLoad
} from '@dcloudio/uni-app'
import newsApi from '@/api/news/news.js'
import uploads from '@/api/upload.js'

const newsDetail = ref({});
const attachmentList = ref([]);
const newsId = ref('');

onLoad((e) => {
	console.log(e, 666)
	newsId.value = e.id;
	if (e.id) {
		getNewsDetail(e.id);
	}
})

// 获取新闻详情
const getNewsDetail = (id) => {
	uni.showLoading({
		title: '加载中'
	});
	
	newsApi.getDetail(id).then(res => {
		newsDetail.value = res;
		// 如果有附件信息，可以在这里处理
		// attachmentList.value = res.attachments || [];
		uni.hideLoading();
	}).catch(err => {
		console.error('获取新闻详情失败', err);
		uni.hideLoading();
		uni.showToast({
			title: '获取详情失败',
			icon: 'none'
		});
	})
}

// 格式化日期
const formatDate = (dateObj) => {
	if (!dateObj) return '';
	if (dateObj.seconds) {
		const date = new Date(dateObj.seconds * 1000);
		return date.toLocaleString('zh-CN');
	}
	return dateObj;
}

// 预览文件
const preview = async (item) => {
	try {
		const fileUrl = await uploads.getFileUrl(item.filePath);
		if (!fileUrl) {
			uni.showToast({
				title: '文件路径不存在',
				icon: 'none'
			});
			return;
		}
		
		const fileType = item.fileType?.toLowerCase();
		
		// 图片预览
		if (['jpg', 'jpeg', 'png', 'gif'].includes(fileType)) {
			uni.previewImage({
				urls: [fileUrl],
				current: fileUrl
			});
		}
		// 文档预览
		else if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileType)) {
			uni.showLoading({ title: '加载中...' });
			uni.downloadFile({
				url: fileUrl,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.hideLoading();
						uni.openDocument({
							filePath: res.tempFilePath,
							showMenu: true
						});
					}
				},
				fail: () => {
					uni.hideLoading();
					uni.showToast({
						title: '预览失败',
						icon: 'none'
					});
				}
			});
		} else {
			uni.showToast({
				title: '该文件类型不支持预览',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('预览失败', error);
		uni.showToast({
			title: '预览失败',
			icon: 'none'
		});
	}
}

// 下载文件
const download = async (item) => {
	try {
		const fileUrl = await uploads.getFileUrl(item.filePath);
		if (!fileUrl) {
			uni.showToast({
				title: '文件路径不存在',
				icon: 'none'
			});
			return;
		}
		
		uni.showLoading({ title: '下载中...' });
		
		// #ifdef MP-WEIXIN
		wx.downloadFile({
			url: fileUrl,
			success: (res) => {
				if (res.statusCode === 200) {
					uni.hideLoading();
					wx.saveFile({
						tempFilePath: res.tempFilePath,
						success: () => {
							uni.showToast({
								title: '文件已保存',
								icon: 'success'
							});
						},
						fail: () => {
							uni.showToast({
								title: '保存失败',
								icon: 'none'
							});
						}
					});
				}
			},
			fail: () => {
				uni.hideLoading();
				uni.showToast({
					title: '下载失败',
					icon: 'none'
				});
			}
		});
		// #endif
		
		// #ifdef H5
		const a = document.createElement('a');
		a.href = fileUrl;
		a.download = item.fileName || '文件';
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		uni.hideLoading();
		// #endif
	} catch (error) {
		console.error('下载失败', error);
		uni.hideLoading();
		uni.showToast({
			title: '下载失败',
			icon: 'none'
		});
	}
}
</script>

<style lang="scss">
page {
	background-color: #f5f5f5;
}
</style>

<style lang="scss" scoped>
.news-info {
	.info-item {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
		
		.info-label {
			font-size: 24rpx;
			color: #666;
			margin-right: 16rpx;
		}
		
		.info-value {
			font-size: 24rpx;
			color: #333;
		}
	}
}

.news-content {
	line-height: 1.6;
	font-size: 28rpx;
	color: #333;
	padding: 20rpx;
}

.attachment-title {
	font-size: 28rpx;
	color: #333;
}
</style>