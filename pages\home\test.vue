
<template>
  <view class="page-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-content">
        <uni-icons type="arrowleft" size="24" color="#666" @click="handleBack"></uni-icons>
        <text class="header-title">合规风险识别清单编辑</text>
      </view>
    </view>

    <!-- 主体内容区 -->
    <scroll-view class="content" scroll-y>
      <view class="form-container">
        <!-- 基础信息区 -->
        <view class="card">
          <view class="card-header">
            <text class="card-title">基础信息</text>
          </view>
          <view class="card-body">
            <!-- 业务类型 -->
            <view class="form-item">
              <view class="form-label">
                <text class="required">*</text>
                <text>业务类型</text>
              </view>
              <picker 
                mode="selector" 
                :range="businessTypeOptions" 
                @change="handleBusinessTypeChange"
              >
                <view class="picker">
                  {{ form.businessType || '请选择业务类型' }}
                </view>
              </picker>
              <input 
                v-if="form.businessType === '其他'" 
                v-model="form.customBusinessType" 
                placeholder="请输入业务类型" 
                class="custom-input"
              />
            </view>

            <!-- 创建人员 -->
            <view class="form-item">
              <view class="form-label">
                <text>创建人员</text>
              </view>
              <input 
                v-model="form.creator" 
                placeholder="张明（合规部）" 
                disabled 
                class="disabled-input"
              />
            </view>
          </view>
        </view>

        <!-- 风险信息区 -->
        <view class="card">
          <view class="card-header">
            <text class="card-title">风险信息</text>
            <text class="tag">智能生成</text>
          </view>
          <view class="card-body">
            <!-- 合规风险描述点 -->
            <view class="form-item">
              <view class="form-label-row">
                <text>合规风险描述点</text>
                <text 
                  class="regenerate-btn" 
                  :class="{disabled: !form.riskDescription}"
                  @click="regenerate('riskDescription')"
                >
                  重新生成
                </text>
              </view>
              <textarea 
                v-model="form.riskDescription" 
                disabled 
                class="disabled-textarea"
              ></textarea>
            </view>

            <!-- 风险产生原因 -->
            <view class="form-item">
              <view class="form-label-row">
                <text>风险产生原因</text>
                <text 
                  class="regenerate-btn" 
                  :class="{disabled: !form.riskReason}"
                  @click="regenerate('riskReason')"
                >
                  重新生成
                </text>
              </view>
              <textarea 
                v-model="form.riskReason" 
                disabled 
                class="disabled-textarea"
              ></textarea>
            </view>

            <!-- 风险发生后果 -->
            <view class="form-item">
              <view class="form-label-row">
                <text>风险发生后果</text>
                <text 
                  class="regenerate-btn" 
                  :class="{disabled: !form.riskConsequence}"
                  @click="regenerate('riskConsequence')"
                >
                  重新生成
                </text>
              </view>
              <textarea 
                v-model="form.riskConsequence" 
                disabled 
                class="disabled-textarea"
              ></textarea>
            </view>
          </view>
        </view>

        <!-- 合规义务信息区 -->
        <view class="card">
          <view class="card-header">
            <text class="card-title">合规义务</text>
            <text class="tag">智能生成</text>
          </view>
          <view class="card-body">
            <!-- 合规依据卡片 -->
            <view 
              v-for="(item, index) in form.complianceBasis" 
              :key="index" 
              class="compliance-item"
            >
              <view class="compliance-content">
                <text class="compliance-name">{{ item.name }}</text>
                <view class="compliance-meta">
                  <text class="compliance-type">{{ item.type }}</text>
                  <text class="compliance-issuer">{{ item.issuer }}</text>
                  <text class="compliance-date">{{ item.effectiveDate }}</text>
                </view>
              </view>
              <uni-icons 
                type="close" 
                size="20" 
                color="#999" 
                @click="removeComplianceBasis(index)"
              ></uni-icons>
            </view>

            <view class="add-compliance" @click="showAddComplianceDialog">
              <uni-icons type="plus" size="16" color="#1890ff"></uni-icons>
              <text>添加更多依据</text>
            </view>
          </view>
        </view>

        <!-- 管理信息区 -->
        <view class="card">
          <view class="card-header">
            <text class="card-title">管理信息</text>
            <text class="tag">智能生成</text>
          </view>
          <view class="card-body">
            <!-- 风险控制措施 -->
            <view class="form-item">
              <view class="form-label-row">
                <text>风险控制措施</text>
                <text 
                  class="regenerate-btn" 
                  :class="{disabled: !form.controlMeasures}"
                  @click="regenerate('controlMeasures')"
                >
                  重新生成
                </text>
              </view>
              <textarea 
                v-model="form.controlMeasures" 
                disabled 
                class="disabled-textarea"
              ></textarea>
            </view>

            <!-- 归口部门 -->
            <view class="form-item">
              <view class="form-label-row">
                <text>归口部门</text>
                <text class="edit-btn" @click="editField('responsibleDepartment')">
                  修改
                </text>
              </view>
              <input 
                v-model="form.responsibleDepartment" 
                disabled 
                class="disabled-input"
              />
            </view>

            <!-- 配合部门 -->
            <view class="form-item">
              <view class="form-label-row">
                <text>配合部门</text>
                <text class="edit-btn" @click="editField('supportingDepartments')">
                  修改
                </text>
              </view>
              <view class="departments-container">
                <view 
                  v-for="(dept, index) in form.supportingDepartments" 
                  :key="index" 
                  class="department-tag"
                >
                  <text>{{ dept }}</text>
                  <uni-icons 
                    type="close" 
                    size="14" 
                    color="#999" 
                    @click.stop="removeSupportingDepartment(index)"
                  ></uni-icons>
                </view>
              </view>
            </view>

            <!-- 风险等级 -->
            <view class="form-item">
              <view class="form-label">
                <text>风险等级</text>
              </view>
              <view class="risk-level">
                <view 
                  class="level-dot" 
                  :class="{
                    'high': form.riskLevel === '高风险',
                    'medium': form.riskLevel === '中风险',
                    'low': form.riskLevel === '低风险'
                  }"
                ></view>
                <text class="level-text">{{ form.riskLevel }}</text>
              </view>
              <text class="risk-assessment">{{ form.riskAssessmentBasis }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="footer">
      <button class="btn draft" @click="handleSaveDraft">保存草稿</button>
      <button class="btn generate" @click="handleGenerate">风险智能识别</button>
      <button class="btn submit" @click="handleSubmit">提交审核</button>
    </view>

    <!-- AI生成进度弹窗 -->
    <uni-popup ref="generatePopup" type="dialog">
      <view class="generate-popup">
        <text class="popup-title">猫伯伯智能生成中</text>
        <view class="steps-container">
          <view 
            v-for="(step, index) in generateSteps" 
            :key="index" 
            class="step-item"
          >
            <uni-icons 
              :type="step.completed ? 'checkmarkempty' : step.active ? 'refresh' : 'circle'" 
              size="16" 
              :color="step.active ? '#1890ff' : step.completed ? '#1890ff' : '#999'"
            ></uni-icons>
            <text 
              :class="{
                'active': step.active,
                'completed': step.completed
              }"
            >
              {{ step.text }}
            </text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 添加合规依据弹窗 -->
    <uni-popup ref="compliancePopup" type="dialog">
      <view class="compliance-popup">
        <text class="popup-title">添加合规依据</text>
        <input 
          v-model="complianceSearch" 
          placeholder="搜索合规依据" 
          class="search-input"
        />
        <scroll-view class="compliance-list" scroll-y>
          <view 
            v-for="(item, index) in filteredComplianceList" 
            :key="index" 
            class="compliance-option"
            @click="selectCompliance(item)"
          >
            <view class="option-content">
              <text class="option-name">{{ item.name }}</text>
              <text class="option-meta">{{ item.issuer }} · {{ item.effectiveDate }}</text>
            </view>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 表单数据
const form = ref({
  businessType: '',
  customBusinessType: '',
  creator: '张明（合规部）',
  riskDescription: '',
  riskReason: '',
  riskConsequence: '',
  complianceBasis: [],
  controlMeasures: '',
  responsibleDepartment: '',
  supportingDepartments: [],
  riskLevel: '中风险',
  riskAssessmentBasis: '根据历史数据分析，此类业务风险发生概率为中等，影响程度为中等'
});

// 业务类型选项
const businessTypeOptions = ref([
  '信访管理', '财务管理', '人事管理', '采购管理', '合同管理', '安全管理', '其他'
]);

// AI生成相关状态
const generatePopup = ref();
const currentGenerateStep = ref(0);
const generateSteps = ref([
  { text: '正在分析业务类型...', active: false, completed: false },
  { text: '正在检索相关法规...', active: false, completed: false },
  { text: '正在识别风险点...', active: false, completed: false },
  { text: '正在生成控制措施...', active: false, completed: false },
  { text: '生成完成！', active: false, completed: false }
]);

// 合规依据相关
const compliancePopup = ref();
const complianceSearch = ref('');
const complianceList = ref([
  { name: '中华人民共和国合同法', type: '法律法规', issuer: '全国人大', effectiveDate: '1999-10-01' },
  { name: '企业内部控制基本规范', type: '监管规定', issuer: '财政部', effectiveDate: '2009-07-01' },
  { name: '公司采购管理办法', type: '规章制度', issuer: '公司总部', effectiveDate: '2020-01-01' },
  { name: '安全生产法', type: '法律法规', issuer: '全国人大', effectiveDate: '2014-12-01' },
  { name: '劳动法', type: '法律法规', issuer: '全国人大', effectiveDate: '1995-01-01' },
  { name: '公司财务管理制度', type: '规章制度', issuer: '公司总部', effectiveDate: '2018-05-01' }
]);

const filteredComplianceList = computed(() => {
  return complianceList.value.filter(item =>
    item.name.includes(complianceSearch.value) ||
    item.issuer.includes(complianceSearch.value)
  );
});

// 业务类型变化处理
const handleBusinessTypeChange = (e) => {
  const value = businessTypeOptions.value[e.detail.value];
  form.value.businessType = value;
  if (value !== '其他') {
    form.value.customBusinessType = '';
  }
};

// 重新生成内容
const regenerate = (field) => {
  if (!form.value[field]) return;
  
  form.value[field] = `这是系统根据业务类型"${form.value.businessType || '未选择'}"智能生成的${getFieldName(field)}内容。`;
};

const getFieldName = (field) => {
  const map = {
    riskDescription: '合规风险描述点',
    riskReason: '风险产生原因',
    riskConsequence: '风险发生后果',
    controlMeasures: '风险控制措施'
  };
  return map[field] || field;
};

// 删除合规依据
const removeComplianceBasis = (index) => {
  form.value.complianceBasis.splice(index, 1);
};

// 显示添加合规依据弹窗
const showAddComplianceDialog = () => {
  complianceSearch.value = '';
  compliancePopup.value.open();
};

// 选择合规依据
const selectCompliance = (item) => {
  if (!form.value.complianceBasis.some(b => b.name === item.name)) {
    form.value.complianceBasis.push({ ...item });
  }
  compliancePopup.value.close();
};

// 删除配合部门
const removeSupportingDepartment = (index) => {
  form.value.supportingDepartments.splice(index, 1);
};

// 编辑字段
const editField = (field) => {
  const map = {
    responsibleDepartment: {
      title: '修改归口部门',
      value: form.value.responsibleDepartment,
      placeholder: '请输入归口部门'
    },
    supportingDepartments: {
      title: '修改配合部门',
      value: form.value.supportingDepartments.join(','),
      placeholder: '请输入配合部门，多个部门用逗号分隔'
    }
  };
  
  const config = map[field];
  if (!config) return;
  
  uni.showModal({
    title: config.title,
    content: config.placeholder,
    editable: true,
    placeholderText: config.value,
    success: (res) => {
      if (res.confirm && res.content) {
        if (field === 'responsibleDepartment') {
          form.value.responsibleDepartment = res.content;
        } else if (field === 'supportingDepartments') {
          form.value.supportingDepartments = res.content.split(',').map(s => s.trim()).filter(s => s);
        }
      }
    }
  });
};

// 智能生成处理
const handleGenerate = () => {
  if (!form.value.businessType) {
    uni.showToast({
      title: '请先选择业务类型',
      icon: 'none'
    });
    return;
  }
  
  generatePopup.value.open();
  currentGenerateStep.value = 0;
  generateSteps.value.forEach(step => {
    step.active = false;
    step.completed = false;
  });
  
  // 模拟AI生成过程
  const timer = setInterval(() => {
    if (currentGenerateStep.value > 0) {
      generateSteps.value[currentGenerateStep.value - 1].active = false;
      generateSteps.value[currentGenerateStep.value - 1].completed = true;
    }
    
    if (currentGenerateStep.value < generateSteps.value.length) {
      generateSteps.value[currentGenerateStep.value].active = true;
      currentGenerateStep.value++;
    } else {
      clearInterval(timer);
      setTimeout(() => {
        generatePopup.value.close();
        // 填充生成的内容
        fillGeneratedData();
      }, 1000);
    }
  }, 800);
};

// 填充生成的数据
const fillGeneratedData = () => {
  const businessType = form.value.businessType === '其他'
    ? form.value.customBusinessType
    : form.value.businessType;
    
  form.value.riskDescription = `在${businessType}业务中，可能存在以下合规风险：1. 未按规定程序操作；2. 文件记录不完整；3. 审批流程不规范。`;
  form.value.riskReason = `1. 操作人员对相关规定不了解；2. 内部监督机制不健全；3. 业务流程设计存在缺陷。`;
  form.value.riskConsequence = `1. 可能导致违规处罚；2. 影响企业声誉；3. 造成经济损失；4. 引发法律纠纷。`;
  form.value.complianceBasis = [
    { name: `${businessType}相关规定`, type: '规章制度', issuer: '公司总部', effectiveDate: '2022-01-01' },
    { name: '企业内部控制基本规范', type: '监管规定', issuer: '财政部', effectiveDate: '2009-07-01' }
  ];
  form.value.controlMeasures = `1. 制定详细的${businessType}操作手册；2. 定期开展合规培训；3. 建立双重审核机制；4. 实施定期检查制度。`;
  form.value.responsibleDepartment = `${businessType}部门`;
  form.value.supportingDepartments = ['法务部', '审计部', '监察部'];
  form.value.riskLevel = businessType === '安全管理' ? '高风险' : '中风险';
  form.value.riskAssessmentBasis = `根据历史数据分析，${businessType}业务风险发生概率为中等，影响程度为${businessType === '安全管理' ? '高' : '中等'}`;
};

// 返回处理
const handleBack = () => {
  uni.showModal({
    title: '提示',
    content: '确定要离开吗？未保存的更改将会丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack();
      }
    }
  });
};

// 保存草稿
const handleSaveDraft = () => {
  uni.showToast({
    title: '保存成功',
    icon: 'success'
  });
};

// 提交处理
const handleSubmit = () => {
  if (!form.value.businessType) {
    uni.showToast({
      title: '请先选择业务类型',
      icon: 'none'
    });
    return;
  }
  
  uni.showModal({
    title: '提示',
    content: '确认提交审核？提交后不可修改。',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        });
        // 实际项目中应该是提交到后端
        console.log('提交数据:', form.value);
      }
    }
  });
};

onLoad(() => {
  // 初始化表单数据
  form.value.creator = '张明（合规部）';
});
</script>

<style>
page {
  height: 100%;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

/* 头部样式 */
.header {
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

/* 内容区域样式 */
.content {
  flex: 1;
  padding-top: 88rpx;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.form-container {
  padding: 20rpx;
}

.card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.card-body {
  padding: 24rpx 30rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.form-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.required {
  color: #ff4d4f;
  margin-right: 8rpx;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #333;
}

.custom-input,
.disabled-input {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.disabled-textarea {
  height: 160rpx;
  padding: 16rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  font-size: 28rpx;
}

.regenerate-btn {
  font-size: 24rpx;
  color: #1890ff;
}

.regenerate-btn.disabled {
  color: #ccc;
}

.edit-btn {
  font-size: 24rpx;
  color: #1890ff;
}

.compliance-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.compliance-item:last-child {
  border-bottom: none;
}

.compliance-content {
  flex: 1;
}

.compliance-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.compliance-meta {
  margin-top: 12rpx;
  display: flex;
  flex-wrap: wrap;
}

.compliance-type {
  font-size: 22rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.compliance-issuer,
.compliance-date {
  font-size: 22rpx;
  color: #999;
  margin-right: 12rpx;
}

.add-compliance {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 28rpx;
  padding: 24rpx 0;
}

.departments-container {
  display: flex;
  flex-wrap: wrap;
  background-color: #f5f5f5;
  padding: 16rpx;
  border-radius: 8rpx;
}

.department-tag {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 40rpx;
  padding: 8rpx 16rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.department-tag text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.risk-level {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.level-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.level-dot.high {
  background-color: #ff4d4f;
}

.level-dot.medium {
  background-color: #faad14;
}

.level-dot.low {
  background-color: #52c41a;
}

.level-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.risk-assessment {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作栏样式 */
.footer {
  height: 120rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 0 40rpx;
  margin: 0;
}

.btn.draft {
  background-color: #fff;
  color: #666;
  border: 1px solid #ddd;
}

.btn.generate {
  background-color: #1890ff;
  color: #fff;
}

.btn.submit {
  background-color: #52c41a;
  color: #fff;
}

/* 弹窗样式 */
.generate-popup,
.compliance-popup {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  width: 600rpx;
  max-width: 80vw;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  display: block;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-item text {
  font-size: 28rpx;
  color: #999;
  margin-left: 16rpx;
}

.step-item text.active {
  color: #1890ff;
  font-weight: 500;
}

.step-item text.completed {
  color: #1890ff;
}

.search-input {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}

.compliance-list {
  max-height: 600rpx;
}

.compliance-option {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.compliance-option:last-child {
  border-bottom: none;
}

.option-content {
  flex: 1;
}

.option-name {
  font-size: 28rpx;
  color: #333;
}

.option-meta {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}
</style>

