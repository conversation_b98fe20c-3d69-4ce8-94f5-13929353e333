import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}

const baseUrl = `/services/whiskerguardorgservice/api/news/`

export default {
    // 获取新闻列表
    getList(params) {
        return request(baseUrl + 'list', params, 'get')
    },
    // 获取新闻详情（基于列表接口推测的详情接口）
    getDetail(id) {
        return request(baseUrl + `detail/${id}`, {}, 'get')
    },
    // 获取新闻详情
    getNewsCategories() {
        return request(baseUrl + `categories/list`, {}, 'get')
    },
}