<template>
	<view class="report-detail-container">
		<view class="content">
			<!-- 第一张卡片：基本信息 -->
			<view class="info-card">
				<view class="info-item">
					<text class="info-label">举报编号：</text>
					<text class="info-value">{{ reportDetail.reportNo || reportDetail.id || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">举报类型：</text>
					<uv-button type="primary" size="mini" :plain="true">{{ getViolationTypeText(reportDetail.violationType) }}</uv-button>
				</view>
				<view class="info-item">
					<text class="info-label">当前状态：</text>
					<uv-button type="primary" size="mini" :plain="true" :customStyle="getStatusButtonStyle(reportDetail.status)">{{ getStatusText(reportDetail.status) }}</uv-button>
				</view>
				<view class="info-item">
					<text class="info-label">是否匿名：</text>
					<text class="info-value">{{ reportDetail.isAnonymous ? '是' : '否' }}</text>
				</view>
			</view>

			<!-- 第二张卡片：详情内容 -->
			<view class="info-card">
				<view class="detail-title">
					<text class="title-text">{{ reportDetail.title || '举报详情' }}</text>
				</view>
				<view class="detail-time">
					<text class="time-text">{{ reportDetail.createdAt }}</text>
				</view>
				<view class="detail-content">
					<text class="detail-text">{{ reportDetail.detail || '暂无详情' }}</text>
				</view>
			</view>

			<!-- 第三张卡片：附件 -->
			<view class="info-card">
				<view class="attachment-header">
					<text class="chapter-title">附件 ({{ reportDetail.attachmentList?.length || 0 }})</text>
				</view>
				<view class="divider"></view>
				<view class="attachment-item" v-for="item in reportDetail.attachmentList" :key="item.id">
					<view class="attachment-left">
						<text class="attachment-name" style="margin-left: 0;">{{ item.fileName || '未命名文件' }}</text>
						<text class="attachment-size">{{ item.fileSize || '未知大小' }}</text>
					</view>
					<view class="attachment-actions">
						<FilePreview :item="item" />
						<FileDownload :item="item" />
					</view>
				</view>
				<view class="empty-attachment" v-if="!reportDetail.attachmentList || reportDetail.attachmentList.length === 0">
					<text class="empty-text">暂无附件</text>
				</view>
			</view>

			<!-- 举报处理按钮 -->
			<view class="action-button">
				<button class="handle-btn" @click="handleReport">举报处理</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import https from '@/api/violation/report.js';
import uploads from '@/api/upload.js';
import getDictData from '@/utils/dict.js';
import FilePreview from '@/components/FilePreview/FilePreview.vue';
import FileDownload from '@/components/FileDownload/FileDownload.vue';

// 页面参数
const reportId = ref('');
const reportDetail = ref({});
const loading = ref(false);

// 字典数据
const dictData = ref([]);

// 页面加载
onLoad((options) => {
	if (options.id) {
		reportId.value = options.id;
		loadReportDetail();
	}
});

// 获取字典数据
onMounted(async () => {
	try {
		const res = await getDictData('45');
		if (res && res.length > 0) {
			dictData.value = res;
		}
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
});

// 加载举报详情
const loadReportDetail = async () => {
	if (!reportId.value) return;
	
	loading.value = true;
	uni.showLoading({ title: '加载中...' });
	
	try {
		const res = await https.getDetail(reportId.value);
		reportDetail.value = res;
	} catch (error) {
		console.error('加载举报详情失败:', error);
		uni.showToast({
			title: '加载失败',
			icon: 'error'
		});
	} finally {
		loading.value = false;
		uni.hideLoading();
	}
};

// 根据violationType映射文字
const getViolationTypeText = (violationType) => {
	if (!violationType || !dictData.value.length) {
		return violationType || '未知类型';
	}
	const item = dictData.value.find(dict => dict.value === violationType);
	return item ? item.name : violationType;
};

// 获取状态文字
const getStatusText = (status) => {
	switch (status) {
		case 'PENDING':
			return '待处理';
		case 'REPLIED':
			return '已回复';
		case 'CLOSED':
			return '已关闭';
		default:
			return '未知状态';
	}
};

// 获取状态按钮样式
const getStatusButtonStyle = (status) => {
	switch (status) {
		case 'PENDING':
			return {
				backgroundColor: '#FFEDD5',
				color: '#dd6b20',
				borderColor: '#dd6b20'
			};
		case 'REPLIED':
			return {
				backgroundColor: '#DCFCE7',
				color: '#16a34a',
				borderColor: '#16a34a'
			};
		case 'CLOSED':
			return {
				backgroundColor: '#F3F4F6',
				color: '#6b7280',
				borderColor: '#6b7280'
			};
		default:
			return {
				backgroundColor: '#F3F4F6',
				color: '#6b7280',
				borderColor: '#6b7280'
			};
	}
};

// 举报处理
const handleReport = () => {
	try {
		// 只传入id参数
		const id = reportDetail.value.id || reportId.value;
		
		uni.navigateTo({
			url: `/pages/component/continuousOpt/processReport?id=${id}`
		});
		
	} catch (error) {
		console.error('处理举报跳转失败:', error);
		uni.showToast({
			title: '跳转失败',
			icon: 'error'
		});
	}
};
</script>

<style lang="scss" scoped>
.report-detail-container {
	height: 100vh;
	background-color: #f5f7fa;
}

.content {
	padding: 20rpx;
}

.info-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.info-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
	flex-shrink: 0;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	

}

.detail-title {
	margin-bottom: 16rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.detail-time {
	margin-bottom: 24rpx;
}

.time-text {
	font-size: 24rpx;
	color: #999;
}

.detail-content {
	line-height: 1.6;
}

.detail-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.attachment-header {
	margin-bottom: 16rpx;
}

.chapter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.divider {
	height: 1rpx;
	background-color: #f0f0f0;
	margin-bottom: 24rpx;
}

.attachment-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.attachment-left {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.attachment-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	word-break: break-all;
}

.attachment-size {
	font-size: 24rpx;
	color: #999;
}

.attachment-actions {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.empty-attachment {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.action-button {
	padding: 0 20rpx 40rpx;
}

.handle-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16rpx;
	border: none;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&:active {
		opacity: 0.8;
	}
}
</style>