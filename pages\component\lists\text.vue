<template>
<view class="container">
<!-- 顶部导航栏 -->
<view class="nav-bar">
<uni-icons type="arrowleft" size="24" color="#333" @click="handleBack" />
<text class="nav-title">关键业务流程管控详情</text>
<view style="width: 24px;"></view>
</view>
<!-- 主体内容区 -->
<scroll-view class="content" scroll-y>
<!-- 状态标识区 -->
<view class="card status-card">
<view class="status-tag active">已生效</view>
<view class="create-info">
<text>创建人：张明（合规部）</text>
<text class="divider">|</text>
<text>创建时间：2024-01-15 14:30</text>
</view>
</view>
<!-- 业务流程区 -->
<view class="card">
<view class="section-title">业务流程区</view>
<view class="business-process">
<view class="process-item">
<text class="process-label">业务领域：</text>
<text class="process-value">合同管理</text>
</view>
<view class="process-item">
<text class="process-label">业务流程：</text>
<text class="process-value">重大合同审批流程：申请人提交合同→部门初审→法务审核→财务审核→总经理审批→董事长审批（500万以上）→合同签订→执行跟踪→归档管理</text>
</view>
</view>
</view>
<!-- 管控环节 -->
<view class="card">
<view class="section-title">管控环节</view>
<!-- 环节1 -->
<view class="control-section">
<view class="control-title">合同提交环节</view>
<view class="department-tags">
<text class="tag">业务部</text>
<text class="tag">法务部</text>
</view>
<view class="control-item">
<text class="control-label">节点描述：</text>
<text class="control-value">合同提交前初步审查，确保基本信息完整</text>
</view>
<view class="control-item">
<text class="control-label">审批部门/审批人：</text>
<text class="control-value">财务部 李华</text>
</view>
<view class="control-item">
<text class="control-label">总经理审批：</text>
<text class="control-value">金额超过200万</text>
</view>
<view class="control-item">
<text class="control-label">董事长审批：</text>
<text class="control-value">金额超过500万</text>
</view>
<view class="control-item">
<text class="control-label">风险点：</text>
<text class="control-value">合同信息不完整、提交流程不规范</text>
</view>
<view class="control-item">
<text class="control-label">风险描述：</text>
<text class="control-value">在合同提交过程中，可能存在合同条款不完整、金额填写错误、未按规定流程提交等风险，导致后续审核延误</text>
</view>
<view class="control-item">
<text class="control-label">合规要求：</text>
<text class="control-value">严格按照合同管理制度提交，确保合同条款完整，金额准确，附件齐全</text>
</view>
<view class="control-item">
<text class="control-label">合规依据：</text>
<text class="control-value">《合同法》颁布单位：全国人大常委会，效力层级：法律法规，实施时间：1999-10-01</text>
</view>
<view class="control-item">
<text class="control-label">管控措施：</text>
<text class="control-value">1.建立合同提交清单 2.设置必填项校验 3.双人复核机制 4.提交后自动通知相关审核人员</text>
</view>
<view class="control-item">
<text class="control-label">责任部门/责任岗位：</text>
<text class="control-value">业务部 业务经理</text>
</view>
</view>
<!-- 环节2 -->
<view class="control-section">
<view class="control-title">法务审核环节</view>
<view class="department-tags">
<text class="tag">法务部</text>
<text class="tag">财务部</text>
</view>
<view class="control-item">
<text class="control-label">节点描述：</text>
<text class="control-value">进行合同条款合规性审查，重点关注法律风险</text>
</view>
<view class="control-item">
<text class="control-label">审批部门/审批人：</text>
<text class="control-value">法务部 王律师</text>
</view>
<view class="control-item">
<text class="control-label">总经理审批：</text>
<text class="control-value">合同条款重大变更时</text>
</view>
<view class="control-item">
<text class="control-label">董事长审批：</text>
<text class="control-value">涉及重大法律风险时</text>
</view>
<view class="control-item">
<text class="control-label">风险点：</text>
<text class="control-value">合同条款合规性风险、法律责任界定不清</text>
</view>
<view class="control-item">
<text class="control-label">风险描述：</text>
<text class="control-value">法务审核过程中可能遗漏重要法律条款，对合同风险评估不准确，导致企业承担不必要的法律责任</text>
</view>
<view class="control-item">
<text class="control-label">合规要求：</text>
<text class="control-value">全面审查合同条款，重点关注违约责任、争议解决、知识产权等条款</text>
</view>
<view class="control-item">
<text class="control-label">合规依据：</text>
<text class="control-value">《企业合同管理制度》颁布单位：公司，效力层级：企业制度，实施时间：2023-01-01</text>
</view>
<view class="control-item">
<text class="control-label">管控措施：</text>
<text class="control-value">1.建立合同审核标准模板 2.重大条款变更必须书面说明 3.建立法务审核档案 4.定期培训更新法律知识</text>
</view>
<view class="control-item">
<text class="control-label">责任部门/责任岗位：</text>
<text class="control-value">法务部 法务经理</text>
</view>
</view>
</view>
<!-- 工作文档 -->
<view class="card">
<view class="section-title">工作文档</view>
<view class="document-list">
<view class="document-item">
<uni-icons type="paperclip" size="20" color="#666" />
<text class="document-name">合同模板.pdf</text>
<text class="document-size">2.5MB</text>
<text class="download-btn" @click="downloadFile('合同模板.pdf')">下载</text>
</view>
<view class="document-item">
<uni-icons type="paperclip" size="20" color="#666" />
<text class="document-name">项目计划书.docx</text>
<text class="document-size">1.8MB</text>
<text class="download-btn" @click="downloadFile('项目计划书.docx')">下载</text>
</view>
<view class="document-item">
<uni-icons type="paperclip" size="20" color="#666" />
<text class="document-name">审批流程图.png</text>
<text class="document-size">0.5MB</text>
<text class="download-btn" @click="downloadFile('审批流程图.png')">下载</text>
</view>
</view>
</view>
<!-- 操作历史 -->
<view class="card">
<view class="section-title">
<text>操作历史</text>
<text class="toggle-btn" @click="toggleHistory">{{ showHistory ? '收起' : '展开' }}</text>
</view>
<view class="history-timeline" v-if="showHistory">
<view class="timeline-item">
<view class="timeline-line"></view>
<view class="timeline-dot modify"></view>
<view class="timeline-content">
<view class="timeline-header">
<text class="timeline-time">2024-01-20 09:15</text>
<text class="timeline-operation modify">修改</text>
</view>
<view class="timeline-info">
<view class="timeline-person-row">
<text class="timeline-person">李华（合规部）</text>
</view>
<text class="timeline-desc">更新了风险管控措施和责任部门时间</text>
</view>
</view>
</view>
<view class="timeline-item">
<view class="timeline-line"></view>
<view class="timeline-dot review"></view>
<view class="timeline-content">
<view class="timeline-header">
<text class="timeline-time">2024-01-18 16:45</text>
<text class="timeline-operation review">审核</text>
</view>
<view class="timeline-info">
<view class="timeline-person-row">
<text class="timeline-person">王总</text>
</view>
<text class="timeline-desc">审核通过</text>
</view>
</view>
</view>
<view class="timeline-item">
<view class="timeline-line"></view>
<view class="timeline-dot create"></view>
<view class="timeline-content">
<view class="timeline-header">
<text class="timeline-time">2024-01-15 14:30</text>
<text class="timeline-operation create">创建</text>
</view>
<view class="timeline-info">
<view class="timeline-person-row">
<text class="timeline-person">张明（合规部）</text>
</view>
<text class="timeline-desc">创建了新的合规风险识别清单</text>
</view>
</view>
</view>
</view>
</view>
</scroll-view>
<!-- 底部操作栏 -->
<view class="action-bar">
<button class="edit-btn" @click="handleEdit">编辑</button>
<button class="export-btn" @click="handleExport">导出</button>
</view>
</view>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
const showHistory = ref(true);
const toggleHistory = () => {
showHistory.value = !showHistory.value;
};
const handleBack = () => {
uni.navigateBack();
};
const handleEdit = () => {
// 检查权限逻辑
const hasPermission = true; // 实际应用中应从权限系统获取
if (hasPermission) {
uni.navigateTo({
url: '/pages/edit/index'
});
} else {
uni.showToast({
title: '您没有编辑权限',
icon: 'none'
});
}
};
const handleExport = () => {
uni.showLoading({
title: '正在生成...'
});
// 模拟PDF生成过程
setTimeout(() => {
uni.hideLoading();
uni.showModal({
title: '导出成功',
content: '文档已生成，是否立即下载？',
confirmText: '下载',
cancelText: '取消',
success: (res) => {
if (res.confirm) {
// 实际应用中这里应该是下载PDF的逻辑
uni.showToast({
title: '开始下载',
icon: 'success'
});
}
}
});
}, 1500);
};
const downloadFile = (fileName: string) => {
uni.showToast({
title: `开始下载${fileName}`,
icon: 'none'
});
};
</script>
<style>
page {
height: 100%;
}
.container {
display: flex;
flex-direction: column;
height: 100%;
background-color: #f5f5f5;
}
.nav-bar {
height: 88rpx;
display: flex;
align-items: center;
justify-content: space-between;
padding: 0 32rpx;
background-color: #fff;
border-bottom: 1px solid #eee;
}
.nav-title {
font-size: 16px;
font-weight: 500;
color: #333;
}
.content {
flex: 1;
overflow: auto;
padding: 24rpx 32rpx;
}
.card {
background-color: #fff;
border-radius: 12rpx;
padding: 32rpx;
margin-bottom: 24rpx;
box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.status-card {
display: flex;
flex-direction: column;
align-items: flex-start;
}
.status-tag {
padding: 4rpx 16rpx;
border-radius: 4px;
font-size: 12px;
margin-bottom: 16rpx;
}
.status-tag.active {
background-color: #e8f5e9;
color: #4caf50;
}
.create-info {
display: flex;
align-items: center;
font-size: 12px;
color: #999;
}
.divider {
margin: 0 16rpx;
color: #ddd;
}
.section-title {
font-size: 16px;
font-weight: 500;
color: #333;
margin-bottom: 24rpx;
display: flex;
justify-content: space-between;
}
.business-process {
display: flex;
flex-direction: column;
}
.process-item {
display: flex;
margin-bottom: 16rpx;
}
.process-label {
font-size: 14px;
color: #666;
min-width: 120rpx;
}
.process-value {
font-size: 14px;
color: #333;
flex: 1;
}
.control-section {
margin-bottom: 32rpx;
padding-bottom: 32rpx;
border-bottom: 1px dashed #eee;
}
.control-section:last-child {
margin-bottom: 0;
padding-bottom: 0;
border-bottom: none;
}
.control-title {
font-size: 15px;
font-weight: 500;
color: #333;
margin-bottom: 16rpx;
}
.department-tags {
display: flex;
flex-wrap: wrap;
margin-bottom: 16rpx;
}
.tag {
font-size: 12px;
color: #1890ff;
background-color: #e6f7ff;
padding: 4rpx 16rpx;
border-radius: 4px;
margin-right: 12rpx;
margin-bottom: 12rpx;
}
.control-item {
display: flex;
margin-bottom: 16rpx;
}
.control-label {
font-size: 14px;
color: #666;
min-width: 140rpx;
}
.control-value {
font-size: 14px;
color: #333;
flex: 1;
}
.document-list {
display: flex;
flex-direction: column;
}
.document-item {
display: flex;
align-items: center;
padding: 20rpx 0;
border-bottom: 1px solid #f0f0f0;
}
.document-item:last-child {
border-bottom: none;
}
.document-name {
font-size: 14px;
color: #333;
margin-left: 16rpx;
flex: 1;
}
.document-size {
font-size: 12px;
color: #999;
margin-right: 24rpx;
}
.download-btn {
font-size: 14px;
color: #1890ff;
}
.history-timeline {
padding: 24rpx 0;
position: relative;
}
.timeline-item {
position: relative;
padding-left: 48rpx;
margin-bottom: 48rpx;
}
.timeline-item:last-child {
margin-bottom: 0;
}
.timeline-item:last-child .timeline-line {
display: none;
}
.timeline-line {
position: absolute;
left: 16rpx;
top: 24rpx;
bottom: -48rpx;
width: 2rpx;
background-color: #e8e8e8;
}
.timeline-dot {
position: absolute;
left: 8rpx;
top: 16rpx;
width: 18rpx;
height: 18rpx;
border-radius: 50%;
}
.timeline-dot.create {
background-color: #52c41a;
}
.timeline-dot.modify {
background-color: #1890ff;
}
.timeline-dot.review {
background-color: #faad14;
}
.timeline-content {
background-color: #f8f8f8;
border-radius: 8rpx;
padding: 24rpx;
}
.timeline-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 16rpx;
}
.timeline-time {
font-size: 12px;
color: #999;
}
.timeline-operation {
font-size: 12px;
padding: 2rpx 12rpx;
border-radius: 4rpx;
}
.timeline-operation.create {
background-color: #f6ffed;
color: #52c41a;
}
.timeline-operation.modify {
background-color: #e6f7ff;
color: #1890ff;
}
.timeline-operation.review {
background-color: #fff7e6;
color: #faad14;
}
.timeline-info {
display: flex;
flex-direction: column;
}
.timeline-person-row {
display: flex;
align-items: center;
margin-bottom: 8rpx;
}
.timeline-person {
font-size: 14px;
color: #333;
margin-right: 16rpx;
}
.timeline-desc {
font-size: 14px;
color: #666;
line-height: 1.4;
}
.toggle-btn {
font-size: 14px;
color: #1890ff;
}
.action-bar {
height: 100rpx;
display: flex;
align-items: center;
justify-content: space-between;
padding: 0 32rpx;
background-color: #fff;
border-top: 1px solid #eee;
}
.edit-btn {
width: 48%;
background-color: #1890ff;
color: #fff;
border-radius: 8rpx;
font-size: 16px;
height: 80rpx;
line-height: 80rpx;
margin: 0;
}
.export-btn {
width: 48%;
background-color: #fff;
color: #666;
border: 1px solid #ddd;
border-radius: 8rpx;
font-size: 16px;
height: 80rpx;
line-height: 80rpx;
margin: 0;
}
</style>