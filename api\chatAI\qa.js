import http from '@/utils/request'
function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 100000 // 设置超时时间为100秒
    })
}
const baseUrl = `/services/whiskerguardaiservice/`

export default {
    // 热门问题
    queryHotQuestions() {
        return request(
            `${baseUrl}api/popular-qa/trending`,
            {  }, 'get')
    },
    //常见问题
    queryCommonQuestions() {
        return request(
            `${baseUrl}api/popular-qa/common`,
            {  }, 'get')
    },
    // 获取所有问答分类
    queryCategories() {
        return request(
            `${baseUrl}api/popular-qa/categories`,
            {}, 'get')
    },
    // 按分类获取问答列表
    queryQuestionsByCategory(category) {
        return request(
            `${baseUrl}api/popular-qa/by-category/${category}`,
            {  }, 'get')
    },
    // 新增浏览次数及问答详情
    addViewCount(id, params) {
        return request(
            `${baseUrl}api/popular-qa/${id}/start-chat`,
            params, 'post')
    },
}