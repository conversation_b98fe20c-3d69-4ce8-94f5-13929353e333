<template>
  <view class="container">
    <uv-navbar title="树形组件演示" :auto-back="true"></uv-navbar>
    
    <view class="content">
      <!-- 单选示例 -->
      <view class="demo-section">
        <view class="section-title">单选模式</view>
        <tree-picker
          v-model="singleValue"
          :tree-data="treeData"
          :multiple="false"
          :default-expand-level="2"
          title="选择部门（单选）"
          placeholder="请选择部门"
          @change="handleSingleChange"
        ></tree-picker>
        
        <view class="result-display">
          <text class="result-label">选中结果：</text>
          <text class="result-value">{{ singleResult }}</text>
        </view>
      </view>

      <!-- 多选示例 -->
      <view class="demo-section">
        <view class="section-title">多选模式</view>
        <tree-picker
          v-model="multipleValue"
          :tree-data="treeData"
          :multiple="true"
          :default-expand-level="2"
          title="选择部门（多选）"
          placeholder="请选择部门"
          @change="handleMultipleChange"
        ></tree-picker>
        
        <view class="result-display">
          <text class="result-label">选中结果：</text>
          <text class="result-value">{{ multipleResult }}</text>
        </view>
      </view>

      <!-- 自定义配置示例 -->
      <view class="demo-section">
        <view class="section-title">自定义配置</view>
        <tree-picker
          v-model="customValue"
          :tree-data="treeData"
          :multiple="false"
          :show-search="false"
          :default-expand-level="2"
          title="选择组织（无搜索，展开2层）"
          placeholder="请选择组织"
          @change="handleCustomChange"
        ></tree-picker>
        
        <view class="result-display">
          <text class="result-label">选中结果：</text>
          <text class="result-value">{{ customResult }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <uv-button type="primary" @click="clearAll">清空所有选择</uv-button>
        <uv-button type="info" @click="setDefaultValues" style="margin-top: 20rpx;">设置默认值</uv-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import TreePicker from '@/components/tree-picker.vue'

// 树形数据（从1.js文件中的数据结构）
const treeData = ref([])

// 单选值和结果
const singleValue = ref('')
const singleSelectedNode = ref(null)

// 多选值和结果
const multipleValue = ref([])
const multipleSelectedNodes = ref([])

// 自定义配置值和结果
const customValue = ref('')
const customSelectedNode = ref(null)

// 计算显示结果
const singleResult = computed(() => {
  return singleSelectedNode.value 
    ? `${singleSelectedNode.value.name} (ID: ${singleSelectedNode.value.id}, 类型: ${singleSelectedNode.value.type})`
    : '未选择'
})

const multipleResult = computed(() => {
  return multipleSelectedNodes.value.length > 0
    ? multipleSelectedNodes.value.map(node => `${node.name} (${node.type})`).join('、')
    : '未选择'
})

const customResult = computed(() => {
  return customSelectedNode.value 
    ? `${customSelectedNode.value.name} (ID: ${customSelectedNode.value.id}, 类型: ${customSelectedNode.value.type})`
    : '未选择'
})

// 初始化数据
onMounted(() => {
  loadTreeData()
})

// 加载树形数据
const loadTreeData = () => {
  // 这里使用1.js中的数据结构
  treeData.value = [
 
  ]
}

// 处理单选变化
const handleSingleChange = (result) => {
  console.log('单选变化:', result)
  singleSelectedNode.value = result.nodes[0] || null
}

// 处理多选变化
const handleMultipleChange = (result) => {
  console.log('多选变化:', result)
  multipleSelectedNodes.value = result.nodes || []
}

// 处理自定义配置变化
const handleCustomChange = (result) => {
  console.log('自定义配置变化:', result)
  customSelectedNode.value = result.nodes[0] || null
}

// 清空所有选择
const clearAll = () => {
  singleValue.value = ''
  multipleValue.value = []
  customValue.value = ''
  singleSelectedNode.value = null
  multipleSelectedNodes.value = []
  customSelectedNode.value = null
  
  uni.showToast({
    title: '已清空所有选择',
    icon: 'success'
  })
}

// 设置默认值
const setDefaultValues = () => {
  singleValue.value = 661  // 总经理办公室
  multipleValue.value = [662, 663, 1001]  // 综合管理部、财务部、前端开发团队
  customValue.value = 669  // 技术部
  
  uni.showToast({
    title: '已设置默认值',
    icon: 'success'
  })
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.content {
  padding: 20rpx;
}

.demo-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .result-display {
    margin-top: 30rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    
    .result-label {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }
    
    .result-value {
      font-size: 28rpx;
      color: #333;
      margin-top: 10rpx;
      display: block;
      word-break: break-all;
    }
  }
}

.action-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}
</style>
