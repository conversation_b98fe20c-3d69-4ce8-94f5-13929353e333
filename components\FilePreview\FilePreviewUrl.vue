<template>
  <view class="action-btn" @click="handlePreview">
    <uni-icons type="eye" size="14" color="#1A73E8"></uni-icons>
    <text class="action-text">预览</text>
  </view>
</template>

<script setup>
import uploads from '@/api/upload.js'
import { getFileExtension, isPreviewable } from '@/utils/file-utils'

const props = defineProps({
  url: {
    type: String,
    required: true
  },
  fileName: {
    type: String,
    default: ''
  }
})

const handlePreview = async () => {
  if (!props.url) {
    uni.showToast({ title: '文件路径不存在', icon: 'none' })
    return
  }

  // 检查文件是否支持预览
  const targetFileName = props.fileName || props.url
  if (!isPreviewable(targetFileName)) {
    uni.showToast({ title: '该文件类型不支持预览', icon: 'none' })
    return
  }

  // 获取文件扩展名
  const fileExtension = getFileExtension(targetFileName)

  try {
    // 显示加载提示
    uni.showLoading({ title: '获取文件地址中...' })

    // 通过接口获取真正的文件地址
    const fileUrl = await uploads.getFileUrl(props.url)

    if (!fileUrl) {
      uni.hideLoading()
      uni.showToast({ title: '获取文件地址失败', icon: 'none' })
      return
    }

    // 更新加载提示
    uni.showLoading({ title: '下载文件中...' })

    // 直接下载文件并打开
    uni.downloadFile({
      url: fileUrl,
      success: (res) => {
        uni.hideLoading()
        
        if (res.statusCode === 200) {
          // 使用openDocument打开文件
          uni.openDocument({
            filePath: res.tempFilePath,
            fileType: fileExtension, // 指定文件类型，解决iOS端预览问题
            showMenu: true,
            success: () => {
              console.log('文件预览成功')
            },
            fail: (error) => {
              console.error('预览失败:', error)
              uni.showToast({ 
                title: '预览失败，请检查文件格式是否支持', 
                icon: 'none',
                duration: 3000
              })
            }
          })
        } else {
          uni.showToast({ 
            title: `下载失败，状态码: ${res.statusCode}`, 
            icon: 'none' 
          })
        }
      },
      fail: (error) => {
        uni.hideLoading()
        console.error('文件下载失败:', error)
        uni.showToast({ 
          title: '文件下载失败，请检查网络连接', 
          icon: 'none',
          duration: 3000
        })
      }
    })
  } catch (error) {
    uni.hideLoading()
    console.error('预览过程出错:', error)
    uni.showToast({ 
      title: '预览失败，请重试', 
      icon: 'none',
      duration: 3000
    })
  }
}
</script>

<style scoped>
.action-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  transition: background-color 0.3s ease;
}

.action-btn:hover {
  background-color: rgba(26, 115, 232, 0.1);
}

.action-btn:active {
  background-color: rgba(26, 115, 232, 0.2);
}

.action-text {
  margin-left: 4rpx;
  font-size: 24rpx;
  color: #1A73E8;
}
</style>
