<template>
    <button  @click="handleClick">跳转</button>
</template>

<script setup>
  const props = defineProps({
    url: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: true
    }
  })
  const handleClick = () => {
    if(props.disabled){
      uni.navigateTo({
          url: props.url
      })
    }else{
      uni.switchTab({
        url: props.url
      })
    }
    
  }
</script>