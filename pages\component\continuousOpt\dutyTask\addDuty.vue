<template>
  <view class="container">
    <uv-form labelPosition="top" label-width="200" :model="formData">
      <!-- 任务基础信息 -->
      <view class="section">
        <view class="section-title">基础信息</view>
        <uv-form-item label="整改项目名称">
          <uv-input
            v-model="formData.name"
            placeholder="请输入整改项目名称，如：关于采购失误的责任处理"
            clearable
            maxlength="50"
            show-word-limit
          />
        </uv-form-item>
        <uv-form-item label="整改编号">
          <uv-input
            v-model="formData.correctionCode"
            placeholder="请输入整改编号"
            clearable
            maxlength="30"
          />
        </uv-form-item>
        <uv-form-item label="整改类型">
          <picker-input
            v-model="formData.correctionType"
            :columns="[experienceTypeOptions]"
            placeholder="请选择整改类型"
            display-key="text"
            value-key="value"
          />
        </uv-form-item>
        <uv-form-item label="来源方式">
          <picker-input
            v-model="formData.experienceSource"
            :columns="[experienceSourceOptions]"
            placeholder="请选择来源方式"
            display-key="text"
            value-key="value"
          />
        </uv-form-item>
        <uv-form-item label="风险等级">
          <FilterTags 
            v-model="formData.level"
            :tags="riskLevels"
            :fixed="false"
            display-key="name"
            value-key="id"
          />
        </uv-form-item>
        <uv-form-item label="来源">
          <picker-input
            v-model="formData.sourceCode"
            :columns="[experienceSourceOptions]"
            placeholder="请选择来源"
            display-key="text"
            value-key="value"
          />
        </uv-form-item>
        <uv-form-item label="责任人所属组织">
          <picker-input
            v-model="formData.dutyEmployeeOrgId"
            :columns="[orgList]"
            placeholder="请选择组织"
            display-key="name"
            value-key="id"
          />
        </uv-form-item>
        <uv-form-item label="标签关键词">
          <uv-input
            v-model="formData.label"
            placeholder="请输入关键词，用#分隔"
            clearable
            maxlength="20"
            show-word-limit
          />
        </uv-form-item>
        <uv-form-item label="开始日期">
          <uv-input
            v-model="formData.startDate"
            type="text"
            placeholder="请选择开始日期"
            readonly
            @click="handleDatePicker('start')"
          />
        </uv-form-item>
        <uv-form-item label="完成日期">
          <uv-input
            v-model="formData.finishDate"
            type="text"
            placeholder="请选择完成日期"
            readonly
            @click="handleDatePicker('finish')"
          />
        </uv-form-item>
      </view>

      <!-- 责任内容描述 -->
      <view class="section">
        <view class="section-title">责任内容描述</view>
        <uv-form-item label="整改背景">
          <uv-input
            v-model="formData.correctionBackground"
            type="textarea"
            :rows="3"
            placeholder="请描述整改背景和上下文"
            resize="none"
            maxlength="500"
            show-word-limit
          />
        </uv-form-item>
        <uv-form-item label="整改要求">
          <uv-input
            v-model="formData.correctionRequire"
            type="textarea"
            :rows="3"
            placeholder="请详细描述整改要求"
            resize="none"
            maxlength="500"
            show-word-limit
          />
        </uv-form-item>
        <uv-form-item label="整改范围">
          <uv-input
            v-model="formData.correctionRange"
            type="textarea"
            :rows="3"
            placeholder="请描述整改范围"
            resize="none"
            maxlength="500"
            show-word-limit
          />
        </uv-form-item>
        <uv-form-item label="责任分类">
          <picker-input
            v-model="formData.reasonCategory"
            :columns="[reasonCategoryOptions]"
            placeholder="请选择责任分类"
            display-key="text"
            value-key="value"
          />
        </uv-form-item>
        <uv-form-item label="整改方案">
          <uv-input
            v-model="formData.correctionScheme"
            type="textarea"
            :rows="3"
            placeholder="请提出具体的整改方案"
            resize="none"
            maxlength="500"
            show-word-limit
          />
        </uv-form-item>
      </view>

      <!-- 附件上传 -->
      <view class="section">
        <view class="section-title">附件上传</view>
        <view class="upload-area">
          <view 
            v-if="formData.attachmentList.length === 0"
            class="upload-empty"
            @click="handleUpload"
          >
            <uv-icon name="plus" color="#999" size="24" />
            <text class="upload-text">上传处理报告 / 佐证材料</text>
            <text class="upload-hint">支持图片 / PDF / Word</text>
          </view>
          <view v-else class="file-list">
            <view 
              v-for="(file, index) in formData.attachmentList" 
              :key="index"
              class="file-item"
            >
              <view class="file-info">
                <uv-icon name="paperclip" color="#666" size="20" />
                <text class="file-name">{{ file.name }}</text>
              </view>
              <view class="file-actions">
                <uv-icon name="eye" color="#1890ff" size="16" @click="handlePreview(file)" />
                <uv-icon name="close" color="#999" size="16" @click="handleRemoveFile(index)" />
              </view>
            </view>
            <button 
              @click="handleUpload"
              class="add-more-button"
            >
              <uv-icon name="plus" color="#666" size="16" />
              <text>继续添加</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="footer">
        <uv-button 
          type="primary" 
          block 
          :loading="loading"
          :disabled="loading"
          @click="handleSubmit"
        >
          {{ loading ? '创建中...' : '发起责任追究整改任务' }}
        </uv-button>
      </view>
    </uv-form>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import PickerInput from '@/components/picker-input.vue'
import FilterTags from '@/components/filterTags.vue'
import taskApi from '@/api/violation/task.js'
import codeApi from '@/api/common/index.js'

onMounted(async () => {
  try {
    const type = await codeApi.getCode('RESPONSIBILITY_CORRECTION');
    formData.correctionCode = type;
  } catch (error) {
  }
});

const formData = reactive({
  name: '', // 整改项目名称
  correctionCode: '', // 整改编号
  correctionType: 'COMPLIANCE_RISK', // 整改类型
  level: 'MIDDLE', // 风险等级
  investigateId: 0, // 违规调查id (保留)
  dutyEmployeeOrgId: '', // 责任人所属组织
  startDate: '', // 开始日期
  finishDate: '', // 完成日期
  correctionBackground: '', // 整改背景
  correctionRequire: '', // 整改要求
  correctionRange: '', // 整改范围
  correctionScheme: '', // 整改方案
  experienceSource: '', // 来源方式
  label: '', // 标签关键词
  reasonCategory: '', // 责任分类
  attachmentList: [] // 附件列表
})

// 风险等级选项
const riskLevels = [
  { name: '低', id: 'LOW' },
  { name: '中', id: 'MIDDLE' },
  { name: '高', id: 'HIGH' }
]

// 整改类型选项 (对应correctionType)
const experienceTypeOptions = [
  { value: 'COMPLIANCE_RISK', text: '合规风险' },
  { value: 'OPERATIONAL_RISK', text: '操作风险' },
  { value: 'SYSTEM_RISK', text: '系统风险' }
]

const experienceSourceOptions = [
  { value: 'internalAudit', text: '内部审计' },
  { value: 'annualPlan', text: '年度计划' },
  { value: 'customerFeedback', text: '客户反馈' },
  { value: 'regulationChange', text: '法规变更' },
  { value: 'managementRequirement', text: '管理层要求' }
]

const reasonCategoryOptions = [
  { value: 'human', text: '人为失误' },
  { value: 'process', text: '流程漏洞' },
  { value: 'management', text: '管理缺失' },
  { value: 'other', text: '其他' }
]

const orgList = [
  { id: 1, name: '集团总部' },
  { id: 2, name: '财务部' },
  { id: 3, name: '人力资源部' },
  { id: 4, name: '采购部' },
  { id: 5, name: '市场部' },
  { id: 6, name: '技术部' }
]

const loading = ref(false)

function handleUpload() {
  uni.chooseFile({
    count: 5,
    type: 'all',
    success: (res) => {
      res.tempFiles.forEach(file => {
        formData.attachmentList.push({
          name: file.name,
          url: file.path,
          desc: ''
        })
      })
    }
  })
}

function handlePreview(file) {
  uni.previewImage({
    urls: [file.url]
  })
}

function handleRemoveFile(index) {
  formData.attachmentList.splice(index, 1)
}

function handleDatePicker(type) {
  uni.showModal({
    title: '选择日期',
    editable: true,
    placeholderText: '请输入日期 (YYYY-MM-DD)',
    success: (res) => {
      if (res.confirm && res.content) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/
        if (dateRegex.test(res.content)) {
          if (type === 'start') {
            formData.startDate = res.content
          } else {
            formData.finishDate = res.content
          }
        } else {
          uni.showToast({ title: '日期格式不正确，请使用YYYY-MM-DD格式', icon: 'none' })
        }
      }
    }
  })
}

async function handleSubmit() {
  // 表单验证
  if (!formData.name) {
    uni.showToast({ title: '请填写整改项目名称', icon: 'none' })
    return
  }
  if (!formData.correctionCode) {
    uni.showToast({ title: '请填写整改编号', icon: 'none' })
    return
  }
  if (!formData.correctionType) {
    uni.showToast({ title: '请选择整改类型', icon: 'none' })
    return
  }
  if (!formData.correctionBackground) {
    uni.showToast({ title: '请填写整改背景', icon: 'none' })
    return
  }
  if (!formData.dutyEmployeeOrgId) {
    uni.showToast({ title: '请选择责任人所属组织', icon: 'none' })
    return
  }
  
  if (loading.value) return
  loading.value = true
  
  try {
    // 构建提交数据
    const submitData = {
      name: formData.name,
      correctionCode: formData.correctionCode,
      correctionType: formData.correctionType,
      level: formData.level,
      investigateId: formData.investigateId || 0,
      dutyEmployeeId: 0, // 默认值
      dutyEmployeeOrgId: parseInt(formData.dutyEmployeeOrgId),
      collaborationEmployeeId: 0, // 默认值
      supervisionEmployeeId: 0, // 默认值
      startDate: formData.startDate || new Date().toISOString().split('T')[0],
      finishDate: formData.finishDate || '',
      // status: 'NO_START', // 默认状态
      correctionBackground: formData.correctionBackground,
      correctionRequire: formData.correctionRequire,
      correctionRange: formData.correctionRange,
      correctionScheme: formData.correctionScheme,
    }
    
    console.log('提交数据：', submitData)
    
    // 调用接口
    const response = await taskApi.createInvestigateDeal(submitData)
    
    if (response && response.id) {
      uni.showToast({ 
        title: '责任追究整改任务创建成功', 
        icon: 'success',
        duration: 2000
      })
      
      // 延迟返回上级页面，并传递responsibilityInvestigateId
      setTimeout(() => {
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        if (prevPage && prevPage.route.includes('processReport')) {
          prevPage.$vm.responsibilityInvestigateId = response.id
        }
        uni.navigateBack()
      }, 2000)
    } else {
      throw new Error('创建失败')
    }
  } catch (error) {
    console.error('创建任务失败：', error)
    uni.showToast({ 
      title: error.message || '创建失败，请重试', 
      icon: 'none',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}

onLoad(() => {
  // 模拟获取组织列表
  // 实际项目中这里应该是接口请求
})
</script>

<style scoped>
.container {
  background: #f0f2f5;
  padding: 16px;
  min-height: 100vh;
}
.section {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}
.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}
.upload-area {
  margin-top: 12px;
}
.upload-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
}
.upload-text {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}
.upload-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}
.file-list {
  margin-top: 12px;
}
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  background: #f9f9f9;
  border-radius: 6px;
}
.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}
.file-name {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}
.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
.add-more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 16px;
  background: #f0f0f0;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
}
.footer {
  padding: 16px 0;
  background: #fff;
}
</style>
    