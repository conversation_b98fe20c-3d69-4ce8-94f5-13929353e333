# 微信服务号授权H5页面部署说明

## 文件说明

本方案包含3个独立的H5页面，需要部署到您的域名 `https://dev.api.mbbhg.com` 下：

### 1. wechat-auth.html - 主授权页面
- **功能**: 展示授权信息，引导用户进行微信授权
- **访问地址**: `https://dev.api.mbbhg.com/wechat-auth.html`
- **特点**: 
  - 响应式设计，适配移动端
  - 检测微信浏览器环境
  - 自动构建微信授权URL并跳转

### 2. wechat-auth-callback.html - 授权回调处理页面
- **功能**: 处理微信授权回调，与后端API交互
- **访问地址**: `https://dev.api.mbbhg.com/wechat-auth-callback.html`
- **特点**:
  - 接收微信回调的code和state参数
  - 调用后端API处理授权
  - 显示处理进度和结果
  - 向小程序webview传递授权结果

### 3. auth-success.html - 授权成功页面
- **功能**: 显示授权成功信息和后续操作指引
- **访问地址**: `https://dev.api.mbbhg.com/auth-success.html`
- **特点**:
  - 友好的成功提示界面
  - 功能介绍和使用提示
  - 返回应用的操作按钮

## 部署步骤

### 1. 上传文件
将以下3个文件上传到您的服务器根目录：
```
https://dev.api.mbbhg.com/
├── wechat-auth.html
├── wechat-auth-callback.html
└── auth-success.html
```

### 2. 配置微信服务号
在微信公众平台配置以下信息：

**授权回调域名**:
```
dev.api.mbbhg.com
```

**授权回调URL**:
```
https://dev.api.mbbhg.com/wechat-auth-callback.html
```

### 3. 后端API配置
确保以下后端接口正常工作：

**授权回调处理接口**:
```
POST https://dev.api.mbbhg.com/services/whiskerguardgeneralservice/api/wechat/binding/oauth/callback
```

请求参数：
```json
{
  "code": "微信授权码",
  "state": "状态参数"
}
```

响应格式：
```json
{
  "success": true,
  "code": 200,
  "message": "授权成功",
  "data": {
    "openId": "用户openId",
    "accessToken": "访问令牌"
  }
}
```

### 4. 小程序配置
小程序中的调用代码已更新，直接跳转到独立H5页面：

```javascript
const goToMessageAuth = async () => {
  const authUrl = 'https://dev.api.mbbhg.com/wechat-auth.html';
  uni.navigateTo({
    url: `/pages/webview/webview?url=${encodeURIComponent(authUrl)}`
  });
}
```

## 配置参数说明

### 需要修改的参数

在 `wechat-auth.html` 和 `wechat-auth-callback.html` 中，请根据实际情况修改以下参数：

1. **微信服务号AppID**:
```javascript
const appId = 'wx101caef81fb1b3b9'; // 替换为您的服务号AppID
```

2. **授权回调URL**:
```javascript
const redirectUri = encodeURIComponent('https://dev.api.mbbhg.com/wechat-auth-callback.html');
```

3. **后端API地址**:
```javascript
const response = await fetch('https://dev.api.mbbhg.com/services/whiskerguardgeneralservice/api/wechat/binding/oauth/callback', {
  // API调用配置
});
```

## 测试流程

### 1. 功能测试
1. 在微信中访问 `https://dev.api.mbbhg.com/wechat-auth.html`
2. 点击"立即授权"按钮
3. 完成微信授权流程
4. 验证授权结果是否正确返回

### 2. 小程序集成测试
1. 在小程序中点击"服务号消息推送授权"
2. 跳转到webview页面
3. 完成授权流程
4. 验证是否能正确返回小程序

## 注意事项

### 1. 域名配置
- 确保域名已备案且可正常访问
- 在微信公众平台配置授权回调域名
- 确保HTTPS证书有效

### 2. 跨域问题
- H5页面与后端API需要配置CORS
- 确保webview可以正常加载H5页面

### 3. 错误处理
- 页面包含完整的错误处理逻辑
- 网络异常时会显示友好的错误提示
- 支持重试机制

### 4. 安全考虑
- state参数用于防止CSRF攻击
- 建议在后端验证state参数的有效性
- 敏感信息不要在前端暴露

## 常见问题

### Q: 授权页面无法加载？
A: 检查域名配置和HTTPS证书，确保在微信中可以正常访问。

### Q: 授权后没有回调？
A: 检查微信公众平台的回调域名配置是否正确。

### Q: 后端API调用失败？
A: 检查API地址、请求格式和CORS配置。

### Q: 小程序webview无法接收消息？
A: 检查postMessage的使用和webview的message事件监听。

## 技术支持

如有问题，请检查：
1. 浏览器控制台的错误信息
2. 网络请求的响应状态
3. 微信公众平台的配置
4. 后端API的日志

建议在测试环境充分验证后再部署到生产环境。
