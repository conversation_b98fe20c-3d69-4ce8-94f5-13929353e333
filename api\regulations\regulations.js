import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
const baseUrl = `/services/whiskerguardregulatoryservice/api/`
const regulations = {
    // 字典信息管理
    dict(params, key) {
        let ser = `/services/whiskerguardregulatoryservice/api/`
        switch (key) {
            case 'info':
            // return 
            default: //获取所有字典信息
                return request(
                    `${ser}dictionary/all`,
                    params, 'GET')
        }
    },
    // 获取法律法规
    // 内部法规列表（内部）
    enterprise(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}enterprise/regulations/page?page=${params.page}&size=${params.size}`,
                    params, 'post')
        }
    },
    // 法规列表（外部）
    laws(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}laws/regulations/page?page=${params.page}&size=${params.size}`,
                    params, 'post')
        }
    },
    // 内部详情
    enterpriseDetail(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}enterprise/regulations/get/${params.id}`,
                    {}, 'GET')
        }
    },
    // 更新内部规章
    updateEnterprise(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}enterprise/regulations/update/${params.id}`,
                    params, 'POST')
        }
    },
    // 外部详情
    lawsDetail(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}laws/regulations/get/${params.id}`,
                    {}, 'GET')
        }
    },
    //法规转化
    saveTransforms(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}laws/regulation/transforms`,
                    params, 'POST')
        }
    },
    // 获取租户相关的法律法规列表
    getTenantRegulations(paging,params) {
          return request(
                    `${baseUrl}tenant/law/regulations/page?page=${paging.page}&size=${paging.size}`,
                    params, 'post')
    },
}

export default regulations