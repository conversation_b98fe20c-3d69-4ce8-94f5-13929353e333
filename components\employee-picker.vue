<template>
  <view>
    <!-- 输入框显示 -->
    <view class="employee-input" @tap="openPicker">
      <uv-input
        v-model="displayValue"
        :placeholder="placeholder"
        readonly
        suffix-icon="arrow-down"
      ></uv-input>
    </view>

    <!-- 员工选择弹窗 -->
    <uv-popup
      ref="popupRef"
      mode="bottom"
      :round="24"
      :overlay="true"
      :close-on-click-overlay="true"
      :safe-area-inset-bottom="true"
      @maskClick="closePicker"
      @change="handlePopupChange"
    >
      <view class="picker-container">
        <!-- 标题栏 -->
        <view class="picker-header">
          <view class="header-btn" @tap="closePicker">
            <text class="btn-text cancel">取消</text>
          </view>
          <view class="header-title">
            <text class="title-text">选择员工</text>
          </view>
          <view class="header-btn" @tap="confirmSelect">
            <text class="btn-text confirm">确定</text>
          </view>
        </view>

        <!-- 部门选择 -->
        <view class="dept-selector">
          <view class="selector-label">选择部门：</view>
          <view class="dept-tabs">
            <scroll-view scroll-x="true" class="dept-scroll">
              <view class="dept-tab-container">
                <view
                  v-for="(dept, index) in departments"
                  :key="dept.id"
                  class="dept-tab"
                  :class="{ active: selectedDeptId === dept.id }"
                  @tap="selectDepartment(dept)"
                >
                  <text class="dept-name">{{ dept.name }}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- 员工列表 -->
        <view class="employee-list">
          <view class="list-header">
            <text class="list-title">员工列表</text>
            <text class="list-count">({{ employees.length }}人)</text>
          </view>
          
          <scroll-view scroll-y="true" class="employee-scroll">
            <!-- <view v-if="loading" class="loading-container">
              <uv-loading-icon mode="circle"></uv-loading-icon>
              <text class="loading-text">加载中...</text>
            </view> -->
            <view v-if="employees.length === 0" class="empty-container">
              <uv-empty mode="data" text="暂无员工数据"></uv-empty>
            </view>
            
            <view v-else class="employee-items">
              <view
                v-for="employee in employees"
                :key="employee.id"
                class="employee-item"
                :class="{ selected: tempSelectedEmployeeId === employee.id }"
                @tap="selectEmployee(employee)"
              >
                <view class="employee-info">
                  <view class="employee-avatar">
                    <text class="avatar-text">{{ employee.name.charAt(0) }}</text>
                  </view>
                  <view class="employee-details">
                    <text class="employee-name">{{ employee.name }}</text>
                    <text class="employee-position">{{ employee.position || '暂无职位' }}</text>
                  </view>
                </view>
                <view class="employee-check">
                  <uv-icon
                    v-if="tempSelectedEmployeeId === employee.id"
                    name="checkmark-circle-fill"
                    color="#007AFF"
                    size="20"
                  ></uv-icon>
                  <view v-else class="check-circle"></view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, onMounted } from 'vue'
import { useUserStore } from '@/store/pinia.js'
import org from '@/api/org/index.js'

const props = defineProps({
  // 占位符文本
  placeholder: {
    type: String,
    default: '请选择员工'
  },
  // 当前选中的员工ID
  modelValue: {
    type: [String, Number],
    default: ''
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const userStore = useUserStore()
const popupRef = ref(null)
const loading = ref(false)

// 部门和员工数据
const departments = ref([])
const employees = ref([])
const selectedDeptId = ref('')
const selectedEmployeeId = ref('')
const selectedEmployee = ref(null)

// 临时选择的员工（用于弹窗内的选择状态）
const tempSelectedEmployeeId = ref('')
const tempSelectedEmployee = ref(null)

// 显示值（只有确认后才显示）
const displayValue = computed(() => {
  return selectedEmployee.value ? selectedEmployee.value.name : ''
})

// 初始化部门数据
onMounted(() => {
  const deptData = userStore.getDepartments()
  if (deptData && Array.isArray(deptData)) {
    departments.value = deptData
    // 默认选择第一个部门
    if (departments.value.length > 0) {
      selectDepartment(departments.value[0])
    }
  }
})

// 打开选择器
const openPicker = () => {
  if (props.disabled) return
  // 打开时，将当前选中的员工设置为临时选择
  tempSelectedEmployeeId.value = selectedEmployeeId.value
  tempSelectedEmployee.value = selectedEmployee.value
  if (popupRef.value) {
    popupRef.value.open()
  }
}

// 关闭选择器
const closePicker = () => {
  if (popupRef.value) {
    popupRef.value.close()
  }
}

// 处理弹窗状态变化
const handlePopupChange = (e) => {
  // 弹窗关闭时的处理逻辑
  if (!e.show) {
    // 可以在这里添加关闭时的逻辑
  }
}

// 选择部门
const selectDepartment = async (dept) => {
  selectedDeptId.value = dept.id
  await loadEmployees(dept.id)
}

// 加载员工数据
const loadEmployees = async (deptId) => {
  if (!deptId) return
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  loading.value = true
  employees.value = []
  
  try {
    const resObj = await org.getEmpByUnitId(deptId)
    console.log('API返回数据:', resObj)
    const res = resObj.content
    console.log('res', res)
    
    if (res) {
      employees.value = res.map(emp => ({
        id: emp.id,
        name: emp.realName,
        position: emp.position || emp.postName || '',
        avatar: emp.avatar || '',
        ...emp
      }))
      console.log('员工数据加载成功:', employees.value)
    } else {
      console.log('员工数据格式不正确或为空:', res)
      employees.value = []
    }
  } catch (error) {
    console.error('获取员工数据失败:', error)
    employees.value = []
    uni.showToast({
      title: '获取员工数据失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    uni.hideLoading()
    loading.value = false
    console.log('员工数据加载完成，loading状态已重置')
  }
}

// 选择员工（临时选择，不影响显示值）
const selectEmployee = (employee) => {
  tempSelectedEmployeeId.value = employee.id
  tempSelectedEmployee.value = employee
}

// 确认选择
const confirmSelect = () => {
  if (!tempSelectedEmployee.value) {
    uni.showToast({
      title: '请选择员工',
      icon: 'none',
      duration: 2000
    })
    return
  }
  
  // 确认时才更新实际选中的员工和显示值
  selectedEmployeeId.value = tempSelectedEmployee.value.id
  selectedEmployee.value = tempSelectedEmployee.value
  
  emit('update:modelValue', selectedEmployee.value.id)
  emit('change', selectedEmployee.value)
  closePicker()
}

// 监听外部值变化
watch(() => props.modelValue, async (newVal) => {
  if (newVal && newVal !== selectedEmployeeId.value) {
    selectedEmployeeId.value = newVal
    // 根据ID查找员工信息
    await findEmployeeById(newVal)
  } else if (!newVal) {
    // 如果外部值为空，清空选择
    selectedEmployeeId.value = ''
    selectedEmployee.value = null
  }
}, { immediate: true })

// 根据员工ID查找员工信息
const findEmployeeById = async (employeeId) => {
  if (!employeeId) return

  // 首先在当前已加载的员工列表中查找
  const foundEmployee = employees.value.find(emp => emp.id == employeeId)
  if (foundEmployee) {
    selectedEmployee.value = foundEmployee
    return
  }

  // 如果当前列表中没有，尝试从所有部门中查找
  try {
    for (const dept of departments.value) {
      const resObj = await org.getEmpByUnitId(dept.id)
      const empList = resObj.content || []

      const employee = empList.find(emp => emp.id == employeeId)
      if (employee) {
        selectedEmployee.value = {
          id: employee.id,
          name: employee.realName,
          position: employee.position || employee.postName || '',
          avatar: employee.avatar || '',
          ...employee
        }
        return
      }
    }

    console.warn(`未找到ID为${employeeId}的员工信息`)
  } catch (error) {
    console.error('查找员工信息失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.employee-input {
  width: 100%;
}

.picker-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .header-btn {
    min-width: 80rpx;
    
    .btn-text {
      font-size: 32rpx;
      
      &.cancel {
        color: #999;
      }
      
      &.confirm {
        color: #007AFF;
        font-weight: 500;
      }
    }
  }
  
  .header-title {
    flex: 1;
    text-align: center;
    
    .title-text {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }
}

.dept-selector {
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .selector-label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
  }
  
  .dept-scroll {
    white-space: nowrap;
    
    .dept-tab-container {
      display: flex;
      gap: 20rpx;
    }
    
    .dept-tab {
      display: inline-block;
      padding: 16rpx 32rpx;
      background: #f8f9fa;
      border-radius: 40rpx;
      border: 2rpx solid transparent;
      transition: all 0.3s;
      
      &.active {
        background: #e3f2fd;
        border-color: #007AFF;
        
        .dept-name {
          color: #007AFF;
          font-weight: 500;
        }
      }
      
      .dept-name {
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
      }
    }
  }
}

.employee-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  
  .list-header {
    display: flex;
    align-items: center;
    padding: 30rpx 40rpx 20rpx;
    
    .list-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .list-count {
      font-size: 24rpx;
      color: #999;
      margin-left: 10rpx;
    }
  }
  
  .employee-scroll {
    flex: 1;
    height: 600rpx;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  
  .loading-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 20rpx;
  }
}

.empty-container {
  padding: 80rpx 40rpx;
}

.employee-items {
  padding: 0 40rpx 40rpx;
}

.employee-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.selected {
    background-color: #f8f9ff;
  }
  
  .employee-info {
    display: flex;
    align-items: center;
    flex: 1;
    
    .employee-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      
      .avatar-text {
        color: #fff;
        font-size: 32rpx;
        font-weight: 600;
      }
    }
    
    .employee-details {
      flex: 1;
      
      .employee-name {
        display: block;
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
      }
      
      .employee-position {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .employee-check {
    .check-circle {
      width: 40rpx;
      height: 40rpx;
      border: 2rpx solid #ddd;
      border-radius: 50%;
    }
  }
}
</style>