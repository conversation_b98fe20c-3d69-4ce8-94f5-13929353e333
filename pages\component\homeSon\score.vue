<template>
  <view class="page-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="logo">logo</text>
      <view class="icon-group">
        <uni-icons type="notification" size="24" color="#666"></uni-icons>
        <uni-icons type="person" size="24" color="#666"></uni-icons>
      </view>
    </view>

    <!-- 主内容区 -->
    <scroll-view class="main-content" scroll-y>
      <!-- 合规概览 Banner -->
      <view class="main-content-scroll">
        <view class="compliance-banner">
          <view class="banner-header">
            <text class="score-text">合规得分 85分</text>
            <view class="detail-link">
              <text>查看评分详情</text>
              <uni-icons type="arrowright" size="16" color="#1A73E8"></uni-icons>
            </view>
          </view>

          <view class="indicator-group">
            <view class="indicator-item">
              <text class="indicator-value">3/5</text>
              <text class="indicator-label">合规制度</text>
            </view>

            <view class="indicator-item">
              <text class="indicator-value">4/8</text>
              <text class="indicator-label">培训完成</text>
            </view>

            <view class="indicator-item">
              <text class="indicator-value">2/2</text>
              <text class="indicator-label">风险处理</text>
            </view>
          </view>
        </view>

        <!-- 待办事项 -->
        <view class="todo-section">
          <view class="section-header">
            <text class="section-title">待办事项</text>
            <text class="view-all">查看全部</text>
          </view>

          <view class="todo-list">
            <view class="todo-item">
              <view class="icon-wrapper">
                <uni-icons type="document" size="20" color="#1A73E8"></uni-icons>
              </view>
              <view class="todo-content">
                <text class="todo-title">更新信息安全管理制度</text>
                <text class="todo-date">截止: 2023-11-15</text>
              </view>
              <text class="priority-tag">高优先级</text>
            </view>

            <view class="todo-item">
              <view class="icon-wrapper">
                <uni-icons type="education" size="20" color="#34A853"></uni-icons>
              </view>
              <view class="todo-content">
                <text class="todo-title">完成数据合规培训</text>
                <text class="todo-date">截止: 2023-11-20</text>
              </view>
              <text class="priority-tag">中优先级</text>
            </view>
          </view>
        </view>

        <!-- 风险预警 -->
        <view class="risk-section">
          <view class="section-header">
            <text class="section-title">风险预警</text>
            <text class="view-all">查看全部</text>
          </view>

          <view class="risk-item">
            <view class="icon-wrapper">
              <uni-icons type="warning" size="20" color="#F44336"></uni-icons>
            </view>
            <view class="risk-content">
              <text class="risk-title">客户数据泄露风险</text>
              <text class="risk-desc">需在24小时内处理</text>
            </view>
            <text class="risk-tag">紧急</text>
          </view>
        </view>

        <!-- 合规动态 -->
        <view class="news-section">
          <view class="section-header">
            <text class="section-title">合规动态</text>
            <text class="view-all">查看全部</text>
          </view>

          <view class="news-list">
            <view class="news-item">
              <text class="news-title">《个人信息保护法》更新解读</text>
              <text class="news-date">2023-11-10 发布</text>
              <text class="news-desc">最新发布的《个人信息保护法》实施细则对数据跨境传输提出了新的要求，请各部门负责人及时了解...</text>
            </view>

            <view class="news-item">
              <text class="news-title">第四季度合规培训计划</text>
              <text class="news-date">2023-11-08 发布</text>
              <text class="news-desc">第四季度将开展全员数据安全与合规意识培训，请各部门协调时间安排员工参加...</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
   <go-navitor url="/pages/component/lists/riskDetail" />
  </view>
</template>

<script setup>
import goNavitor from '../../../components/goNavitor.vue';
import { ref } from 'vue';
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
  position: relative;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.logo {
  font-family: 'Pacifico', serif;
  font-size: 40rpx;
  color: #1A73E8;
}

.icon-group {
  display: flex;
  gap: 32rpx;
}

.main-content {
  flex: 1;

  .main-content-scroll {
    padding-top: 120rpx;
    padding-bottom: 120rpx;
    padding-left: 32rpx;
    padding-right: 32rpx;
  }
}

.compliance-banner {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.banner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.score-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #1A73E8;
}

.detail-link {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #1A73E8;
}

.indicator-group {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.indicator-item {
  flex: 1;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 16rpx;
  text-align: center;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.indicator-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #1A73E8;
}

.indicator-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
}

.view-all {
  font-size: 24rpx;
  color: #666;
}

.todo-section,
.risk-section,
.news-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.todo-list,
.news-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.todo-item,
.risk-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
}

.icon-wrapper {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
}

.todo-item .icon-wrapper {
  background-color: rgba(26, 115, 232, 0.1);
}

.risk-item .icon-wrapper {
  background-color: rgba(244, 67, 54, 0.1);
}

.todo-content,
.risk-content {
  flex: 1;
}

.todo-title,
.risk-title {
  font-size: 28rpx;
  font-weight: 500;
}

.todo-date,
.risk-desc {
  font-size: 24rpx;
  color: #666;
}

.priority-tag,
.risk-tag {
  font-size: 24rpx;
  color: #666;
}

.risk-tag {
  color: #F44336;
}

.news-item {
  padding: 24rpx;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
}

.news-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.news-date {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.news-desc {
  font-size: 24rpx;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>