<template>
	<view class="list-responsibility-container">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<header-bar title="责任追究整改" shape="circle" prefixIcon="search" clearable :fixed="false" v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click.stop="searchClick">
								<!-- <uni-icons type="search" size="20" /> -->
								<text class="btn-sou">搜索</text>
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags :fixed="false" :top="0" :tags="tags" :active-index="activeIndex"
					@change="handleTagChange" />
			</template>
				<view class="content-container">
					<view class="content-container-list">
						<view v-for="(item, index) in dataList" :key="index" class="list-item"
							@touchstart="handleTouchStart(index)" @touchend="handleTouchEnd(index)"
							@click="handleItemClick(item)">
							<view class="item-content">
								<text class="item-title">{{ item.name }}</text>
								<view class="item-info">
									<text class="info-text" :data-status="item.status">
										{{ getStatusText(item.status) }}
									</text>
									<text class="info-text" :data-level="item.level" style="margin-left: 20rpx;">
										{{ getLevelText(item.level) }}
									</text>
									<text class="info-tig" style="margin-left: 20rpx;">{{ getCorrectionTypeText(item.correctionType) }}</text>
								</view>
								<view class="item-info">
									<text class="info-tig">编号：{{ item.id || '暂无' }}</text>
									<text class="info-tig">日期：{{ item.startDate || '暂无' }}</text>
								</view>
								<view class="item-info">
									<text class="info-tig">开始时间：{{ item.startDate }}</text>
									<text class="info-tig" style="margin-left: 20rpx;">完成时间：{{ item.finishDate }}</text>
								</view>
							</view>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>
				</view>
		</z-paging>
	</view>
</template>

<script setup>
	import headerBar from '@/components/headerBar.vue'
	import filterTags from '@/components/filterTags.vue'
	import {
		ref
	} from 'vue';
	import taskApi from '@/api/violation/task.js'
	import {
		useUserStore
	} from '@/store/pinia.js';

	const activeIndex = ref(0);
	const tags = ref([{
		name: '全部',
		value: null
	}, {
		name: '未开始',
		value: 'NO_START'
	}, {
		name: '进行中',
		value: 'PROGRESSING'
	}, {
		name: '已完成',
		value: 'FINISHED'
	}, {
		name: '已暂停',
		value: 'PAUSED'
	}, {
		name: '已取消',
		value: 'CANCELED'
	}]);

	const handleTagChange = (index) => {
		activeIndex.value = index
		paging.value.reload();
	}
	function searchClick() {
		reload()
	}

	const handleItemClick = (item) => {
		// 跳转到详情页面
		console.log('点击整改项:', item);
		// $tools.routeJump(`/pages/component/continuousOpt/improvementTask/improvementDetail?id=${item.id}`);
	};

	const handleTouchStart = (index) => {
		// 触摸开始效果
		const item = document.querySelectorAll('.list-item')[index];
		if (item) {
			item.style.transform = 'scale(0.98)';
			item.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
		}
	};

	const handleTouchEnd = (index) => {
		// 触摸结束效果
		const item = document.querySelectorAll('.list-item')[index];
		if (item) {
			item.style.transform = 'scale(1)';
			item.style.boxShadow = '0 1px 4px rgba(0,0,0,0.05)';
		}
	};

	// 状态映射
	const getStatusText = (status) => {
		const statusMap = {
			'NO_START': '未开始',
			'PROGRESSING': '进行中',
			'FINISHED': '已完成',
			'PAUSED': '已暂停',
			'CANCELED': '已取消'
		};
		return statusMap[status] || status;
	};

	const getStatusClass = (status) => {
		const classMap = {
			'NO_START': 'status-not-start',
			'PROGRESSING': 'status-progressing',
			'FINISHED': 'status-finished',
			'PAUSED': 'status-paused',
			'CANCELED': 'status-canceled'
		};
		return classMap[status] || '';
	};

	// 优先级映射
	const getLevelText = (level) => {
		const levelMap = {
			'LOW': '低风险',
			'MIDDLE': '中风险',
			'HIGH': '高风险'
		};
		return levelMap[level] || level;
	};

	const getLevelClass = (level) => {
		const classMap = {
			'LOW': 'level-low',
			'MIDDLE': 'level-middle',
			'HIGH': 'level-high'
		};
		return classMap[level] || '';
	};

	// 整改类型映射
	const getCorrectionTypeText = (type) => {
		const typeMap = {
			'COMPLIANCE_RISK': '合规风险',
			'OPERATIONAL_RISK': '操作风险',
			'SYSTEM_RISK': '系统风险'
		};
		return typeMap[type] || type;
	};

	const searchValue = ref("") //
	const paging = ref(null)
	const dataList = ref([])

	// 搜索功能
	const search = () => {
		paging.value.reload();
	}

	// 列表查询
	const queryList = (pageNo, pageSize) => {
		const userStore = useUserStore();
		const selectedTag = tags.value[activeIndex.value];
		var params = {
			// tenantId: userStore.tenantId,
			name: searchValue.value || undefined,
			status: selectedTag.value || undefined
		}
		const pageParams = {
			page: pageNo - 1,
			size: pageSize
		}
		taskApi.getResponsibilityCorrectionList(params, pageParams).then((res) => {
			paging.value.complete(res.content);
		}).catch((err) => {
			paging.value.complete(false);
		})
	}
</script>

<style lang="scss" scoped>
	@import '/static/css/nav.scss';

	.list-responsibility-container {
		background-color: #F5F7FA;
		.content-container-list {
			padding: 20rpx 32rpx;
		}

		.list-item {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			margin-bottom: 16rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		}

		.item-content {
			flex: 1;
		}

		.item-title {
			font-size: 32rpx;
			color: #1a1a1a;
			margin-bottom: 8rpx;
			display: block;
		}

		.item-info {
			display: flex;
			align-items: center;
			margin-bottom: 4rpx;
		}

		.info-text {
			display: inline-block;
			padding: 6rpx 16rpx;
			color: white;
			font-weight: normal;
			border-radius: 6rpx;
			text-align: center;
			font-size: 24rpx;
		}

		/* 状态样式 */
		.info-text[data-status="NO_START"] {
			background-color: rgb(245, 245, 245);
			color: #666;
		}

		.info-text[data-status="PROGRESSING"] {
			background-color: rgb(230, 247, 255);
			color: #1890ff;
		}

		.info-text[data-status="FINISHED"] {
			background-color: rgb(246, 255, 237);
			color: #52c41a;
		}

		.info-text[data-status="PAUSED"] {
			background-color: rgb(255, 247, 230);
			color: #fa8c16;
		}

		.info-text[data-status="CANCELED"] {
			background-color: rgb(255, 242, 240);
			color: #ff4d4f;
		}

		/* 高优先级样式 - 红色背景 */
		.info-text[data-level="HIGH"] {
			background-color: rgb(255, 234, 234);
			color: #ff7875;
		}

		/* 中优先级样式 - 橙色背景 */
		.info-text[data-level="MIDDLE"] {
			background-color: rgb(255, 244, 229);
			color: #ffc069;
		}

		/* 低优先级样式 - 绿色背景 */
		.info-text[data-level="LOW"] {
			background-color: rgb(230, 255, 250);
			color: rgb(49, 151, 149);
		}

		.info-dot {
			font-size: 24rpx;
			color: #666;
			margin: 0 8rpx;
		}

		.info-tig {
			font-size: 24rpx;
			color: #666;
		}
	}
</style>