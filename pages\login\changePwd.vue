<template>
    <view class="change-pwd-container">
        <view class="form-container">
            <!-- 手机号输入 -->
            <view class="input-group">
                <label class="input-label">手机号码</label>
                <view class="input-wrapper">
                    <input v-model="form.phone" type="number" placeholder="请输入注册手机号" class="input-field"
                        placeholder-class="placeholder" />
                </view>
            </view>
            <!-- 验证码输入 -->
            <view class="input-group">
                <label class="input-label">验证码</label>
                <view class="code-input-wrapper">
                    <input v-model="form.code" type="number" placeholder="请输入验证码" class="code-input"
                        placeholder-class="placeholder" />
                    <button  class="base-button get-code-btn" :disabled="countdown > 0" @click="getVerificationCode">
                        {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
                    </button>
                </view>
            </view>
            <!-- 新密码输入 -->
            <view class="input-group">
                <label class="input-label">新密码</label>
                <view class="input-wrapper">
                    <input v-model="form.password" :password="!showPassword" type="text" placeholder="请输入新密码"
                        class="input-field" placeholder-class="placeholder" />
                    <uni-icons :type="showPassword ? 'eye' : 'eye-slash'" size="20" color="#9CA3AF" class="toggle-icon"
                        @click="showPassword=!showPassword" />
                </view>
            </view>
            <!-- 确认密码 -->
            <view class="input-group">
                <label class="input-label">确认密码</label>
                <view class="input-wrapper">
                    <input v-model="form.confirmPassword" :password="!showConfirmPassword" type="text" placeholder="请再次输入新密码" class="input-field"
                        placeholder-class="placeholder" />
                    <uni-icons :type="showConfirmPassword ? 'eye' : 'eye-slash'" size="20" color="#9CA3AF" class="toggle-icon"
                        @click="showConfirmPassword=!showConfirmPassword" />
                </view>
            </view>
            <!-- 提交按钮 -->
            <button  class="base-button submit-btn" @click="handleSubmit">确认修改</button>
        </view>
    </view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import loginApi from '@/api/login/login.js'
// 表单数据
const form = ref({
    phone: '',
    code: '',
    password: '',
    confirmPassword: ''
})

// 密码可见性
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const countdown = ref(0)
let timer = null

// 切换密码可见性
const togglePassword = () => {
    console.log('togglePassword')
    showPassword.value = !showPassword.value
}

// 切换确认密码可见性
const toggleConfirmPassword = () => {
    showConfirmPassword.value = !showConfirmPassword.value
}

// 获取验证码
const getVerificationCode = async () => {
    if (!form.value.phone) {
        uni.showToast({ title: '请输入手机号', icon: 'none' })
        return
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(form.value.phone)) {
        uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
        return
    }

    try {
        uni.showLoading({ title: '发送中...' })
        
        const params = {
            phoneNumber: form.value.phone,
            codeType: 'RESET_PASSWORD',
            regionCode: '86'
        }
        
        const result = await loginApi.sendCode(params)
        uni.hideLoading()
        
        if (result.success) {
            countdown.value = 60
            timer = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0) {
                    clearInterval(timer)
                    timer = null
                }
            }, 1000)
            
            uni.showToast({ title: '验证码已发送', icon: 'success' })
        } else {
            uni.showToast({ title: result.message || '发送失败', icon: 'none' })
        }
    } catch (error) {
        uni.hideLoading()
        console.error('发送验证码失败:', error)
        uni.showToast({ title: '发送失败，请重试', icon: 'none' })
    }
}

// 处理提交
const handleSubmit = async () => {
    if (!form.value.phone) {
        uni.showToast({ title: '请输入手机号', icon: 'none' })
        return
    }
    if (!form.value.code) {
        uni.showToast({ title: '请输入验证码', icon: 'none' })
        return
    }
    if (!form.value.password) {
        uni.showToast({ title: '请输入新密码', icon: 'none' })
        return
    }
    if (form.value.password !== form.value.confirmPassword) {
        uni.showToast({ title: '两次密码输入不一致', icon: 'none' })
        return
    }

    try {
        uni.showLoading({ title: '提交中...' })
        
        const params = {
            phone: form.value.phone,
            verifyCode: form.value.code,
            newPassword: form.value.password,
            confirmPassword: form.value.confirmPassword
        }
        
        const result = await loginApi.changePwd(params)
        uni.hideLoading()
        
        if (result) {
            uni.showToast({ title: '密码修改成功', icon: 'success' })
            setTimeout(() => {
                uni.navigateBack()
            }, 1500)
        } else {
            uni.showToast({ title: '修改失败，请重试', icon: 'none' })
        }
    } catch (error) {
        uni.hideLoading()
        console.error('修改密码失败:', error)
        uni.showToast({ title: error.message || '修改失败，请重试', icon: 'none' })
    }
}

// 返回登录
const goBackToLogin = () => {
    uni.navigateBack()
}

// 显示用户协议
const showAgreement = () => {
    uni.navigateTo({ url: '/pages/agreement/index' })
}

// 组件卸载时清除定时器
onUnmounted(() => {
    if (timer) clearInterval(timer)
})
</script>

<style lang="scss" scoped>
@import '/static/css/buttons.scss';
.change-pwd-container {
    display: flex;
    flex-direction: column;
    height: auto;
    position: relative;

    /* 表单区 */
    .form-container {
        margin: 40rpx 32rpx 0;
        /* 原20px 16px */
        padding: 48rpx;
        /* 原24px */
        // background-color: #FFFFFF;
        border-radius: 24rpx;
        /* 原12px */
        // box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
        /* 原1px 3px */
    }

    .input-group {
        margin-bottom: 48rpx;
        /* 原24px */
    }

    .input-label {
        display: block;
        color: #374151;
        font-size: 28rpx;
        /* 原14px */
        font-weight: 500;
        margin-bottom: 16rpx;
        /* 原8px */
    }

    .input-wrapper {
        position: relative;
    }

    .input-field {
        // width: 100%;
        box-sizing: border-box;
        height: 80rpx;
        /* 原48px */
        padding: 0 32rpx;
        /* 原16px */
        border: 2rpx solid #E5E7EB;
        /* 原1px */
        border-radius: 16rpx;
        /* 原8px */
        font-size: 28rpx;
        /* 原14px */
    }

    .input-field:focus {
        border-color: #1A73E8;
        outline: none;
    }

    .placeholder {
        color: #9CA3AF;
        font-size: 28rpx;
        /* 原14px */
    }

    /* 验证码输入区 */
    .code-input-wrapper {
        display: flex;
        gap: 16rpx;
        /* 原8px */
    }

    .code-input {
        flex: 1;
        height: 80rpx;
        /* 原48px */
        padding: 0 32rpx;
        /* 原16px */
        border: 2rpx solid #E5E7EB;
        /* 原1px */
        border-radius: 16rpx;
        /* 原8px */
        font-size: 28rpx;
        /* 原14px */
    }

    .get-code-btn {
        width: 240rpx;
        /* 原120px */
        /* 原48px */
        background-color: #1A73E8;
        color: #FFFFFF;
        font-size: 24rpx;
        /* 原12px */
        font-weight: 500;
        border-radius: 16rpx;
        /* 原8px */
        line-height: 1;
    }

    .get-code-btn:disabled {
        background-color: #E5E7EB;
        color: #9CA3AF;
    }

    .toggle-icon {
        position: absolute;
        right: 32rpx;
        /* 原16px */
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    /* 提交按钮 */
    .submit-btn {
        width: 100%;
        height: 85rpx;
        /* 原48px */
        background-color: #1A73E8;
        color: #FFFFFF;
        font-weight: bold;
        font-size: 32rpx;
        /* 原16px */
        border-radius: 48rpx;
        /* 原24px */
        margin-top: 16rpx;
    }

    .submit-btn:active {
        background-color: #1557B0;
        transform: scale(0.98);
    }

    .btn-text {
        line-height: 1;
    }

    /* 返回登录 */
    .back-login {
        margin-top: 10rpx;
        /* 原16px */
        text-align: center;
    }

    .back-text {
        color: #1A73E8;
        font-size: 28rpx;
        /* 原14px */
        font-weight: 500;
        text-decoration: underline;
    }

    /* 底部协议 */
    .footer {
        position: absolute;
        bottom: 32rpx;
        /* 原16px */
        left: 0;
        right: 0;
        text-align: center;
    }

    .footer-text {
        color: #6B7280;
        font-size: 24rpx;
        /* 原12px */
    }

    .footer-link {
        color: #1A73E8;
        font-size: 24rpx;
        /* 原12px */
    }

    /* 重置按钮默认样式 */
    // button {
    //     padding: 0;
    //     margin: 0;
    //     line-height: 1;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    // }

    // button::after {
    //     display: none;
    // }
}
</style>