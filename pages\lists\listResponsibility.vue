<template>
    <view class="list-responsibility-container">
        <z-paging :auto="false" class="paging-content" ref="paging" v-model="list" @query="queryList" :loading="loading">
            <template #top>
                <header-bar v-model="keyword" shape="circle" prefixIcon="search" clearable :fixed="false">
                    <template #right>
                        <view class="nav-right">
                            <view class="nav-btn" @click.stop="handleSearch">
                                <!-- <uni-icons type="search" size="20" /> -->
								<text class="btn-sou">搜索</text>
                            </view>
                            <view class="nav-btn" v-if="!userStore.lawyer" @click="addResponsibility">
                                <text class="btn-text">新增</text>
                            </view>
                        </view>
                    </template>
                </header-bar>
              <filter-tags :fixed="false" v-model="tagValue" :tags="tags" valueKey="value" @change="handleTagChange" />
            </template>
            <base-list :list-data="list" :get-status-text="getStatusName" @item-click="handleItemClick"></base-list>
        </z-paging>
        <!-- 内容区域 -->
        <!-- <scroll-view scroll-y class="content-container">
            <view v-for="(item, index) in dutyList" :key="index" class="list-item" @touchstart="handleTouchStart(index)"
                @touchend="handleTouchEnd(index)" @click="handleItemClick(item)">
                <view class="item-content">
                    <text class="item-title">{{ item.title }}</text>
                    <view class="item-info">
                        <text class="info-text">{{ item.department }}</text>
                        <text class="info-dot">·</text>
                        <text class="info-text">责任人：{{ item.responsible }}</text>
                        <text class="info-dot">·</text>
                        <text class="info-text">{{ item.date }}</text>
                    </view>
                </view>
                <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>
        </scroll-view> -->

    </view>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue';
import https from '@/api/duties/duties.js'
import headerBar from '@/components/headerBar.vue'
import filterTags from '@/components/filterTags.vue'
import baseList from '@/components/baseList.vue'
import {
    useUserStore
} from '@/store/pinia.js';
const userStore = useUserStore();
const tagValue = ref(null)
const keyword = ref('')
const paging = ref(null);
const loading = ref(false);

// 审批状态映射
const approvalStatus = ref({
    "DRAFT": '草稿',
    "APPROVED": '审核通过',
    "REJECTED": '审核未通过',
    "PENDING": '审批中'
});

// 获取状态名称
function getStatusName(status) {
    return approvalStatus.value[status] || status;
}

onMounted(() => {
    reload();
});

function addResponsibility() {
    uni.navigateTo({
        url: '/pages/component/lists/addResponsibility?type=add'
    })
}
function reload() {
    nextTick(() => {
        // 刷新列表数据(如果不希望列表pageNo被重置可以用refresh代替reload方法)
        paging.value && paging.value.reload();
    })
}
const handleSearch = (value) => {
    reload()
}

// 搜索方法
const search = () => {
    reload()
}
function queryList(pageNo, pageSize) {
    var params = {
        page: pageNo - 1,
        size: pageSize,
        riskLevel: tagValue.value,
        // sort: 'createdAt',
        // direction: 'desc'
        postName: keyword.value,
        // status: 1,
        // tenantId: userStore.tenantId
    }
    dutyList(params)
}
// 列表请求
function dutyList(params, key, getObj) {
    uni.showLoading({
        title: '加载中...',
        mask: true
    });
    loading.value = true;
    https.dutyList(
        params,
        key,
        getObj
    ).then(res => {
        paging.value.complete(res.content)
        console.log(res)
        loading.value = false;
        uni.hideLoading();
    }).catch(err => {
        paging.value.complete(false);
        loading.value = false;
        uni.hideLoading();
    })
}

const tags = ref([{
		name: '全部',
		value: null
	}, {
		name: '高危',
		value: 'HIGH'
	}, {
		name: '中危',
		value: 'MEDIUM'
	}, {
		name: '低危',
		value: 'LOW'
	}]);

const handleTagChange = (index) => {
    reload();
};

const list = ref([])


const handleAdd = () => {
    // 新增职责逻辑
    console.log('新增职责');
};

const handleItemClick = (item) => {
    // 点击职责项逻辑，跳转到详情页面
    console.log('点击职责项:', item);
    // 统一传入detail
      uni.navigateTo({
        url: `/pages/component/lists/responsibilityDetail?type=detail&id=${item.item.dutyMainId}`
    })
    // uni.navigateTo({
    //     url: `/pages/component/lists/addResponsibility?type=detail&id=${item.item.dutyMainId}`
    // })
};

// 暴露方法供父组件调用
defineExpose({
    reload
});

</script>

<style lang="scss" scoped>
@import '/static/css/nav.scss';

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;

    .action-btn1 {
        width: 100rpx;
    }
}

.list-responsibility-container {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    background-color: #F5F7FA;

    .cancel-btn {
        font-size: 28rpx;
        color: #1A73E8;
        font-weight: normal;
        background-color: transparent;
        border: none;
        padding: 0;
        margin-left: 16rpx;
    }

    .filter-container {
        position: fixed;
        top: 88rpx;
        left: 0;
        width: 100%;
        height: 88rpx;
        background-color: #fff;
        padding: 20rpx 32rpx;
        z-index: 10;
    }

    .filter-scroll {
        width: 100%;
        height: 100%;
        white-space: nowrap;
    }

    .filter-tabs {
        display: inline-flex;
        height: 100%;
        align-items: center;
        gap: 16rpx;
    }

    .tab-item {
        font-size: 28rpx;
        font-weight: normal;
        padding: 16rpx 24rpx;
        border-radius: 32rpx;
    }

    .tab-active {
        background-color: #1A73E8;
        color: #fff;
    }

    .tab-inactive {
        background-color: #EFEFF4;
        color: #666;
    }

    .content-container {
        flex: 1;
        margin-top: 176rpx;
        margin-bottom: 88rpx;
        padding: 0 32rpx;
    }

    .list-item {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 24rpx 32rpx;
        margin-bottom: 16rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    }

    .item-content {
        flex: 1;
    }

    .item-title {
        font-size: 32rpx;
        font-weight: normal;
        color: #1a1a1a;
        margin-bottom: 8rpx;
        display: block;
    }

    .item-info {
        display: flex;
        align-items: center;
    }

    .info-text {
        font-size: 24rpx;
        color: #666;
    }

    .info-dot {
        font-size: 24rpx;
        color: #666;
        margin: 0 8rpx;
    }
}
</style>