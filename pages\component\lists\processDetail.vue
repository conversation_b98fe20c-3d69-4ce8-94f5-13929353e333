<template>
    <view class="process-detail">
        <!-- 主体内容区 -->
        <view class="process-content" scroll-y>
            <!-- 状态标识区 -->
            <view class="card-section status-header">
                <view class="create-info-container">
                    <view class="create-info-item">创建人：{{ processDetail.createdBy }}</view>
                    <view class="create-info-item">创建时间：{{ processDetail.createdAt }}</view>
                </view>
                <view :class="['status-tag', statusClass]">{{ statusText }}</view>
            </view>
            <!-- 业务流程区 -->
            <view class="card">
                <view class="section-title">业务流程区</view>
                <view class="business-process">
                    <view class="process-item">
                        <text class="process-label">业务领域：</text>
                        <text class="process-value">{{ processDetail.businessDomainName }}</text>
                    </view>
                    <view class="process-item">
                        <text class="process-label">业务流程：</text>
                        <text class="process-value">{{ processDetail.businessProcess }}</text>
                    </view>
                </view>
            </view>
            <!-- 管控环节 -->
            <view class="card">
                <view class="control-section">
                    <view class="control-title">管控环节</view>
                    <view v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                        <view v-for="(link, index) in processDetail.controlLinks" :key="index" class="control-item">
                            <view class="control-item-header">
                                <view class="control-name">{{ link.controlLinkName }}</view>
                            </view>
                            <view class="department-tags">
                                <text v-for="dept in link.responsibleDepartments"
                                    :key="dept.departmentName || dept.departmentId" class="dept-tag">
                                    {{ dept.departmentName }}
                                </text>
                            </view>
                            <view class="control-content">
                                <view class="control-field" v-if="link.linkDescription">
                                    <text class="field-label">环节描述：</text>
                                    <text class="field-value">{{ link.linkDescription }}</text>
                                </view>
                                <view class="control-field" v-if="link.approvalDepartment">
                                    <text class="field-label">审批部门：</text>
                                    <text class="field-value">{{ link.approvalDepartment }}</text>
                                </view>
                                <view class="control-field" v-if="link.approver">
                                    <text class="field-label">审批人：</text>
                                    <text class="field-value">{{ link.approver }}</text>
                                </view>
                                <view class="control-field" v-if="link.riskPoints">
                                    <text class="field-label">风险点：</text>
                                    <text class="field-value">{{ link.riskPoints }}</text>
                                </view>
                                <view class="control-field" v-if="link.riskDescriptions">
                                    <text class="field-label">风险描述：</text>
                                    <text class="field-value">{{ link.riskDescriptions }}</text>
                                </view>
                                <view class="control-field" v-if="link.complianceRequirements">
                                    <text class="field-label">合规要求：</text>
                                    <text class="field-value">{{ link.complianceRequirements }}</text>
                                </view>
                                <view class="control-field" v-if="link.complianceBasis">
                                    <text class="field-label">合规依据：</text>
                                    <text class="field-value">{{ link.complianceBasis }}</text>
                                </view>
                                <view class="control-field" v-if="link.controlMeasures">
                                    <text class="field-label">管控措施：</text>
                                    <text class="field-value">{{ link.controlMeasures }}</text>
                                </view>
                                <view class="control-field"
                                    v-if="link.responsibleDepartments && link.responsibleDepartments.length > 0">
                                    <text class="field-label">责任部门：</text>
                                    <text class="field-value">
                                        <text v-for="(dept, deptIndex) in link.responsibleDepartments"
                                            :key="dept.departmentName || dept.departmentId">
                                            {{ dept.departmentName }}<text
                                                v-if="deptIndex < link.responsibleDepartments.length - 1">、</text>
                                        </text>
                                    </text>
                                </view>
                                <view class="control-field"
                                    v-if="link.responsiblePositions && link.responsiblePositions.length > 0">
                                    <text class="field-label">责任岗位：</text>
                                    <text class="field-value">
                                        <text v-for="(pos, posIndex) in link.responsiblePositions"
                                            :key="pos.positionName || pos.positionId">
                                            {{ pos.positionName }}<text
                                                v-if="posIndex < link.responsiblePositions.length - 1">、</text>
                                        </text>
                                    </text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-else class="empty-state">
                        <text>暂无管控环节数据</text>
                    </view>
                </view>
            </view>
            <!-- 工作文档 -->
            <view class="card">
                <view class="section-title">工作文档</view>
                <view class="document-list" v-if="processDetail.processFiles && processDetail.processFiles.length > 0">
                    <view v-for="(file, index) in processDetail.processFiles" :key="index" class="document-item">
                        <view class="document-icon">
                            <uni-icons type="file" size="20" color="#1a73e8"></uni-icons>
                        </view>
                        <view class="document-info">
                            <text class="document-name">{{ file.fileName }}</text>
                            <text class="document-size">{{ file.fileSize ? (file.fileSize / 1024).toFixed(2) + 'KB' : ''
                                }}</text>
                        </view>
                        <view class="document-action">
                            <FilePreview :item="file" />
                            <!-- <uni-icons type="download" size="18" color="#1a73e8"></uni-icons> -->
                        </view>
                    </view>
                </view>
                <view v-else class="empty-state">
                    <text>暂无工作文档</text>
                </view>
            </view>
        </view>
        <!-- 底部操作栏 -->
        <view v-if="isDataLoaded">
            <FooterBar @submit="handleFooterClick" :buttons="footerButtons" />
        </view>
    </view>
</template>
<script setup>
import { ref, computed } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/pinia.js';
import addForm from '@/api/duties/addForm.js';
import FooterBar from '@/components/footerBar.vue'
import FilePreview from '@/components/FilePreview/FilePreview.vue'
// 响应式数据
const processDetail = ref({});
const showHistory = ref(false);
const processId = ref('');
const userStore = useUserStore();
const isDataLoaded = ref(false); // 数据加载状态
const lawyer = computed(() => userStore.lawyer); // 律师角色判断

const footerButtons = computed(() => {
	// 如果状态为已通过，律师和非律师都不能操作
	if (processDetail.value.approvalStatus === 'APPROVED') {
		return [];
	}
	
	// 律师角色且状态为待审核时可以操作
	if (lawyer.value && processDetail.value.approvalStatus === 'PENDING') {
		return [
			{
				text: '编辑',
				type: 'submit',
				slotName: 'submit',
				bgColor: '#1a73e8',
				textColor: '#fff'
			}
		];
	}
	
	// 非律师角色且状态为草稿时可以操作
	if (!lawyer.value && processDetail.value.approvalStatus === 'DRAFT') {
		return [
			{
				text: '编辑',
				type: 'submit',
				slotName: 'submit',
				bgColor: '#1a73e8',
				textColor: '#fff'
			}
		];
	}
	
	// 其他情况不显示按钮
	return [];
});
const handleFooterClick = () => {
    uni.navigateTo({
        url: `/pages/component/lists/addProcess?type=detail&id=${processId.value}`
    })
};
// 解析AI返回的责任部门/岗位数据
const parseResponsibleData = (data) => {
    if (!data) return [];

    // 如果已经是数组，直接返回
    if (Array.isArray(data)) {
        return data.map(item => ({
            ...item,
            isExternal: !item.departmentId && !item.positionId // 标记外部数据（无ID）
        }));
    }

    // 如果是字符串，尝试解析JSON
    if (typeof data === 'string') {
        try {
            const parsed = JSON.parse(data);
            if (Array.isArray(parsed)) {
                return parsed.map(item => ({
                    ...item,
                    isExternal: !item.departmentId && !item.positionId // 标记外部数据（无ID）
                }));
            }
        } catch (e) {
            console.warn('解析责任数据失败:', e);
        }
    }

    return [];
};

// 获取流程详情
const fetchProcessDetail = async () => {
    if (!processId.value) {
        console.error('processId为空');
        return;
    }

    uni.showLoading({
        title: '加载中...'
    });

    try {
        const res = await addForm.getProcessDetail(processId.value);
        uni.hideLoading();

        if (res) {
            console.log('流程详情数据:', res);

            // 更新流程详情数据
            processDetail.value = {
                businessDomainName: res.businessDomainName || '',
                businessDomainType: res.businessDomainType || '',
                businessProcess: res.businessProcess || '',
                processDescription: res.processDescription || '',
                approvalStatus: res.approvalStatus || '',
                processStatus: res.processStatus || '',
                createdBy: res.createdBy || '',
                createdAt: res.createdAt || '',
                createdTime: res.createdTime || '',
                updatedBy: res.updatedBy || '',
                updatedTime: res.updatedTime || ''
            };

            // 处理流程图数据
            if (res.processDiagramUrl) {
                processDetail.value.processDiagramUrl = res.processDiagramUrl;
            }

            // 处理管控环节数据
            if (res.controlLinks && res.controlLinks.length > 0) {
                processDetail.value.controlLinks = res.controlLinks.map(link => {
                    const controlDetail = link.controlDetail || {};
                    return {
                        controlLinkName: link.controlLinkName || '',
                        linkDescription: link.linkDescription || '',
                        approvalDepartment: link.approvalDepartment || '',
                        approver: link.approver || '',
                        requiresGeneralManager: link.requiresGeneralManager || false,
                        requiresChairman: link.requiresChairman || false,
                        riskPoints: controlDetail.riskPoints || '',
                        riskDescriptions: controlDetail.riskDescriptions || '',
                        complianceRequirements: controlDetail.complianceRequirements || '',
                        complianceBasis: controlDetail.complianceBasis || '',
                        controlMeasures: controlDetail.controlMeasures || '',
                        responsibleDepartments: parseResponsibleData(controlDetail.responsibleDepartments),
                        responsiblePositions: parseResponsibleData(controlDetail.responsiblePositions)
                    };
                });
            } else {
                processDetail.value.controlLinks = [];
            }

            // 处理文件数据
            if (res.processFiles && Array.isArray(res.processFiles)) {
                processDetail.value.processFiles = res.processFiles.map(doc => ({
                    name: doc.fileName || '',
                    fileName: doc.fileName || '',
                    url: doc.fileUrl || '',
                    type: doc.fileType || '',
                    size: doc.fileSize || 0,
                    fileSize: doc.fileSize || 0,
                    key: doc.fileUrl || '',
                    filePath: doc.fileUrl || '',
                    fileType: doc.fileType || '',
                    status: 'success'
                }));
            } else {
                processDetail.value.processFiles = [];
            }
        }
        isDataLoaded.value = true; // 数据加载完成
    } catch (error) {
        uni.hideLoading();
        isDataLoaded.value = true; // 即使失败也设置为已加载，避免一直不显示按钮
        console.error('获取流程详情失败:', error);
        uni.showToast({
            title: '获取详情失败',
            icon: 'none',
            duration: 2000
        });
    }
};

// 生命周期函数
onLoad((options) => {
    if (options.id) {
        processId.value = options.id;
    }
});

onShow(() => {
    if (processId.value) {
        fetchProcessDetail();
    }
});



// 状态计算属性
const statusText = computed(() => {
    switch (processDetail.value.approvalStatus) {
        case 'APPROVED': return '已生效';
        case 'PENDING': return '待审核';
        case 'DRAFT': return '草稿';
        case 'REJECTED': return '已驳回';
        default: return '';
    }
});

const statusClass = computed(() => {
    switch (processDetail.value.approvalStatus) {
        case 'APPROVED': return 'active';
        case 'PENDING': return 'pending';
        case 'DRAFT': return 'draft';
        case 'REJECTED': return 'rejected';
        default: return 'active';
    }
});
const downloadFile = (fileName) => {
    uni.showToast({
        title: `下载 ${fileName}`,
        icon: 'none'
    });
};


</script>
<style lang="scss" scoped>
.process-detail {
    min-height: 100vh;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
}

// 主体内容区
.process-content {
    flex: 1;
    padding: 20rpx;
    padding-bottom: 120rpx;
}

// 卡片样式
.card {
    background: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

// 状态卡片
.status-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 30rpx;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.create-info-container {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.create-info-item {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 4rpx;
}

.create-info-item:last-child {
    margin-bottom: 0;
}

.divider {
    color: #ddd;
}

// 区块标题
.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

// 业务流程区
.business-process {
    .process-item {
        display: flex;
        margin-bottom: 20rpx;
        align-items: flex-start;

        .process-label {
            min-width: 140rpx;
            color: #666;
            font-size: 28rpx;
            margin-right: 16rpx;
        }

        .process-value {
            flex: 1;
            color: #333;
            font-size: 28rpx;
            line-height: 1.6;
        }
    }
}

// 管控环节
.control-section {
    margin-bottom: 32rpx;
    border-radius: 12rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.control-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
}

.department-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 16rpx;

    .tag {
        padding: 8rpx 16rpx;
        background: #e3f2fd;
        color: #1976d2;
        border-radius: 20rpx;
        font-size: 24rpx;
    }
}

.control-item {
    // display: flex;
    margin-bottom: 12rpx;
    // align-items: flex-start;
    padding-bottom: 12rpx;

    &:not(:last-child) {
        border-bottom: 1rpx dashed #e0e0e0;
    }

    .control-label {
        min-width: 160rpx;
        color: #666;
        font-size: 26rpx;
        margin-right: 12rpx;
    }

    .control-value {
        flex: 1;
        color: #333;
        font-size: 26rpx;
        line-height: 1.5;
    }
}

// 工作文档
.document-list {
    .document-item {
        display: flex;
        align-items: center;
        padding: 16rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }

        .document-name {
            flex: 1;
            margin-left: 12rpx;
            color: #333;
            font-size: 28rpx;
        }

        .document-size {
            color: #999;
            font-size: 24rpx;
            margin-right: 20rpx;
        }

        .download-btn {
            color: #007aff;
            font-size: 26rpx;
            padding: 8rpx 16rpx;
            border: 1rpx solid #007aff;
            border-radius: 8rpx;
        }
    }
}


.empty-state {
    text-align: center;
    padding: 40rpx 20rpx;
    color: #999;
    font-size: 28rpx;
}

.control-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.control-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.dept-tag {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 24rpx;
    margin-left: 10rpx;
}

.control-content {
    padding-left: 20rpx;
}

.control-field {
    margin-bottom: 16rpx;
    display: flex;
    flex-direction: row;
}

.field-label {
    color: #666;
    font-size: 28rpx;
    min-width: 160rpx;
    flex-shrink: 0;
}

.field-value {
    color: #333;
    font-size: 28rpx;
    flex: 1;
    line-height: 1.5;
}

.document-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.document-icon {
    margin-right: 20rpx;
}

.document-info {
    flex: 1;
}

.document-name {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 8rpx;
}

.document-size {
    font-size: 24rpx;
    color: #999;
}

.document-action {
    padding: 10rpx;
}
</style>