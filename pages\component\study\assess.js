import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}

const baseUrl = '/services/whiskerguardtrainingservice/api'
export default { 
  getExamList(params) {
    return request(`${baseUrl}/exam/management/center/query`, params)
  },
  // 考试页面的详情
  getExamDetail(id) {
    return request(`${baseUrl}/exam/management/detail/${id}`, {}, 'get')
  },
  // 开始考试
  startExam(examId) {
    return request(`${baseUrl}/exam/management/start?examId=${examId}`, {}, 'post')
  },
  // 获取考试题目
  getExamQuestion(id) {
    return request(`${baseUrl}/exam/management/questions/${id}`, {}, 'get')
  },
  // 提交考试
  submitExam(examRecordId, params) {
    return request(`${baseUrl}/exam/management/submit/all/answers?examRecordId=${examRecordId}`, params, 'post')
  },
  // 错题分析
  wrongAnalysis(examRecordId) {
    return request(`${baseUrl}/exam/management/wrong/analysis/${examRecordId}`, {}, 'get')
  },
}