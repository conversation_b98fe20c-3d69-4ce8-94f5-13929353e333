---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 06-企业三张清单服务/业务流程管控清单V2

## POST AI分析业务流程管控清单

POST /compliancelistservice/api/v2/biz/process/ai/analyze

{@code POST  /api/v2/biz/process/ai/analyze} : AI analyze business process control.
AI分析业务流程管控清单
根据业务领域和流程图，AI分析生成可直接提交的业务流程管控清单数据

> Body 请求参数

```json
{
  "businessDomain": "采购管理",
  "processDiagramUrl": "https://example.com/process-diagram.png",
  "toolKey": "deepseek",
  "conversationId": "conv-c043b0fa14e34840"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[BizProcessAiAnalysisRequestDTO](#schemabizprocessaianalysisrequestdto)| 否 |none|

> 返回示例

> 500 Response

```json
{
  "success": false,
  "message": "",
  "aiRawResponse": "",
  "processData": {
    "orgUnitId": 0,
    "businessDomainName": "",
    "businessDomainType": "",
    "businessProcess": "",
    "processDescription": "",
    "processDiagramUrl": "",
    "processStatus": "",
    "approvalStatus": "",
    "metadata": "",
    "controlLinks": [
      {
        "id": 0,
        "controlLinkName": "",
        "linkOrder": 0,
        "linkDescription": "",
        "approvalDepartment": "",
        "approver": "",
        "requiresGeneralManager": false,
        "requiresChairman": false,
        "metadata": "",
        "version": 0,
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "createdBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "isDeleted": false,
        "controlDetail": {
          "id": 0,
          "riskPoints": "",
          "riskDescriptions": "",
          "complianceRequirements": "",
          "complianceBasis": "",
          "controlMeasures": "",
          "responsibleDepartments": "",
          "responsiblePositions": "",
          "metadata": "",
          "version": 0,
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "createdBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "isDeleted": false
        }
      }
    ],
    "workDocuments": [
      {
        "id": 0,
        "fileName": "",
        "fileUrl": "",
        "fileType": "",
        "fileSize": 0,
        "metadata": "",
        "version": 0,
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "createdBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "isDeleted": false
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|[ResponseEntityBizProcessComprehensiveAnalysisResponseDTO](#schemaresponseentitybizprocesscomprehensiveanalysisresponsedto)|

# 数据模型

<h2 id="tocS_BizProcessControlDetailDTO">BizProcessControlDetailDTO</h2>

<a id="schemabizprocesscontroldetaildto"></a>
<a id="schema_BizProcessControlDetailDTO"></a>
<a id="tocSbizprocesscontroldetaildto"></a>
<a id="tocsbizprocesscontroldetaildto"></a>

```json
{
  "id": 0,
  "riskPoints": "string",
  "riskDescriptions": "string",
  "complianceRequirements": "string",
  "complianceBasis": "string",
  "controlMeasures": "string",
  "responsibleDepartments": "string",
  "responsiblePositions": "string",
  "metadata": "string",
  "version": 0,
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "createdBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|riskPoints|string|false|none||风险点|
|riskDescriptions|string|false|none||风险描述|
|complianceRequirements|string|false|none||合规要求|
|complianceBasis|string|false|none||合规依据|
|controlMeasures|string|false|none||管控措施|
|responsibleDepartments|string|false|none||责任部门（JSON格式存储多个部门）|
|responsiblePositions|string|false|none||责任岗位（JSON格式存储多个岗位）|
|metadata|string|false|none||附加信息|
|version|integer|false|none||版本号|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|createdBy|string|false|none||创建人|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|updatedBy|string|false|none||更新人|
|isDeleted|boolean|false|none||删除状态|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_BizProcessControlLinkDTO">BizProcessControlLinkDTO</h2>

<a id="schemabizprocesscontrollinkdto"></a>
<a id="schema_BizProcessControlLinkDTO"></a>
<a id="tocSbizprocesscontrollinkdto"></a>
<a id="tocsbizprocesscontrollinkdto"></a>

```json
{
  "id": 0,
  "controlLinkName": "string",
  "linkOrder": 0,
  "linkDescription": "string",
  "approvalDepartment": "string",
  "approver": "string",
  "requiresGeneralManager": true,
  "requiresChairman": true,
  "metadata": "string",
  "version": 0,
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "createdBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "isDeleted": true,
  "controlDetail": {
    "id": 0,
    "riskPoints": "string",
    "riskDescriptions": "string",
    "complianceRequirements": "string",
    "complianceBasis": "string",
    "controlMeasures": "string",
    "responsibleDepartments": "string",
    "responsiblePositions": "string",
    "metadata": "string",
    "version": 0,
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "createdBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "isDeleted": true
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|controlLinkName|string|true|none||管控环节名称|
|linkOrder|integer|false|none||环节序号|
|linkDescription|string|false|none||环节描述|
|approvalDepartment|string|false|none||审批部门|
|approver|string|false|none||审批人|
|requiresGeneralManager|boolean|false|none||是否需要总经理审批|
|requiresChairman|boolean|false|none||是否需要董事长审批|
|metadata|string|false|none||附加信息|
|version|integer|false|none||版本号|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|createdBy|string|false|none||创建人|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|updatedBy|string|false|none||更新人|
|isDeleted|boolean|false|none||删除状态|
|controlDetail|[BizProcessControlDetailDTO](#schemabizprocesscontroldetaildto)|false|none||管控环节详情|

<h2 id="tocS_ResponseEntityBizProcessComprehensiveAnalysisResponseDTO">ResponseEntityBizProcessComprehensiveAnalysisResponseDTO</h2>

<a id="schemaresponseentitybizprocesscomprehensiveanalysisresponsedto"></a>
<a id="schema_ResponseEntityBizProcessComprehensiveAnalysisResponseDTO"></a>
<a id="tocSresponseentitybizprocesscomprehensiveanalysisresponsedto"></a>
<a id="tocsresponseentitybizprocesscomprehensiveanalysisresponsedto"></a>

```json
{
  "success": true,
  "message": "string",
  "aiRawResponse": "string",
  "processData": {
    "orgUnitId": 0,
    "businessDomainName": "string",
    "businessDomainType": "string",
    "businessProcess": "string",
    "processDescription": "string",
    "processDiagramUrl": "string",
    "processStatus": "IN_USE",
    "approvalStatus": "DRAFT",
    "metadata": "string",
    "controlLinks": [
      {
        "id": 0,
        "controlLinkName": "string",
        "linkOrder": 0,
        "linkDescription": "string",
        "approvalDepartment": "string",
        "approver": "string",
        "requiresGeneralManager": true,
        "requiresChairman": true,
        "metadata": "string",
        "version": 0,
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "createdBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "isDeleted": true,
        "controlDetail": {
          "id": 0,
          "riskPoints": "string",
          "riskDescriptions": "string",
          "complianceRequirements": "string",
          "complianceBasis": "string",
          "controlMeasures": "string",
          "responsibleDepartments": "string",
          "responsiblePositions": "string",
          "metadata": "string",
          "version": 0,
          "createdAt": {},
          "createdBy": "string",
          "updatedAt": {},
          "updatedBy": "string",
          "isDeleted": true
        }
      }
    ],
    "workDocuments": [
      {
        "id": 0,
        "fileName": "string",
        "fileUrl": "string",
        "fileType": "string",
        "fileSize": 0,
        "metadata": "string",
        "version": 0,
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "createdBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "isDeleted": true
      }
    ]
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||操作是否成功|
|message|string|false|none||操作消息|
|aiRawResponse|string|false|none||AI原始响应内容|
|processData|[BizProcessListV2CreateRequestDTO](#schemabizprocesslistv2createrequestdto)|false|none||业务流程管控清单数据（可直接用于表单提交）|

<h2 id="tocS_BizProcessListV2CreateRequestDTO">BizProcessListV2CreateRequestDTO</h2>

<a id="schemabizprocesslistv2createrequestdto"></a>
<a id="schema_BizProcessListV2CreateRequestDTO"></a>
<a id="tocSbizprocesslistv2createrequestdto"></a>
<a id="tocsbizprocesslistv2createrequestdto"></a>

```json
{
  "orgUnitId": 0,
  "businessDomainName": "string",
  "businessDomainType": "string",
  "businessProcess": "string",
  "processDescription": "string",
  "processDiagramUrl": "string",
  "processStatus": "IN_USE",
  "approvalStatus": "DRAFT",
  "metadata": "string",
  "controlLinks": [
    {
      "id": 0,
      "controlLinkName": "string",
      "linkOrder": 0,
      "linkDescription": "string",
      "approvalDepartment": "string",
      "approver": "string",
      "requiresGeneralManager": true,
      "requiresChairman": true,
      "metadata": "string",
      "version": 0,
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "createdBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "isDeleted": true,
      "controlDetail": {
        "id": 0,
        "riskPoints": "string",
        "riskDescriptions": "string",
        "complianceRequirements": "string",
        "complianceBasis": "string",
        "controlMeasures": "string",
        "responsibleDepartments": "string",
        "responsiblePositions": "string",
        "metadata": "string",
        "version": 0,
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "createdBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "isDeleted": true
      }
    }
  ],
  "workDocuments": [
    {
      "id": 0,
      "fileName": "string",
      "fileUrl": "string",
      "fileType": "string",
      "fileSize": 0,
      "metadata": "string",
      "version": 0,
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "createdBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|orgUnitId|integer(int64)|false|none||部门ID|
|businessDomainName|string|false|none||业务领域|
|businessDomainType|string|false|none||业务类型|
|businessProcess|string|false|none||业务流程|
|processDescription|string|false|none||流程描述|
|processDiagramUrl|string|false|none||流程图URL|
|processStatus|string|false|none||流程状态|
|approvalStatus|string|false|none||审批状态|
|metadata|string|false|none||附加信息|
|controlLinks|[[BizProcessControlLinkDTO](#schemabizprocesscontrollinkdto)]|false|none||管控环节列表|
|workDocuments|[[BizProcessFileDTO](#schemabizprocessfiledto)]|false|none||工作文档列表|

#### 枚举值

|属性|值|
|---|---|
|processStatus|IN_USE|
|processStatus|DEPRECATED|
|approvalStatus|DRAFT|
|approvalStatus|PENDING|
|approvalStatus|APPROVED|
|approvalStatus|REJECTED|

<h2 id="tocS_BizProcessFileDTO">BizProcessFileDTO</h2>

<a id="schemabizprocessfiledto"></a>
<a id="schema_BizProcessFileDTO"></a>
<a id="tocSbizprocessfiledto"></a>
<a id="tocsbizprocessfiledto"></a>

```json
{
  "id": 0,
  "fileName": "string",
  "fileUrl": "string",
  "fileType": "string",
  "fileSize": 0,
  "metadata": "string",
  "version": 0,
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "createdBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|fileName|string|true|none||文件名称|
|fileUrl|string|true|none||文件url|
|fileType|string|false|none||文件类型|
|fileSize|integer(int64)|false|none||文件大小（字节）|
|metadata|string|false|none||附加信息|
|version|integer|false|none||版本号|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|createdBy|string|false|none||创建人|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|updatedBy|string|false|none||更新人|
|isDeleted|boolean|false|none||删除状态|

<h2 id="tocS_BizProcessAiAnalysisRequestDTO">BizProcessAiAnalysisRequestDTO</h2>

<a id="schemabizprocessaianalysisrequestdto"></a>
<a id="schema_BizProcessAiAnalysisRequestDTO"></a>
<a id="tocSbizprocessaianalysisrequestdto"></a>
<a id="tocsbizprocessaianalysisrequestdto"></a>

```json
{
  "businessDomain": "采购管理",
  "processDiagramUrl": "https://example.com/process-diagram.png",
  "toolKey": "deepseek",
  "conversationId": "conv-c043b0fa14e34840"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|businessDomain|string|true|none||业务领域|
|processDiagramUrl|string|false|none||业务流程图URL|
|toolKey|string|false|none||AI工具类型|
|conversationId|string|false|none||上下文会话ID|

