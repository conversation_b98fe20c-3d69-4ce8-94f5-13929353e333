<template>
  <view class="subscribe-container">

    <!-- 订阅说明 -->
    <view class="subscribe-tip">
      <view class="tip-icon">
        <uni-icons type="info" size="16" color="#1890ff"></uni-icons>
      </view>
      <text class="tip-text">开启消息订阅后，您将及时收到相关通知提醒</text>
    </view>

    <!-- 订阅列表 -->
    <view class="subscribe-list">
      <view 
        v-for="(item, index) in subscribeList" 
        :key="index" 
        class="subscribe-item"
      >
        <view class="item-content">
          <view class="item-info">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-desc">{{ item.description }}</view>
          </view>
          <view class="item-switch">
            <switch 
              :checked="item.subscribed" 
              @change="handleSwitchChange($event, index)"
              color="#1890ff"
            />
          </view>
        </view>
      </view>
    </view>
    <!-- 微信授权按钮 -->
    <view v-if="!hasWechatAuth" class="auth-section">
      <view class="auth-tip">
        <view class="tip-icon">
          <uni-icons type="info" size="16" color="#ff9500"></uni-icons>
        </view>
        <text class="tip-text">需要先进行微信授权才能订阅消息通知</text>
      </view>
      <button class="auth-btn" @tap="handleWechatAuth" :loading="authLoading">
        {{ authLoading ? '授权中...' : '微信授权' }}
      </button>
    </view>
    
    <!-- 底部说明 -->
    <view class="bottom-tip">
      <text class="tip-text">* 您可以随时在此页面管理消息订阅设置</text>
    </view>
  </view>
</template>

<script>
import profileApi from './index.js'
import { useUserStore } from '@/store/pinia.js'
import orgApi from '@/api/org/index.js'

export default {
  data() {
    return {
      subscribeList: [],
      loading: false,
      hasWechatAuth: false,
      authLoading: false
    }
  },
  
  onLoad() {
    this.checkWechatAuth()
    this.getSubscribeStatus()
    this.getOrgTreeData() // 获取组织树数据
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 获取组织树数据
    async getOrgTreeData() {
      try {
        const userStore = useUserStore()
        // 如果store中没有数据，则从API获取
        if (!userStore.getOrgTree() || userStore.getOrgTree().length === 0) {
          const res = await orgApi.unitTree()
          if (res) {
            userStore.setOrgTree(res)
            console.log('组织树数据获取成功:', res)
          }
        }
      } catch (error) {
        console.error('获取组织树数据失败:', error)
      }
    },
    
    // 获取订阅状态
    async getSubscribeStatus() {
      try {
        // 从API获取用户绑定消息模板列表
        const response = await profileApi.getUserBindingTemplates()
        if (response && Array.isArray(response)) {
          this.subscribeList = response.map(item => ({
            id: item.id,
            title: item.templateName || '消息通知',
            description: `${item.templateName || '消息通知'}提醒`,
            templateId: item.templateId,
            subscribed: item.isBound || false
          }))
        }
        
        // 同时检查微信订阅状态
        await this.checkWechatSubscribeStatus()
      } catch (error) {
        console.error('获取订阅状态失败:', error)
        // 如果API调用失败，可以显示错误提示
        uni.showToast({
          title: '获取订阅状态失败',
          icon: 'none'
        })
      }
    },
    
    // 检查微信订阅状态
    async checkWechatSubscribeStatus() {
      try {
        // 这里可以调用后端接口查询用户的微信订阅状态
        // 由于API文档中没有查询接口，这里暂时跳过
        console.log('检查微信订阅状态')
      } catch (error) {
        console.error('检查微信订阅状态失败:', error)
      }
    },
    
    // 处理开关变化
    async handleSwitchChange(event, index) {
      const checked = event.detail.value
      const item = this.subscribeList[index]
      
      // 检查是否已授权微信
      if (!this.hasWechatAuth) {
        // 恢复开关状态
        item.subscribed = !checked
        uni.showToast({
          title: '请先进行微信授权',
          icon: 'none'
        })
        return
      }
      
      if (this.loading) return
      
      this.loading = true
      
      try {
        if (checked) {
          // 订阅消息
          await this.subscribeMessage(item.templateId)
          // 只有订阅成功后才更新状态为true
          item.subscribed = true
          uni.showToast({
            title: '订阅成功',
            icon: 'success'
          })
        } else {
          // 取消订阅 - 只有当前状态确实是已订阅时才调用接口
          if (item.subscribed) {
            await this.unsubscribeMessage(item.templateId)
            item.subscribed = false
            uni.showToast({
              title: '已取消订阅',
              icon: 'success'
            })
          } else {
            // 如果当前实际未订阅，直接更新状态即可
            item.subscribed = false
          }
        }
        
      } catch (error) {
        console.error('订阅操作失败:', error)
        // 恢复开关状态到操作前的状态
        item.subscribed = !checked
        
        // 根据错误类型给出不同提示
        if (error.message && error.message.includes('用户拒绝订阅')) {
          uni.showModal({
            title: '提示',
            content: '您拒绝了消息订阅',
            showCancel: false,
            confirmText: '我知道了'
          })
        } else {
          uni.showToast({
            title: error.message || '操作失败',
            icon: 'none'
          })
        }
      } finally {
        this.loading = false
      }
    },
    
    // 检查微信授权状态
    checkWechatAuth() {
      const userStore = useUserStore()
      this.hasWechatAuth = !!(userStore.userDetail && userStore.userDetail.wechatOpenId)
    },
    
    // 处理微信授权
    async handleWechatAuth() {
      if (this.authLoading) return
      
      this.authLoading = true
      
      try {
        // 1. 通过uni.login获取code
        const code = await this.getWechatCode()
        if (!code) {
          throw new Error('获取微信授权码失败')
        }
        
        // 2. 通过code获取openid
        const userInfoRes = await profileApi.getUserInfoByCode({ code })
        if (!userInfoRes || !userInfoRes.openid) {
          throw new Error('获取openid失败')
        }
        
        const openId = userInfoRes.openid
        
        // 3. 进行用户与微信绑定
        const bindParams = {
          openId: openId,
          unionId: 'opid001'
        }
        await profileApi.bindWechat(bindParams)
        
        // 4. 更新用户store中的openId
        const userStore = useUserStore()
        if (userStore.userDetail) {
          userStore.userDetail.wechatOpenId = openId
        }
        
        // 5. 更新授权状态
        this.hasWechatAuth = true
        
        uni.showToast({
          title: '微信授权成功',
          icon: 'success'
        })
        
      } catch (error) {
        console.error('微信授权失败:', error)
        uni.showToast({
          title: error.message || '微信授权失败',
          icon: 'none'
        })
      } finally {
        this.authLoading = false
      }
    },
    
    // 订阅消息（简化版，只处理微信订阅）
    async subscribeMessage(templateId) {
      try {
        // 1. 请求微信订阅消息授权
        const subscribeRes = await this.requestWechatSubscribe(templateId)
        
        // 2. 绑定消息模板
        const templateParams = {
          templateId: templateId,
          templateName: this.subscribeList.find(item => item.templateId === templateId)?.title || '',
          isBound: true
        }
        await profileApi.bindMessageTemplate(templateParams)
        
        return subscribeRes
      } catch (error) {
        console.error('订阅失败:', error)
        throw error
      }
    },
    
    // 请求微信订阅消息
    requestWechatSubscribe(templateId) {
      return new Promise((resolve, reject) => {
        uni.requestSubscribeMessage({
          tmplIds: [templateId],
          success: (res) => {
            console.log('订阅结果:', res)
            if (res[templateId] === 'accept') {
              resolve(res)
            } else {
              reject(new Error('用户拒绝订阅'))
            }
          },
          fail: (err) => {
            console.error('订阅失败:', err)
            reject(new Error('订阅失败，请重试'))
          }
        })
      })
    },
    
    // 取消订阅
    async unsubscribeMessage(templateId) {
      try {
        // 调用消息模板解绑接口
        const templateParams = {
          templateId: templateId,
          templateName: this.subscribeList.find(item => item.templateId === templateId)?.title || '',
          isBound: false
        }
        await profileApi.bindMessageTemplate(templateParams)
        
        return Promise.resolve()
      } catch (error) {
        console.error('取消订阅失败:', error)
        throw error
      }
    },
    
    // 获取微信授权码
    getWechatCode() {
      return new Promise((resolve, reject) => {
        uni.login({
          success: (loginRes) => {
            if (loginRes.code) {
              resolve(loginRes.code)
            } else {
              reject(new Error('获取微信授权码失败'))
            }
          },
          fail: (err) => {
            reject(new Error('微信登录失败'))
          }
        })
      })
    },
    
    // 获取用户openId
    getOpenId() {
      return new Promise((resolve, reject) => {
        // 优先从store中获取openId
        const userStore = useUserStore()
        if (userStore.userDetail && userStore.userDetail.wechatOpenId) {
          resolve(userStore.userDetail.wechatOpenId)
          return
        }
        
        // 如果store中没有，通过uni.login获取code，然后调用接口获取openId
        uni.login({
          success: async (loginRes) => {
            if (loginRes.code) {
              try {
                // 调用后端接口获取openId
                const userInfoRes = await profileApi.getUserInfoByCode({ code: loginRes.code })
                if (userInfoRes && userInfoRes.openid) {
                  // 更新用户store中的openId
                  if (userStore.userDetail) {
                    userStore.userDetail.wechatOpenId = userInfoRes.openid
                  }
                  resolve(userInfoRes.openid)
                } else {
                  reject(new Error('获取openid失败'))
                }
              } catch (error) {
                console.error('获取openId失败:', error)
                reject(error)
              }
            } else {
              reject(new Error('微信登录失败'))
            }
          },
          fail: (err) => {
            reject(new Error('微信登录失败'))
          }
        })
      })
    },
    

  }
}
</script>

<style lang="scss" scoped>
.subscribe-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.subscribe-tip {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  margin: 20rpx 30rpx;
  background-color: #e6f7ff;
  border-radius: 12rpx;
  border-left: 6rpx solid #1890ff;
  
  .tip-icon {
    margin-right: 16rpx;
  }
  
  .tip-text {
    font-size: 28rpx;
    color: #1890ff;
    line-height: 1.4;
  }
}

.subscribe-list {
  margin: 20rpx 30rpx;
}

.subscribe-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  
  .item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
  }
  
  .item-info {
    flex: 1;
    
    .item-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .item-desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.4;
    }
  }
  
  .item-switch {
    margin-left: 20rpx;
  }
}

.bottom-tip {
  padding: 40rpx 30rpx;
  text-align: center;
  
  .tip-text {
    font-size: 24rpx;
    color: #999;
  }
}

/* 微信授权区域样式 */
.auth-section {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.auth-tip {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff7e6;
  border-radius: 8rpx;
  border-left: 6rpx solid #ff9500;
  
  .tip-icon {
    margin-right: 16rpx;
  }
  
  .tip-text {
    font-size: 28rpx;
    color: #ff9500;
    line-height: 1.4;
  }
}

.auth-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
  }
  
  &::after {
    border: none;
  }
}

/* 开关样式优化 */
::v-deep .uni-switch {
  transform: scale(0.8);
}
</style>