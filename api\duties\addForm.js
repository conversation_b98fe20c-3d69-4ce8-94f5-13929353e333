import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 100000 // 设置超时时间为100秒
    })
}
const baseUrl = `/services/compliancelistservice/`
export default {
    // 创建合规风险清单
    riskForm(params, key) {
        switch (key) {
            case 'info':
            default:
                return request(
                    `${baseUrl}api/compliance/risk/list/create`,
                    params, 'post')
        }
    },
    // 提交合规风险清单
    riskSubmit(params, key) {
        switch (key) {
            case 'info':
            default:
                return request(
                    `${baseUrl}api/compliance/risk/list/submit`,
                    params, 'post')
        }
    },
    // 保存合规风险清单草稿
    riskDraft(params, key) {
        switch (key) {
            case 'info':
            default:
                return request(
                    `${baseUrl}api/compliance/risk/list/draft`,
                    params, 'post')
        }
    },
    // 创建职责
    dutyForm(params, key) {
        switch (key) {
            case 'info':
            default:
                return request(
                    `${baseUrl}api/duty/positions/submit`,
                    params, 'post')
        }
    },
    // 流程创建接口
    addProcess(params) {
        let ser = `/services/compliancelistservice/api/`
        return request(
            `${ser}v2/biz/process/list/submit`,
            params,
            'post'
        )
    },
    // 流程更新接口
    updateProcess(params, id) {
        let ser = `/services/compliancelistservice/api/`
        return request(
            `${ser}v2/biz/process/list/${id}`,
            params,
            'put'
        )
    },
    // 创建流程草稿接口
    createDraftProcess(params) { 
        let ser = `/services/compliancelistservice/api/`
        return request(
            `${ser}v2/biz/process/list/draft`,
            params,
            'post'
        )
    },
    // 查看流程详情
    getProcessDetail(id) { 
        let ser = `/services/compliancelistservice/api/`
        return request(
            `${ser}v2/biz/process/list/${id}/details`,
            {},
            'get'
        )
    },
    aiGenerate(params) {
        return request(
            `${baseUrl}api/compliance/risk/details/risk/generate`,
            {
                toolKey: params.toolKey, //AI工具类型，默认deepseek
                type: params.type, //生成类型（1：合规风险清单 2：重点岗位职责清单 3:关键业务流程清单）
                keywords: params.keywords, //生成内容的关键词(业务类型，岗位，流程)
                point: params.point // 内容描述点
            }, 'post')
    },

}