<template>
	<view class="container">
		<!-- 主要内容区 -->
		<scroll-view class="main-content" scroll-y>
			<!-- 课程封面和基本信息 -->
			<view class="course-header">
				<image class="course-cover" :src="detailObject.newImg" mode="aspectFill"></image>
				<text class="course-title">{{detailObject.courseName}}</text>
				<view class="course-meta">
					<view class="meta-item">
						<uv-icon name="account" size="20" color="#666"></uv-icon>
						<text class="meta-text">{{detailObject.instructor}}</text>
					</view>
					<view class="meta-item">
						<uv-icon name="clock" size="20" color="#666"></uv-icon>
						<text class="meta-text">{{ detailObject.durationMinutes }}分钟</text>
					</view>
				</view>
			</view>

			<!-- Tab导航 -->
			<view class="tab-bar">
				<view class="tab-item" :class="{ 'active-tab': activeTab === 'intro' }" @click="switchTab('intro')">
					<text>简介</text>
				</view>
				<view class="tab-item" :class="{ 'active-tab': activeTab === 'outline' }" @click="switchTab('outline')">
					<text>大纲</text>
				</view>
			</view>

			<!-- Tab内容区 -->
			<view class="tab-content">
				<!-- 简介内容 -->
				<view class="intro-content" v-if="activeTab === 'intro'">
					<text class="intro-text">{{ detailObject.courseOverview }}</text>
				</view>

				<!-- 大纲内容 -->
				<view class="outline-content" v-if="activeTab === 'outline'">
					<view class="chapter-list">
						<view v-for="(chapter, chapterIndex) in courseList" :key="chapter.id" class="chapter-item">
							<!-- 章节头部 -->
							<view @click="toggleChapter(chapterIndex)" class="chapter-header">
								<view class="chapter-info">
									<text class="chapter-title">{{ chapter.chapterTitle }}</text>
									<view class="chapter-meta">
										<text class="chapter-type">章节</text>
										<text class="chapter-duration"
											v-if="chapter.durationMinutes">{{ chapter.durationMinutes }}分钟</text>
										<text class="lesson-count"
											v-if="chapter.lessons && chapter.lessons.length">{{ chapter.lessons.length }}个课时</text>
									</view>
								</view>
								<view class="chapter-action">
									<uv-icon
										:name="expandedChapters.includes(chapterIndex) ? 'arrow-down' : 'arrow-right'"
										size="14" color="#999"></uv-icon>
								</view>
							</view>

							<!-- 课时列表 -->
							<view v-if="expandedChapters.includes(chapterIndex) && chapter.lessons"
								class="lessons-container">
								<view v-for="(lesson, lessonIndex) in chapter.lessons" :key="lesson.id"
									@click="handleLessonClick(chapter, lesson, chapterIndex, lessonIndex)"
									:class="['lesson-item', { 'lesson-clickable': isVideoLesson(lesson), 'lesson-document': !isVideoLesson(lesson) }]">
									<view class="lesson-icon">
										<uv-icon :name="getLessonIcon(lesson)" size="16"
											:color="getLessonIconColor(lesson)"></uv-icon>
									</view>
									<view class="lesson-info">
										<text class="lesson-title">{{ lesson.chapterTitle }}</text>
										<view class="lesson-meta">
											<text
												class="lesson-type">{{ getContentTypeText(lesson.chapterType) }}</text>
											<text class="lesson-duration"
												v-if="lesson.durationMinutes">{{ lesson.durationMinutes }}分钟</text>
											<text class="lesson-status"
												:class="getLessonStatusClass(lesson)">{{ getLessonStatusText(lesson) }}</text>
										</view>
									</view>
									<view class="lesson-action">
										<!-- 视频类型显示观看文本 -->
										<text v-if="isVideoLesson(lesson)" class="action-text">{{ getActionText(lesson.chapterType) }}</text>
										<!-- 文档类型显示预览按钮 -->
										<FilePreviewUrl v-else :url="lesson.contentUrl" :fileName="lesson.chapterTitle" />
									</view>
								</view>
							</view>
						</view>

						<!-- 无数据提示 -->
						<view v-if="courseList.length === 0" class="empty-state">
							<text class="empty-text">暂无课程内容</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import https from '@/api/study/index.js';
	import uploads from '@/api/upload.js'
	import FilePreviewUrl from '@/components/FilePreview/FilePreviewUrl.vue'
	import {
		ref
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app';
    import { useUserStore } from '@/store/pinia.js';
	const courseId = ref(''); // 用于存储课程ID
	const detailObject = ref({}); // 用于存储课程详情
	const courseList = ref([]); // 用于存储课程内容列表
	const activeTab = ref('intro');
	const expandedChapters = ref([]); // 展开的章节索引

	onLoad((options) => {
		courseId.value = options.id; // 获取课程ID
		initCourseData();
	});

	const switchTab = (tab) => {
		activeTab.value = tab;
	};

	// 切换章节展开状态
	const toggleChapter = (chapterIndex) => {
		const index = expandedChapters.value.indexOf(chapterIndex);
		if (index > -1) {
			expandedChapters.value.splice(index, 1);
		} else {
			expandedChapters.value.push(chapterIndex);
		}
	};

	// 判断是否为视频课时
	const isVideoLesson = (lesson) => {
		return lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video';
	};

	// 处理课时点击事件
	const handleLessonClick = (chapter, lesson, chapterIndex, lessonIndex) => {
		// 只有视频类型才能跳转到播放页面
		if (isVideoLesson(lesson)) {
			// 跳转到视频播放页面，传递章节和课时信息
			const courseObj = {
				courseId: courseId.value
			};

			const params = {
				courseObj: encodeURIComponent(JSON.stringify(courseObj)),
				chapterIndex: chapterIndex,
				lessonIndex: lessonIndex
			};

			const queryString = Object.keys(params)
				.map(key => `${key}=${params[key]}`)
				.join('&');

			uni.navigateTo({
				url: `/pages/component/study/courseVideo?${queryString}`
			});
		} else {
			// 文档类型不做任何操作，预览功能由FilePreviewUrl组件处理
			console.log('文档类型课时，使用预览功能');
		}
	};

	// 获取课时图标
	const getLessonIcon = (lesson) => {
		if (lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video') {
			return 'play-circle';
		} else if (lesson.chapterType === 'DOCUMENT' || lesson.chapterType === 'document') {
			return 'file-text';
		} else if (lesson.chapterType === 'FILE' || lesson.chapterType === 'file') {
			return 'download';
		}
		return 'file';
	};

	// 获取课时图标颜色
	const getLessonIconColor = (lesson) => {
		if (lesson.completionStatus === 'COMPLETED') {
			return '#10B981'; // 绿色表示已完成
		} else if (lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video') {
			return '#3B82F6'; // 蓝色表示视频
		}
		return '#6B7280'; // 灰色表示其他
	};

	// 获取课时状态文本
	const getLessonStatusText = (lesson) => {
		if (lesson.completionStatus === 'COMPLETED') {
			return '已完成';
		} else if (lesson.playbackPosition > 0) {
			return '进行中';
		}
		return '未开始';
	};

	// 获取课时状态样式类
	const getLessonStatusClass = (lesson) => {
		if (lesson.completionStatus === 'COMPLETED') {
			return 'status-completed';
		} else if (lesson.playbackPosition > 0) {
			return 'status-progress';
		}
		return 'status-pending';
	};

	// 获取内容类型文本
	const getContentTypeText = (chapterType) => {
		const typeMap = {
			'VIDEO': '视频',
			'video': '视频',
			'DOCUMENT': '文档',
			'document': '文档',
			'FILE': '附件',
			'file': '附件'
		};
		return typeMap[chapterType] || '内容';
	};

	// 获取操作文本
	const getActionText = (chapterType) => {
		if (chapterType === 'VIDEO' || chapterType === 'video') {
			return '观看';
		} else if (chapterType === 'DOCUMENT' || chapterType === 'document' || chapterType === 'FILE') {
			return '下载';
		}
		return '查看';
	};

	// 下载文件
	const downloadFile = (chapter) => {
		if (!chapter.contentUrl) {
			uni.showToast({
				title: '文件链接不存在',
				icon: 'none'
			});
			return;
		}

		uni.showLoading({
			title: '准备下载...'
		});

		uni.downloadFile({
			url: chapter.contentUrl,
			success: (res) => {
				uni.hideLoading();
				if (res.statusCode === 200) {
					uni.showToast({
						title: '下载成功',
						icon: 'success'
					});
					// 可以在这里添加打开文件的逻辑
					uni.openDocument({
						filePath: res.tempFilePath,
						success: () => {
							console.log('打开文档成功');
						},
						fail: (err) => {
							console.log('打开文档失败', err);
						}
					});
				}
			},
			fail: (err) => {
				uni.hideLoading();
				uni.showToast({
					title: '下载失败',
					icon: 'none'
				});
				console.error('下载失败:', err);
			}
		});
	};

	// 初始化课程数据
	const initCourseData = async () => {
		try {
			uni.showLoading({
				title: '加载中...'
			});

			// 获取课程详情
			const detailRes = await https.courseDetail(courseId.value);
			detailObject.value = detailRes;
			detailObject.value.newImg = await uploads.getFileUrl(detailObject.value.coverImageUrl);
			// 获取课程内容（章节和课时）
			const userStore = useUserStore()
            const userId = userStore.userId
			const contentRes = await https.getCourseContentByCourseId(courseId.value, {
				userId
			});
			courseList.value = contentRes || [];

			uni.hideLoading();
		} catch (error) {
			console.error('获取课程数据失败:', error);
			uni.hideLoading();
			uni.showToast({
				title: '获取课程数据失败',
				icon: 'none'
			});
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #f8f9fa;

		/* 主要内容区样式 */
		.main-content {
			flex: 1;
			// margin-top: 88rpx;
			// margin-bottom: 100rpx;
			overflow: auto;
		}

		.course-header {
			padding: 32rpx;
		}

		.course-cover {
			width: 100%;
			height: 320rpx;
			border-radius: 16rpx;
			margin-bottom: 24rpx;
		}

		.course-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 24rpx;
			display: block;
		}

		.course-meta {
			display: flex;
			align-items: center;
		}

		.meta-item {
			display: flex;
			align-items: center;
			margin-right: 48rpx;
		}

		.meta-text {
			font-size: 24rpx;
			color: #666;
			margin-left: 8rpx;
		}

		/* Tab栏样式 */
		.tab-bar {
			display: flex;
			border-bottom: 2rpx solid #eee;
			padding: 0 32rpx;
		}

		.tab-item {
			padding: 24rpx 32rpx;
			font-size: 28rpx;
			color: #666;
			position: relative;
		}

		.tab-item.active-tab {
			color: #3b82f6;
			font-weight: 500;
		}

		.tab-item.active-tab::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 80rpx;
			height: 4rpx;
			background-color: #3b82f6;
		}

		/* Tab内容区样式 */
		.tab-content {
			padding: 32rpx;
		}

		.intro-text {
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 24rpx;
			display: block;
		}

		.feature-box {
			background-color: #ebf5ff;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-top: 32rpx;
		}

		.feature-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #3b82f6;
			margin-bottom: 16rpx;
			display: block;
		}

		.feature-list {
			display: flex;
			flex-direction: column;
		}

		.feature-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 12rpx;
		}

		.feature-text {
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
			margin-left: 12rpx;
			flex: 1;
		}

		/* 大纲内容样式 */
		.chapter-list {
			display: flex;
			flex-direction: column;
		}

		.chapter-item {
			background-color: #fff;
			border-radius: 16rpx;
			margin-bottom: 24rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;
		}

		.chapter-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx;
			background-color: #fff;
			cursor: pointer;
			transition: background-color 0.2s;
		}

		.chapter-header:hover {
			background: #F9FAFB;
		}

		.chapter-info {
			flex: 1;
		}

		.chapter-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 8rpx;
			display: block;
		}

		.chapter-meta {
			display: flex;
			align-items: center;
			gap: 16rpx;
		}

		.chapter-type {
			font-size: 24rpx;
			color: #1A73E8;
			background-color: #E3F2FD;
			padding: 4rpx 12rpx;
			border-radius: 12rpx;
		}

		.chapter-duration {
			font-size: 24rpx;
			color: #666;
		}

		.lesson-count {
			font-size: 24rpx;
			color: #666;
		}

		.chapter-action {
			display: flex;
			align-items: center;
		}

		.action-text {
			font-size: 24rpx;
			color: #1A73E8;
			margin-right: 8rpx;
		}

		/* 课时列表样式 */
		.lessons-container {
			border-top: 1rpx solid #F3F4F6;
			background: #FAFAFA;
		}

		.lesson-item {
			display: flex;
			align-items: center;
			padding: 20rpx 24rpx;
			border-bottom: 1rpx solid #F3F4F6;
			transition: background-color 0.2s;
		}

		.lesson-clickable {
			cursor: pointer;
		}

		.lesson-clickable:hover {
			background: #F3F4F6;
		}

		.lesson-document {
			cursor: default;
		}

		.lesson-item:last-child {
			border-bottom: none;
		}

		.lesson-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.lesson-info {
			flex: 1;
			min-width: 0;
		}

		.lesson-title {
			font-size: 28rpx;
			color: #1F2937;
			margin-bottom: 6rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			display: block;
		}

		.lesson-meta {
			display: flex;
			align-items: center;
			gap: 12rpx;
		}

		.lesson-type {
			font-size: 22rpx;
			color: #6B7280;
			background: #E5E7EB;
			padding: 2rpx 8rpx;
			border-radius: 8rpx;
		}

		.lesson-duration {
			font-size: 22rpx;
			color: #6B7280;
		}

		.lesson-status {
			font-size: 22rpx;
			padding: 4rpx 8rpx;
			border-radius: 8rpx;
			font-weight: 500;
			white-space: nowrap;
		}

		.lesson-action {
			display: flex;
			align-items: center;
		}

		.status-completed {
			background: #D1FAE5;
			color: #065F46;
		}

		.status-progress {
			background: #DBEAFE;
			color: #1E40AF;
		}

		.status-pending {
			background: #F3F4F6;
			color: #6B7280;
		}

		.empty-state {
			text-align: center;
			padding: 80rpx 0;
		}

		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}
</style>