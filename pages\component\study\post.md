---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/考试管理

## POST 提交答案

POST /whiskerguardtrainingservice/api/exam/management/submit/all/answers

提交答案
{@code POST  /exam/management/submit/answer} : Submit an answer.
提交答案
提交用户对指定题目的答案

> Body 请求参数

```json
{
  "0": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|examRecordId|query|integer| 是 |考试记录ID 考试记录ID|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[MapString](#schemamapstring)| 否 |none|

> 返回示例

> 500 Response

```json
{
  "examRecordId": 0,
  "examName": "",
  "score": 0,
  "totalScore": 0,
  "correctCount": 0,
  "wrongCount": 0,
  "actualDuration": 0,
  "isPassed": false,
  "resultStatus": "",
  "endTime": {
    "seconds": 0,
    "nanos": 0
  },
  "certificateId": 0,
  "resultRemark": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|[ResponseEntityExamResultDTO](#schemaresponseentityexamresultdto)|

# 数据模型

<h2 id="tocS_ResponseEntityExamResultDTO">ResponseEntityExamResultDTO</h2>

<a id="schemaresponseentityexamresultdto"></a>
<a id="schema_ResponseEntityExamResultDTO"></a>
<a id="tocSresponseentityexamresultdto"></a>
<a id="tocsresponseentityexamresultdto"></a>

```json
{
  "examRecordId": 0,
  "examName": "string",
  "score": 0,
  "totalScore": 0,
  "correctCount": 0,
  "wrongCount": 0,
  "actualDuration": 0,
  "isPassed": true,
  "resultStatus": "string",
  "endTime": {
    "seconds": 0,
    "nanos": 0
  },
  "certificateId": 0,
  "resultRemark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|examRecordId|integer(int64)|false|none||考试记录ID|
|examName|string|false|none||考试名称|
|score|integer|false|none||考试得分|
|totalScore|integer|false|none||总分|
|correctCount|integer|false|none||正确题数|
|wrongCount|integer|false|none||错误题数|
|actualDuration|integer|false|none||考试用时（秒）|
|isPassed|boolean|false|none||是否合格|
|resultStatus|string|false|none||考试结果状态（合格/不合格）|
|endTime|[Instant](#schemainstant)|false|none||考试完成时间|
|certificateId|integer(int64)|false|none||证书ID（如果合格）|
|resultRemark|string|false|none||结果备注|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_MapString">MapString</h2>

<a id="schemamapstring"></a>
<a id="schema_MapString"></a>
<a id="tocSmapstring"></a>
<a id="tocsmapstring"></a>

```json
{
  "0": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|0|string|false|none||none|

