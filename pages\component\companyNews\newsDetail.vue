<template>
	<view class="news-detail-container">
		<!-- Main Content Area -->
		<scroll-view class="content-area" scroll-y>
			<view class="content-wrapper">
				<!-- Article Title -->
				<text class="article-title">新版财务审批制度正式上线</text>

				<!-- Meta Info -->
				<text class="meta-info">2025-04-18 | 作者：合规部</text>

				<!-- Article Content -->
				<view class="article-content">
					<text class="paragraph">为规范集团财务管理流程，提高审批效率，经集团董事会审议通过，全新《财务审批制度》将于2025年5月1日起正式实施。</text>
					<text class="paragraph">本次修订主要包含以下内容：</text>
					<view class="list-item">
						<uni-icons type="circle" size="12" color="#666" style="margin-right: 8px;"></uni-icons>
						<text>优化审批权限分级，明确各级审批人职责</text>
					</view>
					<view class="list-item">
						<uni-icons type="circle" size="12" color="#666" style="margin-right: 8px;"></uni-icons>
						<text>简化小额费用报销流程，5000元以下费用由部门负责人审批</text>
					</view>
					<view class="list-item">
						<uni-icons type="circle" size="12" color="#666" style="margin-right: 8px;"></uni-icons>
						<text>新增电子审批系统对接，支持移动端审批</text>
					</view>
					<view class="list-item">
						<uni-icons type="circle" size="12" color="#666" style="margin-right: 8px;"></uni-icons>
						<text>强化预算控制机制，超预算支出需特别审批</text>
					</view>
					<text class="paragraph">新制度实施后，原有审批流程将同步废止，请各部门及时组织学习培训。</text>

					<!-- Article Image -->
					<image src="https://ai-public.mastergo.com/ai/img_res/03ec3da0f3e9ded465d2f9b686bb73ea.jpg"
						mode="widthFix" class="article-image">
					</image>

					<text class="paragraph">为配合新制度实施，集团将于4月25日-30日期间组织全员培训，具体安排如下：</text>
					<view class="list-item">
						<text class="number">1.</text>
						<text>4月25日 9:00-11:30 总部大会议室</text>
					</view>
					<view class="list-item">
						<text class="number">2.</text>
						<text>4月26日 14:00-16:30 华东区分会场</text>
					</view>
					<view class="list-item">
						<text class="number">3.</text>
						<text>4月27日 10:00-12:30 华南区分会场</text>
					</view>
				</view>

				<!-- Attachments Section -->
				<view class="attachments">
					<text class="attachments-title">附件</text>
					<view class="attachment-list">
						<!-- Attachment Item 1 -->
						<view class="attachment-item">
							<view class="attachment-left">
								<uni-icons type="paperclip" size="20" color="#f44336"></uni-icons>
								<text class="attachment-name">财务审批制度.pdf</text>
							</view>
							<view class="attachment-right" @click="downloadFile('财务审批制度.pdf')">
								<text class="attachment-action">下载</text>
								<uni-icons type="download" size="16" color="#1A73E8"></uni-icons>
							</view>
						</view>

						<!-- Attachment Item 2 -->
						<view class="attachment-item">
							<view class="attachment-left">
								<uni-icons type="image" size="20" color="#2196F3"></uni-icons>
								<text class="attachment-name">宣传海报.png</text>
							</view>
							<view class="attachment-right" @click="previewFile('宣传海报.png')">
								<text class="attachment-action">预览</text>
								<uni-icons type="link" size="16" color="#1A73E8"></uni-icons>
							</view>
						</view>

						<!-- Attachment Item 3 -->
						<view class="attachment-item">
							<view class="attachment-left">
								<uni-icons type="download-filled" size="20" color="#4CAF50"></uni-icons>
								<text class="attachment-name">审批流程表.xlsx</text>
							</view>
							<view class="attachment-right" @click="downloadFile('审批流程表.xlsx')">
								<text class="attachment-action">下载</text>
								<uni-icons type="download" size="16" color="#1A73E8"></uni-icons>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';

	const goBack = () => {
		uni.navigateBack();
	};

	const downloadFile = (fileName) => {
		uni.showToast({
			title: `开始下载${fileName}`,
			icon: 'none'
		});
	};

	const previewFile = (fileName) => {
		uni.showToast({
			title: `预览${fileName}`,
			icon: 'none'
		});
	};

	const switchTab = (tabName) => {
		uni.showToast({
			title: `切换到${tabName}页面`,
			icon: 'none'
		});
	};
</script>

<style lang="scss" scoped>
	.news-detail-container {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #F5F7FA;

		/* Content Area */
		.content-area {
			flex: 1;
			margin-top: 30rpx;
			margin-bottom: 100rpx;
			overflow: auto;
		}

		.content-wrapper {
			padding: 32rpx;
		}

		.article-title {
			font-size: 36rpx;
			color: #333333;
			margin-bottom: 16rpx;
			display: block;
		}

		.meta-info {
			font-size: 24rpx;
			color: #666666;
			margin-bottom: 32rpx;
			display: block;
		}

		.article-content {
			font-size: 28rpx;
			color: #333333;
			line-height: 48rpx;
		}

		.paragraph {
			display: block;
			margin-bottom: 24rpx;
		}

		.list-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 16rpx;
			padding-left: 8rpx;
		}

		.number {
			margin-right: 16rpx;
			min-width: 40rpx;
		}

		.article-image {
			width: 100%;
			border-radius: 16rpx;
			margin: 32rpx 0;
		}

		/* Attachments */
		.attachments {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-top: 48rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		}

		.attachments-title {
			font-size: 28rpx;
			color: #333333;
			margin-bottom: 24rpx;
			display: block;
		}

		.attachment-list {
			border-top: 1rpx solid #EEEEEE;
		}

		.attachment-item {
			height: 96rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #EEEEEE;
			padding: 0 8rpx;
		}

		.attachment-item:last-child {
			border-bottom: none;
		}

		.attachment-left {
			display: flex;
			align-items: center;
		}

		.attachment-name {
			font-size: 28rpx;
			color: #1A73E8;
			margin-left: 16rpx;
		}

		.attachment-right {
			display: flex;
			align-items: center;
		}

		.attachment-action {
			font-size: 24rpx;
			color: #1A73E8;
			margin-right: 8rpx;
		}
	}
</style>