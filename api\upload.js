import http from '@/utils/request'
import { useUserStore } from '@/store/pinia.js';
import { config } from '@/config';

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
function requestUpload(url, filePath, formData = {}){
    console.log('filePath:', filePath);
    return new Promise((resolve, reject) => {
        const userStore = useUserStore();
        
        const uploadTask = uni.uploadFile({
            url: config.baseUrl + url,
            filePath: filePath,
            name: 'file',
            formData: formData,
            header: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${userStore.token ? userStore.token : null}`,
            },
            success: (uploadRes) => {
                try {
                    // 解析返回结果 - 与 uploadFile.vue 保持一致的逻辑
                    const result = typeof uploadRes.data === 'string' 
                        ? JSON.parse(uploadRes.data) 
                        : uploadRes.data;
                    
                    resolve(result);
                } catch (e) {
                    console.error('解析上传结果失败', e);
                    console.error('原始响应数据:', uploadRes.data);
                    reject(e);
                }
            },
            fail: (err) => {
                console.error('上传失败', err);
                reject(err);
            }
        });
        
        // 可选：监听上传进度
        uploadTask.onProgressUpdate && uploadTask.onProgressUpdate((res) => {
            console.log('上传进度:', res.progress + '%');
        });
    });
}
const baseUrl = `/services/whiskerguardgeneralservice/api/file/getFileUrl`

export default {
    // 正式的文件或者地址
    getFileUrl(key) {
        return request(baseUrl + `?key=${key}`, {}, 'get')
    },
    // 上传图片地址
    uploadUrl(filePath, tenantId, serviceName = 'avatar', categoryName = 'user') {
        return requestUpload(`/services/whiskerguardgeneralservice/api/file/upload?serviceName=${serviceName}&categoryName=${categoryName}`, filePath)
    },
}