import myHttp from '@/utils/request'
import {
	config
} from '@/config'

function request(url, params, method) {
	return myHttp({
		url: url,
		method: method ? method : 'POST',
		data: params,
		timeout: 1000000
	})
}
const services = `/services/whiskerguardcontractservice/api/`
const baseUrl = '/services/'
export default {
	// 合同信息列表
	contractReview(params, key) {
		switch (key) {
			case 'info':
			// return request(`/setting/info`, params)
			default:
				return request(
					`${services}contract/reviews/page?page=${params.page}&size=${params.size}`,
					params, 'POST')
		}
	},
	// 合同信息详情
	contractReviewDetail(params) {
		return request(
			`${services}contract/reviews/${params.id}`,
			params, 'GET')
	},

	// 决策列表
	decisionList(params) {
		return request(
			`${services}decision/reviews/page?page=${params.page}&size=${params.size}`,
			params, 'POST')
	},
	// 决策详情
	decisionDetail(params) {
		return request(
			`${services}decision/reviews/${params.id}`,
			params, 'GET')
	},
	// 其他列表
	supplementalList(params) {
		return request(
			`${services}supplemental/reviews/page?page=${params.page}&size=${params.size}`,
			params, 'POST')
	},
	// 其他详情
	supplementalDetail(params) {
		return request(
			`${services}supplemental/reviews/${params.id}`,
			{}, 'GET')
	},
	// 企业监管审核记录
	companyReview(page, params) {
		return request(
			`${baseUrl}whiskerguardregulatoryservice/api/enterprise/regulation/audits/page?page=${page.page}&size=${page.size}`,
			params,
			'post'
		)
	},
	// 制度ai智能审查
	aiReview(id) {
		return request(
			`${baseUrl}whiskerguardregulatoryservice/api/enterprise/regulation/audits/ai/review/${id}`,
			{},
			'get'
		)
	},
	// 获取制度详情
	regulationDetail(regulationId) {
		return request(
			`${baseUrl}whiskerguardregulatoryservice/api/enterprise/regulation/audits/ai/review/${regulationId}`,
			{},
			'GET'
		)
	},
	//版本更新记录
	getRegulationVersion(regulationId) {
		return request(
			`${baseUrl}whiskerguardregulatoryservice/api/enterprise/regulation/histories/page/${regulationId}`,
			{},
			'GET'
		)
	},
	//制度审查
	regulationReview(params) {
		return request(
			`${baseUrl}whiskerguardregulatoryservice/api/enterprise/regulation/audits/review`,
			params,
			'POST'
		)
	},
}