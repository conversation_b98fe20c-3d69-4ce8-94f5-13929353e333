<!-- components/filter-tags.vue -->
<template>
	<view class="filter-section" :class="{ multiple: multiple }" :style="{ 
      position: fixed ? 'fixed' : 'relative' 
    }">
		<scroll-view class="filter-scroll" scroll-x :show-scrollbar="false">
			<view class="filter-tags">
				<text v-for="(tag, index) in tags" :key="index" class="tag" :class="{ 
            active: isActive(tag[valueKey]),
            'multi-active': multiple && isMultiActive(tag[valueKey])
          }" @click="handleTagClick(tag)">
					{{ tag[displayKey] }}
					<text v-if="multiple && isMultiActive(tag[valueKey])" class="badge">✓</text>
				</text>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import {
		ref,
		watch,
		computed
	} from 'vue'

	const props = defineProps({
		tags: {
			type: Array,
			default: () => [],
			// validator: val => val.every(item => item.name && item.id)
		},
		modelValue: {
			type: [String, Number, Array],
			default: () => ([])
		},
		// top: {
		//   type: Number,
		//   default: 88
		// },
		fixed: {
			type: Boolean,
			default: true
		},
		multiple: {
			type: Boolean,
			default: false
		},
		displayKey: {
			type: String,
			default: 'name'
		},
		valueKey: {
			type: String,
			default: 'id'
		}
	})

	const emit = defineEmits(['update:modelValue', 'change'])

	// 样式计算
	const computedTop = computed(() => `${props.top}rpx`)

	// 判断选中状态
	const isActive = (value) => {
		return !props.multiple && currentValue.value === value
	}

	// 多选激活状态
	const isMultiActive = (value) => {
		return props.multiple && selectedValues.value.includes(value)
	}

	// 当前值处理
	const currentValue = ref('')
	const selectedValues = ref([])

	// 初始化选中状态
	const initSelection = () => {
		if (props.multiple) {
			selectedValues.value = Array.isArray(props.modelValue) ?
				[...props.modelValue] :
				[]
		} else {
			currentValue.value = props.modelValue
		}
	}

	// 处理标签点击
	const handleTagClick = (tag) => {
		const value = tag[props.valueKey]

		if (props.multiple) {
			const index = selectedValues.value.indexOf(value)
			index === -1 ?
				selectedValues.value.push(value) :
				selectedValues.value.splice(index, 1)

			selectedValues.value.sort()
			emit('update:modelValue', [...selectedValues.value])
			emit('change', [...selectedValues.value])
		} else {
			currentValue.value = value
			emit('update:modelValue', value)
			emit('change', value)
		}
	}

	// 监听modelValue变化
	watch(() => props.modelValue, (newVal) => {
		if (props.multiple) {
			selectedValues.value = Array.isArray(newVal) ? [...newVal] : []
		} else {
			currentValue.value = newVal
		}
	}, {
		immediate: true
	})

	// 监听multiple模式切换
	watch(() => props.multiple, (newVal) => {
		initSelection()
		emit('update:modelValue', newVal ? [] : '')
	})
</script>

<style lang="scss" scoped>
	/* 保持原有样式不变 */
	.filter-section {
		height: 88rpx;
		background-color: #fff;
		padding: 0 32rpx;
		position: fixed;
		// top: 88rpx;
		left: 0;
		right: 0;
		z-index: 1;

		&.multiple {
			height: 96rpx;
		}
	}

	.filter-scroll {
		height: 100%;
		// width: 100%;
		// white-space: nowrap;
	}

	.filter-tags {
		height: 100%;
		display: flex;
		align-items: center;
		gap: 20rpx;
		padding-right: 32rpx; /* 确保最后一个标签完整显示 */
		// white-space: nowrap;
	}

	.tag {
		position: relative;
		font-size: 28rpx;
		font-weight: 500;
		padding: 0 32rpx;
		height: 56rpx;
		line-height: 56rpx;
		border-radius: 28rpx;
		background-color: #EFEFF4;
		color: #666;
		transition: all 0.2s ease;
		flex-shrink: 0;

		&.active,
		&.multi-active {
			background-color: #1A73E8;
			color: #fff;
			font-weight: 500;
		}

		&.multi-active {
			padding-right: 48rpx;
		}

		.badge {
			position: absolute;
			right: 12rpx;
			top: 50%;
			transform: translateY(-50%);
			font-size: 20rpx;
		}

		&:active {
			opacity: 0.8;
		}
	}
</style>