import myHttp from '@/utils/request'
function request(url, params, method) {
	return myHttp({
		url: url,
		method: method ? method : 'POST',
		data: params
	})
}
const services = '/services/whiskerguardorgservice/'
export default {
	unitTree(params) {
		return request(
			`${services}api/org/units/tree`,
			{},
			'GET'
		)
	},
	// 根据岗位id获取部门
	getDeptByPostId(params) {
		return request(`/services/whiskerguardorgservice/api/positions/dept/positions/${params.id}`,
			{
			}, 'get')
	},
	// 根据部门获取 员工
	getEmpByUnitId(orgUnitId) {
		return request(`/services/whiskerguardorgservice/api/employees/org/unit/${orgUnitId}`,
			{
			}, 'get')
	},
	// 获取岗位列表
	getPostList() {
		return request(`/services/whiskerguardorgservice/api/positions`,
			{
				page: 0,
				size: 100
			}, 'get')
	},
	// 获取指定ID的员工
	getEmpById(id) {
		return request(`/services/whiskerguardorgservice/api/employees/${id}`,
			{
			}, 'get')
	},
	// 更新员工信息
	updateEmp(userId, params) {
		return request(`/services/whiskerguardorgservice/api/employees/${userId}`,
			params, 'patch')
	},
	// 更新头像
	updateAvatar(params) {
		return request(`/services/whiskerguardorgservice/api/employees/avatar?id=${params.id}&avatar=${params.avatar}`,
			{}, 'put')
	},
	// 获取所有员工
	getEmpList() {
		return request(`/services/whiskerguardorgservice/api/employees/page`, {
			page: 0,
			size: 100
		}, 'post')
	}
}