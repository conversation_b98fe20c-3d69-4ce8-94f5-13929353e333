<template>
	<view class="my-report-container">
		<z-paging ref="paging" v-model="list" @query="queryList">
			<template #top>
				<header-bar v-model="searchTerm" shape="circle" prefixIcon="search" clearable :fixed="false"
					@confirm="searchBtn">
					<template #right>
						<!-- 自定义操作按钮 -->
						<view class="nav-right">
							<!-- <view @click="searchBtn" class="nav-btn">
                <uni-icons type="search" size="20" />
              </view> -->
							<!-- 新增 -->
							<view @click="handleAdd" class="nav-btn">
								<text class="btn-text">＋新增</text>
								<!-- <uv-button @click="handleAdd" type="primary" :plain="true" text="+新增"></uv-button> -->
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags :fixed="false" :tags="tags" v-model="reportStatus" />
			</template>
			<view class="list-container">
				<view v-for="(item, index) in list" :key="index" class="report-card" @click="handleItemClick({item, index})">
					<view>
						<view class="card-header">
							<text class="card-title">违规内容举报：{{ item.title }}</text>
						</view>
						<view class="card-footer">
							<text class="card-tag">{{ getViolationTypeText(item.violationType) }}</text>
							<text class="card-divider">·</text>
							<text class="card-date">{{ item.createdStart }}</text>
							<text class="status-badge status-pending">{{ item.status == 'PENDING' ? '待处理' :
								item.status == 'REPLIED' ? '已回复' : '已关闭' }}</text>
						</view>
					</view>
				</view>
			</view>

		</z-paging>
	</view>
</template>

<script setup>
import {
	ref,
	nextTick,
	watch,
	onMounted
} from 'vue';
import {
	onShow
} from '@dcloudio/uni-app';
import headerBar from '@/components/headerBar.vue'
	import filterTags from '@/components/filterTags.vue'
	import https from '@/api/violation/report.js'

import {
	useUserStore
} from '@/store/pinia.js';
import getDictData from '@/utils/dict.js';

const userStore = useUserStore();

// 字典数据
const dictData = ref([]);

// 获取字典数据
onMounted(async () => {
	try {
		const res = await getDictData('45');
		if (res && res.length > 0) {
			dictData.value = res;
		}
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
});

// 页面显示时刷新列表
onShow(() => {
	reload();
});

// 根据violationType映射文字
const getViolationTypeText = (violationType) => {
	if (!violationType || !dictData.value.length) {
		return violationType || '未知类型';
	}
	const item = dictData.value.find(dict => dict.value === violationType);
	console.log('item', item);
	return item ? item.name : violationType;
};


// 这里可以添加相关的逻辑代码
const tags = [{
	name: '全部',
	id: null
},
{
	name: '待处理',
	id: 'PENDING'
},
{
	name: '已回复',
	id: 'REPLIED'
},
{
	name: '已关闭',
	id: 'CLOSED'
},
];

const reportStatus = ref(null);
// 跳转
function handleAdd() {
	uni.navigateTo({
		url: '/pages/component/monitor/report',
	})
}

const list = ref([])
const paging = ref(null);
const searchTerm = ref('');

function searchBtn() {
	reload();
}

function reload() {
	nextTick(() => {
		paging.value?.reload();
	});
}

watch(reportStatus, () => {
	reload();
})

const handleItemClick = (item) => {
	uni.navigateTo({
		url: `/pages/component/monitor/reportDetail?id=${item.item.id}`
	});
};

async function queryList(pageNo, pageSize) {
	const params = {
		title: searchTerm.value,
		status: reportStatus.value
	}
	const page = {
		page: pageNo - 1,
		size: pageSize,
	}

	uni.showLoading({
		title: '加载中'
	});

	try {
		const res = await https.getList(params, page);
		const data = res.content;
		paging.value.complete(data);
	} catch (err) {
		console.error('获取列表数据失败:', err);
		paging.value.complete(false);
	} finally {
		uni.hideLoading();
	}
}
</script>

<style lang="scss" scoped>
@import '/static/css/nav.scss';

.my-report-container {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #F5F7FA;

		.list-container {
			padding: 20rpx 32rpx;
		}

		.report-card {
			width: 100%;
			background-color: #fff;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
			box-sizing: border-box;
		}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.card-title {
		font-size: 32rpx;
		color: #333333;
	}

	.card-footer {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		flex-wrap: wrap;
	}

	.card-tag {
		color: #666666;
	}

	.card-divider {
		color: #999999;
		margin: 0 8rpx;
	}

	.card-date {
		color: #999999;
	}

	.status-badge {
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		font-size: 24rpx;
		flex-shrink: 0;
	}

	.status-pending {
		background-color: #FFF4E5;
		color: #DD6B20;
	}

	.status-replied {
		background-color: #E6F4FF;
		color: #1A73E8;
	}

	.status-closed {
		background-color: #E6E6E6;
		color: #999999;
	}

}
</style>