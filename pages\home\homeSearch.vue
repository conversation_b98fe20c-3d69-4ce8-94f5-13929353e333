
<template>
  <view class="search-page">
    <!-- 顶部搜索栏 -->
     	<header-bar shape="circle" @confirm="handleSearch" prefixIcon="search" clearable
					v-model="searchKeyword" :fixed="false" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn">
								<text @click="handleCancel" class="cancel-btn">取消</text>
							</view>
						</view>
					</template>
				</header-bar>
    <!-- <view class="search-bar">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="20" color="#999"></uni-icons>
        <input
          class="search-input"
          placeholder="请输入关键词..."
          placeholder-class="placeholder"
          @focus="handleFocus"
          v-model="searchKeyword"
          @confirm="handleSearch"
        />
      </view>
      <text class="cancel-btn" @click="handleCancel">取消</text>
    </view> -->
    <!-- 最近搜索和热门搜索 -->
    <view class="search-tags" v-if="!showResults">
      <view class="section">
        <view class="section-header search-tag-title">
          <text class="section-title">最近搜索</text>
          <text class="clear-btn" @click="clearHistory" v-if="recentSearches.length">清空</text>
        </view>
        <scroll-view scroll-x class="tag-scroll">
          <view class="tag-container">
            <view
              class="tag"
              v-for="(item, index) in recentSearches"
              :key="'recent-' + index"
              @click="handleTagClick(item)"
            >
              {{ item }}
            </view>
          </view>
        </scroll-view>
      </view>

      <view class="section">
        <view class="section-header search-tag-title">
          <text class="section-title">热门搜索</text>
        </view>
        <scroll-view scroll-x class="tag-scroll">
          <view class="tag-container">
            <view
              class="tag"
              v-for="(item, index) in hotSearches"
              :key="'hot-' + index"
              @click="handleTagClick(item)"
            >
              {{ item }}
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <scroll-view class="search-results" scroll-y v-if="showResults">
      <!-- 内规制度 -->
      <view class="result-section" v-if="internalTotal>0">
        <view class="section-header">
          <uni-icons type="book" size="20" color="#333"></uni-icons>
          <text class="section-title">内规制度（共找到 {{ internalTotal }} 条）</text>
        </view>
        <view class="result-list">
          <view
            class="result-item"
            v-for="(item, index) in internalResults"
            :key="'internal-' + index"
            @click="goToDetail('internal', item)"
          >
            <text class="result-title">{{ item.title }}</text>
            <text class="result-subtitle">{{ item.updatedAt }}</text>
          </view>
          <view class="view-all" @click="viewAll('internal')" v-if="internalResults.length >= 5">
            <text>查看更多</text>
            <uni-icons type="right" size="16" color="#1890FF"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 法律法规 -->
      <view class="result-section" v-if="lawTotal>0">
        <view class="section-header">
          <text class="section-title">法律法规（共找到 {{ lawTotal }} 条）</text>
        </view>
        <view class="result-list">
          <view
            class="result-item"
            v-for="(item, index) in lawResults"
            :key="'law-' + index"
            @click="goToDetail('law', item)"
          >
            <text class="result-title">{{ item.title }}</text>
            <text class="result-subtitle">{{ item.updatedAt }}</text>
          </view>
          <view class="view-all" @click="viewAll('law')" v-if="lawResults.length >= 5">
            <text>查看更多</text>
            <uni-icons type="right" size="16" color="#1890FF"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 合同信息 -->
      <view class="result-section" v-if="contractTotal>0">
        <view class="section-header">
          <text class="section-title">合同（共找到 {{ contractTotal }} 条）</text>
        </view>
        <view class="result-list">
          <view
            class="result-item"
            v-for="(item, index) in contractResults"
            :key="'contract-' + index"
            @click="goToDetail('contract', item)"
          >
            <text class="result-title">{{ item.name || item.title }}</text>
            <text class="result-subtitle">{{ item.updatedAt }}</text>
          </view>
          <view class="view-all" @click="viewAll('contract')" v-if="contractResults.length >= 5">
            <text>查看更多</text>
            <uni-icons type="right" size="16" color="#1890FF"></uni-icons>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import headerBar from '@/components/headerBar.vue'
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import homeApi from '@/api/home/<USER>'

// 搜索关键词
const searchKeyword = ref('');

// 是否显示搜索结果
const showResults = ref(false);

// 最近搜索
const recentSearches = ref([]);

// 热门搜索
const hotSearches = ref([
  '员工手册',
  '三张清单',
  '责任追究',
  '合同管理',
  '采购制度',
  '绩效合规',
  '员工合同',
  '数据保护'
]);

// 搜索结果数据
const internalResults = ref([]);
const lawResults = ref([]);
const contractResults = ref([]);
//搜索总数
const internalTotal = ref(0);
const lawTotal = ref(0);
const contractTotal = ref(0);

// 加载最近搜索记录
onLoad(() => {
  const history = uni.getStorageSync('searchHistory') || [];
  recentSearches.value = history;
});

// 处理搜索框聚焦
const handleFocus = () => {
  // 这里可以添加一些聚焦时的逻辑
};

// 处理取消按钮点击
const handleCancel = () => {
  uni.navigateBack();
};

// 处理搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) return;

  // 添加到最近搜索
  addToRecentSearches(searchKeyword.value);

  // 显示加载提示
  uni.showLoading({
    title: '正在搜索...'
  });

  try {
    // 调用真实搜索API
    await searchResults();
    showResults.value = true;
  } finally {
    // 隐藏加载提示
    uni.hideLoading();
  }
};

// 处理标签点击
const handleTagClick = (keyword) => {
  searchKeyword.value = keyword;
  handleSearch();
};

// 添加到最近搜索
const addToRecentSearches = (keyword) => {
  // 去重
  const index = recentSearches.value.indexOf(keyword);
  if (index !== -1) {
    recentSearches.value.splice(index, 1);
  }

  // 添加到最前面
  recentSearches.value.unshift(keyword);

  // 最多保留5条
  if (recentSearches.value.length > 5) {
    recentSearches.value = recentSearches.value.slice(0, 5);
  }

  // 保存到本地
  uni.setStorageSync('searchHistory', recentSearches.value);
};

// 清空搜索历史
const clearHistory = () => {
  recentSearches.value = [];
  uni.removeStorageSync('searchHistory');
};

// 查看更多
const viewAll = (type) => {
  let url = '';
  switch (type) {
    case 'internal':
      // 内规制度跳转到 regulations 页面?keyword=${searchKeyword.value}
      url = `/pages/regulations/regulations?tab=1`;
      break;
    case 'law':
      // 法律法规跳转到 regulations 页面
      url = `/pages/regulations/regulations?tab=2`;
      break;
    case 'contract':
      // 合同跳转到 complianceReview 页面?keyword=${searchKeyword.value}
      url = `/pages/component/respond/complianceReview`;
      break;
    default:
      uni.showToast({
        title: '未知类型',
        icon: 'none'
      });
      return;
  }

  uni.navigateTo({
    url: url
  });
};


// 真实搜索结果
const searchResults = async () => {
  try {
    const keyword = searchKeyword.value;
    const res = await homeApi.homeSearch({ keyword });
    
    if (res) {
      // 内规制度结果
      internalResults.value = res.enterpriseRegulations || [];
       internalTotal.value = res.enterpriseRegulationsCount || 0;
      // 法律法规结果
      lawResults.value = res.lawsRegulations || [];
      lawTotal.value = res.lawsRegulationsCount || 0;
      // 合同结果
      contractResults.value = res.contractReviews || [];
      contractTotal.value = res.contractReviewsCount || 0;
    }
  } catch (error) {
    console.error('搜索失败:', error);
    // 清空结果
    internalResults.value = [];
    lawResults.value = [];
    contractResults.value = [];
  }
};

// 跳转到详情页面
const goToDetail = (type, item) => {
  if (!item.id) {
    uni.showToast({
      title: '数据异常',
      icon: 'none'
    });
    return;
  }

  let url = '';
  switch (type) {
    case 'internal':
      // 内规制度跳转到 detail.vue
      url = `/pages/component/regulations/detail?id=${item.id}`;
      break;
    case 'law':
      // 法律法规跳转到 regulationDetail.vue
      url = `/pages/component/regulations/regulationDetail?id=${item.id}`;
      break;
    case 'contract':
      // 合同跳转到 contractReviewDetail.vue
      url = `/pages/component/respond/contractReviewDetail?id=${item.id}`;
      break;
    default:
      uni.showToast({
        title: '未知类型',
        icon: 'none'
      });
      return;
  }

  uni.navigateTo({
     url: url
   });
 };
</script>

<style lang="scss" scoped>
.search-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 搜索栏样式 */
// .search-bar {
//   display: flex;
//   align-items: center;
//   padding: 16rpx 24rpx;
//   background-color: #fff;
//   position: relative;
//   z-index: 10;
// }

// .search-input-wrapper {
//   flex: 1;
//   height: 72rpx;
//   background-color: #f5f5f5;
//   border-radius: 8px;
//   display: flex;
//   align-items: center;
//   padding: 0 24rpx;
//   margin-right: 24rpx;
// }

// .search-input {
//   flex: 1;
//   height: 100%;
//   font-size: 28rpx;
//   color: #333;
//   padding-left: 16rpx;
// }

// .placeholder {
//   color: #999;
//   font-size: 28rpx;
// }

// .cancel-btn {
//   font-size: 28rpx;
//   color: #666;
// }

/* 搜索标签区域 */
.search-tags {
  padding: 24rpx;
  background-color: #fff;
}

.section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24rpx;
}
.search-tag-title{
    justify-content: space-between;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-btn {
  font-size: 24rpx;
  color: #999;
}

.tag-scroll {
  width: 100%;
  white-space: nowrap;
}

.tag-container {
  display: inline-flex;
  flex-wrap: nowrap;
}

.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 32rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
}

/* 搜索结果区域 */
.search-results {
  height: 100%;
  flex: 1;
  padding: 24rpx;
  background-color: #f8f8f8;
  box-sizing: border-box;
}

.result-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.result-section .section-header {
  margin-bottom: 24rpx;
}

.result-section .section-title {
  margin-left: 12rpx;
}

.result-list {
  display: flex;
  flex-direction: column;
}

.result-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-item:hover {
  background-color: #f8f9fa;
}

.result-item:active {
  background-color: #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
}

.result-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  display: inline;
}

.result-subtitle {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}



.view-all {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 24rpx;
  color: #1890FF;
  font-size: 26rpx;
}

.view-all text {
  margin-right: 8rpx;
}
</style>

