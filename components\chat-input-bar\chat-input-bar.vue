<!-- z-paging聊天输入框 -->

<template>
	<view class="chat-input-bar-container">
		<!-- 上传文件展示区域 -->
		<view class="upload-preview-container" v-if="uploadedFiles.length > 0">
			<scroll-view scroll-x class="upload-preview-scroll">
				<view class="upload-preview-list">
					<view class="upload-item" v-for="(item, index) in uploadedFiles" :key="index">
						<view class="upload-item-content">
							<!-- 图片预览 -->
							<image v-if="item.type === 'image'" class="upload-preview-image" :src="item.url" mode="aspectFill"></image>
							<!-- 文件预览 -->
							<view v-else class="upload-file-preview">
								<text class="file-icon">📄</text>
								<text class="file-name">{{item.name}}</text>
							</view>
						</view>
						<!-- 删除按钮 -->
						<view class="upload-item-delete" @click="removeUploadedFile(index)">
							<text class="delete-icon">×</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<view class="chat-input-bar">
			<view class="chat-input-container">
				<!-- :adjust-position="false"必须设置，防止键盘弹窗自动上顶，交由z-paging内部处理 -->
				<input :focus="focus" class="chat-input" v-model="msg" :adjust-position="false" confirm-type="send" type="text" placeholder="请输入内容" @confirm="sendClick" />
			</view>
			<!-- 加号图标 -->
			<view class="plus-container">
				<view class="plus-icon" @click="plusChange">
					<text class="plus-text">+</text>
				</view>
			</view>
			<view :class="{'chat-input-send': true, 'chat-input-send-disabled': !sendEnabled}" @click="sendClick">
				<text class="chat-input-send-text">发送</text>
			</view>
		</view>
		<!-- 功能面板，包含表情选择 -->
		<view class="function-panel-container" :style="[{height: showFunctionPanel ? '300rpx' : '0px'}]">
			<view class="emoji-panel">
				<view class="emoji-grid">
					<view class="emoji-item" v-for="emoji in emojiList" :key="emoji" @click="selectEmoji(emoji)">
						<text class="emoji-text">{{emoji}}</text>
					</view>
				</view>
				<!-- 隐藏的上传功能，暂时不显示 -->
				<view class="upload-functions" style="display: none;">
					<view class="function-item" @click="uploadImage">
						<view class="function-icon">
							<text class="iconfont icon-image">📷</text>
						</view>
						<text class="function-text">上传图片</text>
					</view>
					<view class="function-item" @click="uploadFile">
						<view class="function-icon">
							<text class="iconfont icon-file">📁</text>
						</view>
						<text class="function-text">上传文件</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"chat-input-bar",
		props: {
			disabled: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				msg: '',
				
				// 当前input focus
				focus: false,
				// 是否显示功能面板
				showFunctionPanel: false,
				// 上传的文件列表
				uploadedFiles: [],
				// 表情列表
				emojiList: [
					'😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
					'😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
					'😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
					'🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
					'😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
					'😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
					'😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
					'😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
					'😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
					'😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
					'🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
					'🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
					'💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
					'😹', '😻', '😼', '😽', '🙀', '😿', '😾', '👋',
					'🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞',
					'🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇',
					'☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏',
					'🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅', '🤳',
					'💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃',
					'🧠', '🦷', '🦴', '👀', '👁️', '👅', '👄', '💋'
				],
			};
		},
		computed: {
			sendEnabled() {
				return !this.disabled && this.msg.length;
			}
		},
		methods: {
			// 更新了键盘高度
			updateKeyboardHeightChange(res) {
				if (res.height > 0) {
					// 键盘展开，隐藏功能面板
					this.showFunctionPanel = false;
				}
			},
			// 用户尝试隐藏键盘
			hidedKeyboard() {
				if (this.showFunctionPanel) {
					this.showFunctionPanel = false;
				}
			},
			// 点击了加号按钮
			plusChange() {
				if (this.showFunctionPanel) {
					// 如果功能面板已显示，则隐藏
					this.showFunctionPanel = false;
				} else {
					// 隐藏键盘并显示功能面板
					this.focus = false;
					uni.hideKeyboard();
					this.showFunctionPanel = true;
				}
			},
			// 上传图片
			uploadImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						// 添加到上传文件列表
						this.uploadedFiles.push({
							type: 'image',
							url: tempFilePaths[0],
							name: '图片'
						});
						this.$emit('uploadImage', tempFilePaths[0]);
						this.showFunctionPanel = false;
					},
					fail: (err) => {
						console.error('选择图片失败:', err);
					}
				});
			},
			// 上传文件
			uploadFile() {
				// #ifdef H5
				// H5环境下使用input file
				const input = document.createElement('input');
				input.type = 'file';
				input.accept = '*/*';
				input.onchange = (e) => {
					const file = e.target.files[0];
					if (file) {
						// 添加到上传文件列表
						this.uploadedFiles.push({
							type: 'file',
							url: '',
							name: file.name,
							file: file
						});
						this.$emit('uploadFile', file);
						this.showFunctionPanel = false;
					}
				};
				input.click();
				// #endif
				
				// #ifndef H5
				// 小程序环境下使用chooseMessageFile
				uni.chooseMessageFile({
					count: 1,
					type: 'file',
					success: (res) => {
						const tempFiles = res.tempFiles;
						if (tempFiles && tempFiles.length > 0) {
							const file = tempFiles[0];
							// 添加到上传文件列表
							this.uploadedFiles.push({
								type: 'file',
								url: file.path,
								name: file.name,
								file: file
							});
							this.$emit('uploadFile', file);
							this.showFunctionPanel = false;
						}
					},
					fail: (err) => {
						console.error('选择文件失败:', err);
					}
				});
				// #endif
			},
			// 移除上传的文件
			removeUploadedFile(index) {
				this.uploadedFiles.splice(index, 1);
			},
			
			// 选择表情
			selectEmoji(emoji) {
				// 将表情插入到输入框当前光标位置
				this.msg += emoji;
				// 隐藏功能面板
				this.showFunctionPanel = false;
				// 重新聚焦输入框
				this.focus = true;
			},
			
			// 点击了发送按钮
			sendClick() {
				if (!this.sendEnabled) {
					return;
				}
				// 暂时只发送文本消息，图片和文件信息不发送（接口暂不支持）
				this.$emit('send', this.msg);
				// 清空输入框和上传文件列表
				this.msg = '';
				this.uploadedFiles = [];
			},
			
			// 设置输入框的值（供外部调用）
			setInputValue(value) {
				this.msg = value || '';
				// 聚焦输入框
				this.focus = true;
			},
		}
	}
</script>

<style scoped>
	.chat-input-bar {
		display: flex;
		flex-direction: row;
		align-items: center;
		border-top: solid 1px #f5f5f5;
		background-color: #f8f8f8;
		
		padding: 10rpx 20rpx;
	}
	.chat-input-container {
		flex: 1;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		padding: 15rpx;
		background-color: white;
		border-radius: 10rpx;
	}
	.chat-input {
		flex: 1;
		font-size: 28rpx;
	}
	.plus-container {
		width: 54rpx;
		height: 54rpx;
		margin: 10rpx 0rpx 10rpx 20rpx;
	}
	.plus-icon {
		width: 54rpx;
		height: 54rpx;
		/* #ifndef APP-NVUE */
		display: flex;
		justify-content: center;
		align-items: center;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex-direction: row;
		justify-content: center;
		align-items: center;
		/* #endif */
		position: relative;
	}
	.plus-text {
		color: #666;
		font-size: 40rpx;
		font-weight: 300;
		line-height: 54rpx;
		text-align: center;
		/* #ifdef APP-NVUE */
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		/* #endif */
	}
	.chat-input-send {
		background-color: #007AFF;
		margin: 10rpx 10rpx 10rpx 20rpx;
		border-radius: 10rpx;
		width: 110rpx;
		height: 60rpx;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
	}
	.chat-input-send-disabled {
		background-color: #bbbbbb;
	}
	.chat-input-send-text {
		color: white;
		font-size: 26rpx;
	}
	.function-panel-container {
		background-color: #f8f8f8;
		overflow: hidden;
		transition-property: height;
		transition-duration: 0.15s;
		/* #ifndef APP-NVUE */
		will-change: height;
		/* #endif */
	}
	
	/* 表情面板样式 */
	.emoji-panel {
		padding: 20rpx;
		height: 100%;
	}
	.emoji-grid {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-wrap: wrap;
		gap: 10rpx;
		justify-content: flex-start;
		align-content: flex-start;
		height: 100%;
		overflow-y: auto;
	}
	.emoji-item {
		width: 60rpx;
		height: 60rpx;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		background-color: white;
		border-radius: 8rpx;
		box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}
	.emoji-item:active {
		transform: scale(0.95);
		background-color: #f0f0f0;
	}
	.emoji-text {
		font-size: 32rpx;
		line-height: 1;
	}
	
	/* 原有功能面板样式（隐藏状态） */
	.upload-functions {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		padding: 30rpx;
		gap: 60rpx;
	}
	.function-item {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	.function-icon {
		width: 80rpx;
		height: 80rpx;
		background-color: white;
		border-radius: 16rpx;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		margin-bottom: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
	.function-text {
		font-size: 24rpx;
		color: #666;
	}
	
	/* 上传文件预览区域样式 */
	.upload-preview-container {
		padding: 20rpx;
		background-color: #f8f8f8;
		border-bottom: 1rpx solid #e5e5e5;
	}
	.upload-preview-scroll {
		white-space: nowrap;
	}
	.upload-preview-list {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		gap: 20rpx;
	}
	.upload-item {
		position: relative;
		/* #ifndef APP-NVUE */
		display: inline-block;
		/* #endif */
	}
	.upload-item-content {
		width: 120rpx;
		height: 120rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: white;
		border: 1rpx solid #e5e5e5;
	}
	.upload-preview-image {
		width: 100%;
		height: 100%;
	}
	.upload-file-preview {
		width: 100%;
		height: 100%;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 10rpx;
	}
	.file-icon {
		font-size: 40rpx;
		margin-bottom: 8rpx;
	}
	.file-name {
		font-size: 20rpx;
		color: #666;
		text-align: center;
		word-break: break-all;
		line-height: 1.2;
	}
	.upload-item-delete {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 32rpx;
		height: 32rpx;
		background-color: #ff4757;
		border-radius: 50%;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
	}
	.delete-icon {
		color: white;
		font-size: 24rpx;
		font-weight: bold;
	}
</style>