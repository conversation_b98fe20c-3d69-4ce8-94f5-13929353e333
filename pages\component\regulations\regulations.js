import http from '@/utils/request'
function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 100000 // 设置超时时间为100秒
    })
}
const baseUrl = `/services/whiskerguardregulatoryservice/api/`

export default { 
    //点击收藏
    addCollect(params) {
        return request(baseUrl + 'enterprise/regulation/collect', params, 'post')
    },
    // 取消收藏
    cancelCollect(params) {
        return request(baseUrl + `enterprise/regulation/collect/cancel/${params.type}/${params.regulationId}`, {}, 'delete')
    },
}