// utils/file-utils.js
export const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];

export function getFileIcon(fileType) {
  if (!fileType) return 'paperclip';

  const type = fileType.toLowerCase();
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(type)) return 'image';
  if (['doc', 'docx', 'pdf', 'txt'].includes(type)) return 'file-text';
  if (['xls', 'xlsx'].includes(type)) return 'file-excel';
  if (['ppt', 'pptx'].includes(type)) return 'file-powerpoint';
  return 'paperclip';
}

/**
 * 获取文件路径/URL，兼容后端接口字段不统一的问题
 * @param {Object} fileItem - 文件对象
 * @returns {string} 文件路径或URL
 */
export function getFileKey(fileItem) {
  if (!fileItem) return '';

  // 按优先级尝试获取文件路径
  return fileItem.filePath ||
         fileItem.fileUrl ||
         fileItem.key ||
         fileItem.url ||
         '';
}

/**
 * 获取文件名，兼容后端接口字段不统一的问题
 * @param {Object} fileItem - 文件对象
 * @returns {string} 文件名
 */
export function getFileName(fileItem) {
  if (!fileItem) return '';

  // 按优先级尝试获取文件名
  return fileItem.fileName ||
         fileItem.name ||
         fileItem.filePath ||
         fileItem.fileUrl ||
         fileItem.key ||
         '';
}

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名或路径
 * @returns {string} 文件扩展名（小写）
 */
export function getFileExtension(fileName) {
  if (!fileName) return '';

  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return '';

  return fileName.substring(lastDotIndex + 1).toLowerCase();
}

/**
 * 检查文件是否支持预览
 * @param {Object|string} fileItem - 文件对象或文件名
 * @returns {boolean} 是否支持预览
 */
export function isPreviewable(fileItem) {
  let fileName = '';

  if (typeof fileItem === 'string') {
    fileName = fileItem;
  } else {
    fileName = getFileName(fileItem);
  }

  const extension = getFileExtension(fileName);
  return previewableTypes.includes(extension);
}
