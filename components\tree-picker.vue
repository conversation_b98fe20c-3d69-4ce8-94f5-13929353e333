<template>
  <view>
    <!-- 输入框显示 -->
    <view class="tree-input" @tap="openPicker">
      <uv-input
        v-model="displayValue"
        :placeholder="placeholder"
        readonly
        suffix-icon="arrow-down"
      ></uv-input>
    </view>

    <!-- 树形选择弹窗 -->
    <uv-popup
      ref="popupRef"
      mode="bottom"
      :round="24"
      :overlay="true"
      :close-on-click-overlay="true"
      :safe-area-inset-bottom="true"
      @maskClick="closePicker"
      @change="handlePopupChange"
    >
      <view class="picker-container">
        <!-- 标题栏 -->
        <view class="picker-header">
          <view class="header-btn" @tap="closePicker">
            <text class="btn-text cancel">取消</text>
          </view>
          <view class="header-title">
            <text class="title-text">{{ title }}</text>
          </view>
          <view class="header-btn" @tap="confirmSelect">
            <text class="btn-text confirm">确定</text>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-container" v-if="showSearch">
          <uv-search
            v-model="searchKeyword"
            placeholder="搜索组织架构"
            :show-action="false"
            @search="handleSearch"
            @change="handleSearchInput"
          ></uv-search>
        </view>

        <!-- 树形结构 -->
        <view class="tree-container">
          <scroll-view scroll-y="true" class="tree-scroll">
            <view v-if="filteredTreeList.length === 0" class="empty-container">
              <uv-empty mode="data" text="暂无数据"></uv-empty>
            </view>
            
            <view v-else class="tree-content">
              <block v-for="(item, index) in filteredTreeList" :key="index">
                <view
                  class="tree-item"
                  :style="{
                    paddingLeft: item.rank * 30 + 20 + 'rpx'
                  }"
                  :class="{
                    show: item.show,
                    selected: isItemSelected(item),
                    selectable: isItemSelectable(item)
                  }"
                  @tap="handleItemClick(item)"
                >
                  <!-- 展开/收起图标 -->
                  <view class="expand-icon" @tap.stop="handleToggleExpand(item)">
                    <view v-if="!item.lastRank" class="expand-arrow" :class="{ expanded: item.showChild }">
                      <text class="arrow-text">▶</text>
                    </view>
                    <view v-else class="expand-placeholder"></view>
                  </view>

                  <!-- 节点图标 -->
                  <view class="node-icon">
                    <view
                      class="type-badge"
                      :style="{ backgroundColor: getNodeIconColor(item.source.type) }"
                    >
                      <text class="type-text">{{ getNodeTypeText(item.source.type) }}</text>
                    </view>
                  </view>

                  <!-- 节点内容 -->
                  <view class="node-content">
                    <text class="node-name">{{ item.name }}</text>
                    <text v-if="item.source.description" class="node-desc">{{ item.source.description }}</text>
                  </view>

                  <!-- 选择框 -->
                  <view class="node-checkbox" v-if="isItemSelectable(item)">
                    <view v-if="multiple">
                      <!-- 多选复选框 -->
                      <uv-checkbox
                        :model-value="isItemSelected(item)"
                        @change="handleCheckboxChange(item)"
                      ></uv-checkbox>
                    </view>
                    <view v-else>
                      <!-- 单选圆形选择器 -->
                      <view class="radio-wrapper">
                        <view
                          class="radio-circle"
                          :class="{ selected: isItemSelected(item) }"
                        >
                          <text v-if="isItemSelected(item)" class="check-mark">✓</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </block>
            </view>
          </scroll-view>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, onMounted } from 'vue'
import { useUserStore } from '@/store/pinia.js'

const props = defineProps({
  // 占位符文本
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 弹窗标题
  title: {
    type: String,
    default: '选择组织架构'
  },
  // 当前选中的值（单选时为字符串/数字，多选时为数组）
  modelValue: {
    type: [String, Number, Array],
    default: () => []
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 默认展开的层级（0表示不展开，-1表示全部展开）
  defaultExpandLevel: {
    type: Number,
    default: 1
  },
  // 值字段名
  valueKey: {
    type: String,
    default: 'id'
  },
  // 标签字段名
  labelKey: {
    type: String,
    default: 'name'
  },
  // 子节点字段名
  childrenKey: {
    type: String,
    default: 'children'
  }
})

// 获取用户store
const userStore = useUserStore()

// 从store中获取组织树数据
const treeData = computed(() => {
  return userStore.getOrgTree() || []
})

const emit = defineEmits(['update:modelValue', 'change'])

const popupRef = ref(null)
const searchKeyword = ref('')

// 扁平化的树形数据
const flatTreeList = ref([])
// 原始扁平化数据（用于搜索过滤）
const originalFlatTreeList = ref([])
// 选中的节点ID集合（临时状态，用于弹窗内的选择）
const tempSelectedIds = ref(new Set())
// 实际选中的节点ID集合
const selectedIds = ref(new Set())
// 选中的节点信息
const selectedNodes = ref([])

// 显示值
const displayValue = computed(() => {
  if (selectedNodes.value.length === 0) return ''
  
  if (props.multiple) {
    return selectedNodes.value.map(node => node[props.labelKey]).join('、')
  } else {
    return selectedNodes.value[0]?.[props.labelKey] || ''
  }
})

// 过滤后的扁平化数据
const filteredTreeList = computed(() => {
  if (!searchKeyword.value.trim()) {
    return originalFlatTreeList.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  const filtered = originalFlatTreeList.value.filter(item => {
    return item.name.toLowerCase().includes(keyword) ||
           (item.source.description && item.source.description.toLowerCase().includes(keyword))
  })
  
  // 搜索时显示所有匹配的节点
  return filtered.map(item => ({
    ...item,
    show: true,
    showChild: true
  }))
})

// 初始化
onMounted(() => {
  initializeComponent()
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  updateSelectedFromModelValue(newVal)
}, { immediate: true, deep: true })

// 监听树形数据变化
watch(treeData, (newData) => {
  if (newData && newData.length > 0) {
    initializeComponent()
  }
}, { deep: true, immediate: true })

// 初始化组件
const initializeComponent = () => {
  // 扁平化树形数据
  flattenTreeData()
  // 更新选中状态
  updateSelectedFromModelValue(props.modelValue)
}

// 扁平化树形数据
const flattenTreeData = () => {
  originalFlatTreeList.value = []
  if (treeData.value && treeData.value.length > 0) {
    renderTreeList(treeData.value)
    // 初始化展开状态
    initializeExpandedState()
  }
}

// 渲染树形列表（扁平化处理）
const renderTreeList = (list = [], rank = 0, parentId = [], parents = []) => {
  list.forEach((item) => {
    const flatItem = {
      id: item[props.valueKey],
      name: item[props.labelKey],
      source: item,
      parentId, // 父级id数组
      parents, // 父级数组
      rank, // 层级
      showChild: false, // 子级是否显示
      open: false, // 是否打开
      show: rank === 0, // 自身是否显示
      hideArr: [],
      lastRank: false // 是否是叶子节点
    }
    
    originalFlatTreeList.value.push(flatItem)
    
    if (Array.isArray(item[props.childrenKey]) && item[props.childrenKey].length > 0) {
      let parentid = [...parentId]
      let parentArr = [...parents]
      parentid.push(item[props.valueKey])
      parentArr.push({
        [props.valueKey]: item[props.valueKey],
        [props.labelKey]: item[props.labelKey]
      })
      
      renderTreeList(item[props.childrenKey], rank + 1, parentid, parentArr)
    } else {
      // 标记为叶子节点
      originalFlatTreeList.value[originalFlatTreeList.value.length - 1].lastRank = true
    }
  })
}

// 初始化展开状态
const initializeExpandedState = () => {
  if (props.defaultExpandLevel === -1) {
    // 展开所有节点
    expandAllNodes()
  } else if (props.defaultExpandLevel > 0) {
    // 展开指定层级
    expandToLevel(props.defaultExpandLevel)
  }
}

// 展开所有节点
const expandAllNodes = () => {
  originalFlatTreeList.value.forEach(item => {
    if (!item.lastRank) {
      item.showChild = true
      item.open = true
      item.show = true
    }
  })
}

// 展开到指定层级
const expandToLevel = (targetLevel) => {
  originalFlatTreeList.value.forEach(item => {
    if (item.rank < targetLevel && !item.lastRank) {
      item.showChild = true
      item.open = true
    }
    if (item.rank <= targetLevel) {
      item.show = true
    }
  })
}

// 根据modelValue更新选中状态
const updateSelectedFromModelValue = (value) => {
  selectedIds.value.clear()
  selectedNodes.value = []

  if (!value) return

  const ids = props.multiple ? (Array.isArray(value) ? value : [value]) : [value]

  ids.forEach(id => {
    if (id !== null && id !== undefined && id !== '') {
      selectedIds.value.add(id)
      const node = findNodeById(id)
      if (node) {
        selectedNodes.value.push(node)
      }
    }
  })
}

// 根据ID查找节点
const findNodeById = (id) => {
  const item = originalFlatTreeList.value.find(item => item.id === id)
  return item ? item.source : null
}

// 判断节点是否可选择
const isItemSelectable = (item) => {
  // return item.source.type === 'DEPARTMENT' || item.source.type === 'TEAM'
  return true
}

// 判断节点是否选中
const isItemSelected = (item) => {
  return tempSelectedIds.value.has(item.id)
}

// 获取节点类型文字
const getNodeTypeText = (type) => {
  const textMap = {
    'COMPANY': '司',
    'SUBSIDIARY': '分',
    'DEPARTMENT': '部',
    'TEAM': '组'
  }
  return textMap[type] || '节'
}

// 获取节点图标颜色
const getNodeIconColor = (type) => {
  const colorMap = {
    'COMPANY': '#FF6B35',
    'SUBSIDIARY': '#4ECDC4',
    'DEPARTMENT': '#45B7D1',
    'TEAM': '#96CEB4'
  }
  return colorMap[type] || '#999'
}

// 处理节点展开/收起
const handleToggleExpand = (item) => {
  if (item.lastRank) return

  item.showChild = !item.showChild
  item.open = !item.open

  // 更新子节点的显示状态
  updateChildrenVisibility(item)
}

// 更新子节点显示状态
const updateChildrenVisibility = (parentItem) => {
  originalFlatTreeList.value.forEach(item => {
    if (item.parentId.includes(parentItem.id)) {
      if (parentItem.showChild) {
        // 如果父节点展开，检查所有父级是否都展开
        const allParentsExpanded = item.parentId.every(pid => {
          const parent = originalFlatTreeList.value.find(p => p.id === pid)
          return parent && parent.showChild
        })
        item.show = allParentsExpanded
      } else {
        // 如果父节点收起，隐藏所有子节点
        item.show = false
        item.showChild = false
        item.open = false
      }
    }
  })
}

// 处理节点点击
const handleItemClick = (item) => {
  if (isItemSelectable(item)) {
    handleNodeSelect(item)
  } else if (!item.lastRank) {
    // 如果不可选择但有子节点，则展开/收起
    handleToggleExpand(item)
  }
}

// 处理节点选择
const handleNodeSelect = (item) => {
  if (!isItemSelectable(item)) return

  if (props.multiple) {
    // 多选模式
    if (tempSelectedIds.value.has(item.id)) {
      tempSelectedIds.value.delete(item.id)
    } else {
      tempSelectedIds.value.add(item.id)
    }
  } else {
    // 单选模式
    tempSelectedIds.value.clear()
    tempSelectedIds.value.add(item.id)
  }
}

// 处理复选框变化
const handleCheckboxChange = (item) => {
  handleNodeSelect(item)
}

// 打开选择器
const openPicker = () => {
  if (props.disabled) return

  // 打开时，将当前选中的状态复制到临时状态
  tempSelectedIds.value = new Set(selectedIds.value)

  if (popupRef.value) {
    popupRef.value.open()
  }
}

// 关闭选择器
const closePicker = () => {
  if (popupRef.value) {
    popupRef.value.close()
  }
}

// 处理弹窗状态变化
const handlePopupChange = (e) => {
  if (!e.show) {
    // 弹窗关闭时重置搜索
    searchKeyword.value = ''
  }
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑通过 computed 属性 filteredTreeList 自动处理
}

// 处理搜索输入
const handleSearchInput = () => {
  // 实时搜索通过 computed 属性 filteredTreeList 自动处理
}

// 确认选择
const confirmSelect = () => {
  // 更新实际选中状态
  selectedIds.value = new Set(tempSelectedIds.value)
  selectedNodes.value = []

  // 获取选中的节点信息
  tempSelectedIds.value.forEach(id => {
    const node = findNodeById(id)
    if (node) {
      selectedNodes.value.push(node)
    }
  })

  // 发出事件
  const value = props.multiple
    ? Array.from(selectedIds.value)
    : (selectedIds.value.size > 0 ? Array.from(selectedIds.value)[0] : '')

  emit('update:modelValue', value)
  emit('change', {
    value,
    nodes: selectedNodes.value
  })

  closePicker()
}
</script>

<style lang="scss" scoped>
.tree-input {
  width: 100%;
}

.picker-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .header-btn {
    min-width: 80rpx;

    .btn-text {
      font-size: 32rpx;

      &.cancel {
        color: #999;
      }

      &.confirm {
        color: #007AFF;
        font-weight: 500;
      }
    }
  }

  .header-title {
    flex: 1;
    text-align: center;

    .title-text {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }
}

.search-container {
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tree-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .tree-scroll {
    flex: 1;
    height: 600rpx;
  }
}

.empty-container {
  padding: 80rpx 40rpx;
}

.tree-content {
  padding: 20rpx 0;
}

.tree-item {
  display: none;
  align-items: center;
  min-height: 88rpx;
  padding: 16rpx 40rpx 16rpx 20rpx;
  transition: background-color 0.3s;

  &.show {
    display: flex;
  }

  &.selectable {
    &:active {
      background-color: #f8f9fa;
    }
  }

  &.selected {
    background-color: #f0f8ff;
  }

  .expand-icon {
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;

    .expand-arrow {
      transition: transform 0.3s;

      .arrow-text {
        font-size: 24rpx;
        color: #999;
      }

      &.expanded {
        transform: rotate(90deg);
      }
    }

    .expand-placeholder {
      width: 16rpx;
      height: 16rpx;
    }
  }

  .node-icon {
    margin-right: 16rpx;

    .type-badge {
      width: 36rpx;
      height: 36rpx;
      border-radius: 6rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .type-text {
        font-size: 20rpx;
        color: #fff;
        font-weight: 500;
      }
    }
  }

  .node-content {
    flex: 1;

    .node-name {
      display: block;
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      line-height: 1.4;
    }

    .node-desc {
      display: block;
      font-size: 24rpx;
      color: #999;
      margin-top: 4rpx;
      line-height: 1.3;
    }
  }

  .node-checkbox {
    margin-left: 16rpx;

    .radio-wrapper {
      .radio-circle {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid #ddd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;

        &.selected {
          border-color: #007AFF;
          background-color: #007AFF;
        }

        .check-mark {
          font-size: 24rpx;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
