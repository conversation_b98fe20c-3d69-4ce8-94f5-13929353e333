{"_from": "follow-redirects@^1.15.6", "_id": "follow-redirects@1.15.9", "_inBundle": false, "_integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "_location": "/follow-redirects", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "follow-redirects@^1.15.6", "name": "follow-redirects", "escapedName": "follow-redirects", "rawSpec": "^1.15.6", "saveSpec": null, "fetchSpec": "^1.15.6"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz", "_shasum": "a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1", "_spec": "follow-redirects@^1.15.6", "_where": "G:\\项目集合\\家客云_h5\\node_modules\\axios", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://ruben.verborgh.org/"}, "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.syskall.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP and HTTPS modules that follow redirects.", "devDependencies": {"concat-stream": "^2.0.0", "eslint": "^5.16.0", "express": "^4.16.4", "lolex": "^3.1.0", "mocha": "^6.0.2", "nyc": "^14.1.1"}, "engines": {"node": ">=4.0"}, "files": ["*.js"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "license": "MIT", "main": "index.js", "name": "follow-redirects", "peerDependenciesMeta": {"debug": {"optional": true}}, "repository": {"type": "git", "url": "git+ssh://**************/follow-redirects/follow-redirects.git"}, "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "version": "1.15.9"}