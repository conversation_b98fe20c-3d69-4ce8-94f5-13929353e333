<template>
	<view class="container">
		<!-- 顶部装饰区 -->
		<view class="header">
			<view class="logo-container">
				<image class="logo"
					src="https://ai-public.mastergo.com/ai/img_res/cdd8b5e0255e571b64bdbce90d1dc37c.jpg" />
			</view>
			<text class="welcome-text">欢迎使用猫伯伯智能合规管家</text>
			<text class="subtitle">企业合规管理一站式解决方案</text>
		</view>

		<!-- 输入字段区 -->
		<view class="input-card">
			<!-- 企业代码输入 -->
			<view class="input-field">
				<text class="input-label">企业代码</text>
				<view class="input-container">
					<uni-icons type="locked" size="20" color="#999999" />
					<input class="input" placeholder="请输入企业代码" placeholder-style="color: #CCCCCC; font-size: 14px"
						v-model="companyCode" />
				</view>
			</view>

			<!-- 手机号输入 -->
			<view class="input-field">
				<text class="input-label">手机号码</text>
				<view class="input-container">
					<uni-icons type="phone" size="20" color="#999999" />
					<input class="input" placeholder="请输入手机号" placeholder-style="color: #CCCCCC; font-size: 14px"
						v-model="phoneNumber" />
				</view>
			</view>

			<!-- 密码/验证码输入 -->
			<view class="input-field">
				<text class="input-label">{{ isPasswordLogin ? '密码' : '验证码' }}</text>
				<view class="input-container">
					<uni-icons :type="isPasswordLogin ? 'locked' : 'email'" size="20" color="#999999" />
					<input class="input" :placeholder="isPasswordLogin ? '请输入密码' : '请输入验证码'"
						placeholder-style="color: #CCCCCC; font-size: 14px" :password="isPasswordLogin && !showPassword"
						v-model="password" />
					<view class="input-action" @click="togglePasswordVisibility" v-if="isPasswordLogin">
						<uni-icons :type="showPassword ? 'eye' : 'eye-slash'" size="20" color="#999999" />
					</view>
					<view class="get-code" @click="getVerificationCode" v-else>
						<text>{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}</text>
					</view>
				</view>
			</view>

			<view class="login-type-switch" @click="toggleLoginType">
				<text>{{ isPasswordLogin ? '验证码登录' : '密码登录' }}</text>
			</view>
		</view>

		<!-- 操作按钮区 -->
		<view class="action-area">
			<button  class="login-btn" :class="{ 'active': isFormValid }" :disabled="!isFormValid" @click="handleLogin">
				<text v-if="!isLoading">登录</text>
				<uni-icons v-else type="spinner-cycle" size="20" color="#FFFFFF" />
			</button>

			<view class="forget-password" @click="handleForgetPassword">
				<text>忘记密码？</text>
			</view>
		</view>

		<!-- 第三方登录区 -->
		<view class="third-party-login">
			<view class="divider">
				<view class="divider-line"></view>
				<text class="divider-text">其他登录方式</text>
				<view class="divider-line"></view>
			</view>

			<view class="third-party-buttons">
				<button  class="third-party-btn wechat" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">
					<uni-icons type="weixin" size="24" color="#FFFFFF" />
				</button>
				<button  class="third-party-btn dingtalk" @click="handleDingTalkLogin">
					<uni-icons type="dingding" size="24" color="#FFFFFF" />
				</button>
			</view>
		</view>

		<!-- 底部协议区 -->
		<view class="agreement">
			<text>登录即代表同意</text>
			<text class="agreement-link" @click="handleTermsClick">《用户协议》</text>
			<text>和</text>
			<text class="agreement-link" @click="handlePrivacyClick">《隐私政策》</text>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, computed, onUnmounted, getCurrentInstance } from 'vue';




	// 表单数据
	const companyCode = ref('');
	const phoneNumber = ref('');
	const password = ref('');
	const showPassword = ref(false);
	const isPasswordLogin = ref(true);
	const isLoading = ref(false);
	const countdown = ref(0);
	let timer : number | null = null;


	onUnmounted(() => {
		// console.log(proxy, )

		if (timer) {
			clearInterval(timer);
			timer = null;
		}
	});

	// 表单验证状态
	const isFormValid = computed(() => {
		return companyCode.value && phoneNumber.value && password.value;
	});

	// 切换密码可见性
	const togglePasswordVisibility = () => {
		showPassword.value = !showPassword.value;
	};

	// 获取验证码
	const getVerificationCode = () => {
		if (countdown.value > 0) return;

		if (!phoneNumber.value) {
			uni.showToast({
				title: '请输入手机号',
				icon: 'none'
			});
			return;
		}

		countdown.value = 60;
		timer = setInterval(() => {
			countdown.value--;
			if (countdown.value <= 0 && timer) {
				clearInterval(timer);
				timer = null;
			}
		}, 1000);

		uni.showToast({
			title: '验证码已发送',
			icon: 'none'
		});
	};

	// 切换登录方式
	const toggleLoginType = () => {
		isPasswordLogin.value = !isPasswordLogin.value;
		password.value = '';
		showPassword.value = false;
	};

	// 处理登录
	const handleLogin = () => {
		isLoading.value = true;
		// 模拟登录请求
		setTimeout(() => {
			isLoading.value = false;
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			});
		}, 1500);
	};

	// 忘记密码处理
	const handleForgetPassword = () => {
		uni.navigateTo({
			url: '/pages/forget-password/index'
		});
	};

	// 微信登录
	const onGetPhoneNumber = (e : any) => {
		console.log('微信登录', e);
	};

	// 钉钉登录
	const handleDingTalkLogin = () => {
		uni.showToast({
			title: '钉钉登录',
			icon: 'none'
		});
	};

	// 用户协议点击
	const handleTermsClick = () => {
		uni.navigateTo({
			url: '/pages/terms/index'
		});
	};

	// 隐私政策点击
	const handlePrivacyClick = () => {
		uni.navigateTo({
			url: '/pages/privacy/index'
		});
	};
</script>

<style>
	page {
		height: 100%;
		background-color: #FFFFFF;
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		padding: 0 40rpx;
	}

	/* 顶部装饰区样式 */
	.header {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 80rpx;
		padding-bottom: 60rpx;
	}

	.logo-container {
		width: 160rpx;
		height: 160rpx;
		border-radius: 32rpx;
		background-color: #F5F7FA;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 32rpx;
	}

	.logo {
		width: 120rpx;
		height: 120rpx;
	}

	.welcome-text {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #999999;
	}

	/* 输入字段区样式 */
	.input-card {
		margin-top: 40rpx;
	}

	.input-field {
		margin-bottom: 40rpx;
	}

	.input-label {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 16rpx;
		display: block;
	}

	.input-container {
		height: 96rpx;
		border: 2rpx solid #E5E5E5;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
	}

	.input-container:focus-within {
		border-color: #1A73E8;
	}

	.input {
		flex: 1;
		height: 100%;
		font-size: 32rpx;
		color: #333333;
		margin-left: 20rpx;
	}

	.input-action {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 20rpx;
	}

	.get-code {
		padding: 8rpx 16rpx;
		background-color: #F5F7FA;
		border-radius: 8rpx;
		margin-left: 20rpx;
	}

	.get-code text {
		font-size: 24rpx;
		color: #1A73E8;
	}

	.login-type-switch {
		display: flex;
		justify-content: flex-end;
		margin-top: -20rpx;
		margin-bottom: 40rpx;
	}

	.login-type-switch text {
		font-size: 28rpx;
		color: #1A73E8;
	}

	/* 操作按钮区样式 */
	.action-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 20rpx;
	}

	.login-btn {
		width: 100%;
		height: 96rpx;
		background: linear-gradient(220deg, #1A73E8 0%, #3AAFFF 100%);
		border-radius: 48rpx;
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: bold;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 32rpx;
	}

	.login-btn[disabled] {
		background-color: #CCCCCC;
	}

	.forget-password {
		font-size: 28rpx;
		color: #999999;
	}

	/* 第三方登录区样式 */
	.third-party-login {
		margin-top: 80rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.divider {
		display: flex;
		align-items: center;
		width: 100%;
		margin-bottom: 40rpx;
	}

	.divider-line {
		flex: 1;
		height: 2rpx;
		background-color: #E5E5E5;
	}

	.divider-text {
		font-size: 28rpx;
		color: #999999;
		margin: 0 24rpx;
	}

	.third-party-buttons {
		display: flex;
		justify-content: center;
		width: 100%;
		gap: 80rpx;
	}

	.third-party-btn {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0;
		margin: 0;
		line-height: 1;
	}

	.third-party-btn::after {
		border: none;
	}

	.third-party-btn.wechat {
		background-color: #07C160;
	}

	.third-party-btn.dingtalk {
		background-color: #0088FF;
	}

	/* 底部协议区样式 */
	.agreement {
		position: absolute;
		bottom: 60rpx;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		font-size: 24rpx;
		color: #999999;
	}

	.agreement-link {
		color: #1A73E8;
		margin: 0 8rpx;
	}
</style>