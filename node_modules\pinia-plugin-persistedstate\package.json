{"_from": "pinia-plugin-persistedstate", "_id": "pinia-plugin-persistedstate@3.2.1", "_inBundle": false, "_integrity": "sha512-MK++8LRUsGF7r45PjBFES82ISnPzyO6IZx3CH5vyPseFLZCk1g2kgx6l/nW8pEBKxxd4do0P6bJw+mUSZIEZUQ==", "_location": "/pinia-plugin-persistedstate", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "pinia-plugin-persistedstate", "name": "pinia-plugin-persistedstate", "escapedName": "pinia-plugin-persistedstate", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/pinia-plugin-persistedstate/-/pinia-plugin-persistedstate-3.2.1.tgz", "_shasum": "66780602aecd6c7b152dd7e3ddc249a1f7a13fe5", "_spec": "pinia-plugin-persistedstate", "_where": "C:\\Users\\<USER>\\Desktop\\小程序配置文件\\ccpvue3-card-new", "author": {"name": "prazdevs"}, "bugs": {"url": "https://github.com/prazdevs/pinia-plugin-persistedstate/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Configurable persistence and rehydration of Pinia stores.", "devDependencies": {"pinia": "^2.1.6"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist"], "homepage": "https://prazdevs.github.io/pinia-plugin-persistedstate/", "keywords": ["pinia", "pinia-plugin"], "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "name": "pinia-plugin-persistedstate", "peerDependencies": {"pinia": "^2.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/prazdevs/pinia-plugin-persistedstate.git"}, "scripts": {"build": "tsup --dts --format esm,cjs src/index.ts", "release": "bumpp -t \"v%s\" -c \":bookmark: release v\"", "test": "vitest", "test:coverage": "vitest run --coverage", "test:run": "vitest --run", "test:ui": "vitest --ui"}, "types": "dist/index.d.ts", "version": "3.2.1"}