import {
	config as configuration
} from "@/config";
let timeout = 100000;
// import {
// 	storeToRefs
// } from 'pinia'

import {
	useUserStore
} from '@/store/pinia.js';

const request = async (config) => {
	const userStore = useUserStore();
	var encryption = false //是否加密
	config.header = config.header || {};
	// config.header["Accept"] = `config.header[\'Accept\'] = \'application/json, text/plain, */*\'`;
	// config.header["Content-type"] = `application/json`;

	// config.header["User-Agent"] = `Apifox/1.0.0 (https://apifox.com)`;
	config.header["Content-Type"] = `application/json`;
	config.header["Accept"] = `*/*`;
	config.header["Host"] = `dev.api.mbbhg.com`;
	config.header["Connection"] = `keep-alive`;
	config.header["X-Source"] = `MINI`;
	config.header["X-Version"] = `1.0`;
	if (!config.isToken) {
		config.header["Authorization"] =
			`Bearer ${userStore.token ? userStore.token : null}`;
	}
	// console.log(userStore.token, 'token')
	// config.header["token"] = userStore.token ? userStore.token : null
	var url = 'https://dev.api.mbbhg.com' + config.url
	// var url = 'https://api.mbbhg.com' + config.url
	// var url = configuration.baseUrl + config.url
	return new Promise(async (resolve, reject) => {
		// console.log('encryption-是否加密', encryption, config.encryption, url)
		if (encryption) {

		} else {
			// console.log('url', url)
			uni.request({
				url: url, //仅为示例，并非真实接口地址。
				method: config.method || "GET",
				data: config.data || config.params || {},
				timeout: config.timeout || timeout,
				header: config.header,
				dataType: "json",
				success: (res) => {

					console.log(res, 'res')
					
					if (res.statusCode == 200 || res.statusCode == 201) {
						const d = res.data;
						resolve(d);
						// if (d.code == 0) {
						// 	switch (checkDataType(d.data)) {
						// 		case 'Array':
						// 			resolve(d);
						// 			break;
						// 		case 'Object':
						// 			resolve(d.data);
						// 			break;
						// 		default:
						// 			resolve(d.data);
						// 			break;
						// 	}
						// } else {
						// 	if (d.code == 401) {
						// 		userStore.token = null
						// 		uni.showModal({
						// 			title: "温馨提示",
						// 			content: "登录状态已过期，请前往重新登录",
						// 			showCancel: false,
						// 			success: (res) => {
						// 				if (res.confirm) {
						// 					uni.reLaunch({
						// 						url: `/pages/index/index`,
						// 					});
						// 				}
						// 			},
						// 		});
						// 		return false;
						// 	}
						// 	if (config.url == '/setting/getInfo') {
						// 		reject(res);
						// 	} else {
						// 		var m = d.msg || "请求异常,请联系管理员";
						// 		// uni.$uv.toast(d.msg);
						// 		reject(res);
						// 	}
						// }
					}else if(res.statusCode == 204){
                        const d = res;
						resolve(d);
					} else if (res.statusCode == 500) {
						uni.showModal({
							title: "温馨提示",
							content: res.data.title || "请求异常,请联系管理员",
							showCancel: false,
						});
						reject(res.data);
					} else {
						reject(res);
						switch (res.statusCode) {
							case 401:
								uni.showModal({
									title: "温馨提示",
									content: "登录状态已过期，请返回首页重新登录",
									showCancel: false,
									success: (res) => {
										if (res.confirm) {
											uni.reLaunch({
												url: `/pages/login/login`,
											});
										}
									},
								});
								break;
							case 403:
								uni.showModal({
									title: "温馨提示",
									content: "登录状态已过期，请返回首页重新登录",
									showCancel: false,
									success: (res) => {
										if (res.confirm) {
											uni.reLaunch({
												url: `/pages/login/login`,
											});
										}
									},
								});
								break;
							default:
								uni.showModal({
									title: "温馨提示",
									content: res.data.title,
									showCancel: false,
								});
								break;
						}
					}
				},
				fail: (fail) => {
					console.log(fail, 'pppppppppppppp');
					// uni.showModal({
					// 	title: "温馨提示",
					// 	content: res.data.title || "请求异常,请联系管理员",
					// 	showCancel: false,
					// });
					reject(fail);
				},
			});
		}
	});
};
export default request;