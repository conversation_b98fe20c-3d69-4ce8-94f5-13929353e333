# liu-swiper-goods适用于uni-app项目的滑动商品组、秒杀专区组件
### 本组件目前兼容微信小程序、H5
### 本组件是滑动商品组、秒杀专区，swiper自动轮播、手动轮播、自动轮播
# --- 扫码预览、关注我们 ---

## 扫码关注公众号，查看更多插件信息，预览插件效果！ 

![](https://uni.ckapi.pro/uniapp/publicize.png)

### 使用示例
``` 
<template>
	<view>
		<view class="swiper-card">
			<view class="swiper-card-title">
				<view class="card-name">正在秒杀</view>
				<view class="go-more">查看更多</view>
			</view>
			<liu-swiper-goods :dataList="list" @clickItem="clickItem"></liu-swiper-goods>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					id: '1',
					price: '29.9',
					desc: '这个是商品1的一段描述文字，内容自定义',
					img: 'https://cdn.pixabay.com/photo/2020/05/19/13/35/cartoon-5190860_1280.jpg',
				}, {
					id: '2',
					price: '29.9',
					desc: '这个是商品2',
					img: 'https://cdn.pixabay.com/photo/2021/07/22/11/25/rabbit-6485072_1280.jpg',
				}, {
					id: '3',
					price: '29.9',
					desc: '这个是商品3的一段描述',
					img: 'https://cdn.pixabay.com/photo/2022/03/31/14/53/camp-7103189_1280.png',
				}, {
					id: '4',
					price: '29.9',
					desc: '这个是商品4',
					img: 'https://cdn.pixabay.com/photo/2022/11/29/19/05/boho-7625140_1280.jpg',
				}, {
					id: '5',
					price: '29.9',
					desc: '这个是商品5的一段描述',
					img: 'https://cdn.pixabay.com/photo/2022/08/25/23/06/woman-7411414_1280.png',
				}]
			}
		},
		methods: {
			//点击商品
			clickItem(e) {
				console.log('所点击商品信息:', e)
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f0f0f0;

		.swiper-card {
			width: 96%;
			background: linear-gradient(180deg, #FF8329 0%, #F4AF1E 100%);
			box-sizing: border-box;
			margin: 0 auto;
			margin-top: 30rpx;
			padding: 20rpx 0 20rpx 20rpx;
			border-radius: 12rpx;

			.swiper-card-title {
				margin-bottom: 16rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.card-name {
					font-size: 32rpx;
					font-weight: bold;
					line-height: 32rpx;
					color: #FFFFFF;
				}

				.go-more {
					font-size: 28rpx;
					line-height: 28rpx;
					color: #FFFFFF;
					margin-right: 16rpx;
				}
			}
		}
	}
</style>
```

### 属性说明
| 名称                         | 类型            | 默认值                 | 描述             |
| ----------------------------|--------------- | ---------------------- | ---------------|
| dataList                    | Array          | []                     | 数据源
| autoplay                    | Boolean        | true                   | 是否自动切换
| interval                    | Number         | 3000                   | 自动切换时间间隔
| @clickItem                  | Function       |                        | 点击商品回调事件

