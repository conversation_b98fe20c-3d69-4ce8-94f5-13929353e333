<template>
  <view class="report-container">
    <uv-form labelPosition="top" labelWidth="200" :model="model" :rules="rules" ref="form">
      <!-- 表单内容区 -->
      <view class="form-container">
        <!-- 举报类型 -->
        <uv-form-item prop="violationType" label="1. 举报类型" :labelStyle="labelStyle" required>
          <picker-input v-model="model.violationType" :columns="[reportTypes]" displayKey="name" valueKey="value"
                        placeholder="请选择举报类型" />
        </uv-form-item>
        <!-- 举报编号 -->
        <!-- <uv-form-item prop="violationCode" label="1. 举报编号" :labelStyle="labelStyle" required>
          <picker-input v-model="model.violationCode" :columns="[reportTypes]" displayKey="name" valueKey="value"
                        placeholder="请选择举报类型" />
        </uv-form-item> -->
        <!-- 举报标题 -->
        <uv-form-item prop="title" label="2. 举报标题" :labelStyle="labelStyle" required>
          <uv-input v-model="model.title" placeholder="请输入简要标题，最多50字" maxlength="50" />
        </uv-form-item>

        <!-- 举报详情 -->
        <uv-form-item prop="detail" label="3. 举报详情" :labelStyle="labelStyle" required>
          <uv-textarea autoHeight v-model="model.detail" placeholder="请输入举报详情，最多1000字" maxlength="1000" />
        </uv-form-item>

        <!-- 附件上传 -->
        <uv-form-item label="4. 附件上传" :labelStyle="labelStyle">
          <upload-files ref="upload" v-model="model.attachmentList" title="附件上传" :tenant-id="'9007199254740991'"
            :service-name="'violation'" :category-name="'category'"
            @upload-success="handleUploadSuccess" />
        </uv-form-item>

        <!-- 匿名模式 -->
        <uv-form-item label="5. 匿名模式" :labelStyle="labelStyle">
          <view class="anonymous-switch">
            <text>匿名举报</text>
            <switch :checked="model.isAnonymous" @change="handleAnonymousChange" color="#1A73E8" />
          </view>
        </uv-form-item>

        <!-- 联系方式 -->
        <uv-form-item label="6. 联系方式（选填）" :labelStyle="labelStyle">
          <uv-input v-model="model.contactWay" placeholder="选填：手机号或邮箱，用于接收反馈" />
        </uv-form-item>
      </view>
    </uv-form>

    <!-- 底部操作区 -->
    <view class="footer">
      <uv-button @click="handleSubmit" type="primary">提交</uv-button>
    </view>
  </view>
</template>

<script setup>
import UploadFiles from '@/components/uploadFile.vue'
import pickerInput from '../../../components/picker-input.vue';
import { useUserStore } from '@/store/pinia.js';
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import https from '@/api/violation/report.js'
import codeApi from '@/api/common/index.js'
import getDictData from '@/utils/dict.js';

const userStore = useUserStore();
const reportTypes = ref([])

const form = ref(null);

// 获取字典数据
onMounted(async () => {
  try {
    const type = await codeApi.getCode('VIOLATION');
    model.value.violationCode = type;
    const res = await getDictData('45');
    if (res && res.length > 0) {
      reportTypes.value = res.map(item => ({
        name: item.name,
        value: item.value
      }));
    }
  } catch (error) {
    console.error('获取字典数据失败:', error);
  }
});

const labelStyle = ref({
  bottom: '30rpx',
  width: '200rpx'
});

const model = ref({
  // tenantId: userStore.tenantId,
  violationType: 'FINANCIAL_VIOLATION',
  title: '',
  detail: '',
  attachmentList: [],
  isAnonymous: false,
  contactWay: '',
  violationCode: ''
});
const upload = ref(null);
const rules = ref({
  violationType: [
    { required: true, message: '请选择举报类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入举报标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在2到50个字符之间', trigger: 'blur' }
  ],
  detail: [
    { required: true, message: '请输入举报详情', trigger: 'blur' },
    { min: 10, max: 1000, message: '详情长度在10到1000个字符之间', trigger: 'blur' }
  ]
});

//  附件上传成功
function handleUploadSuccess(res) {
  console.log(res, '00000000000');
  // model.value.attachmentList = res;
}

const handleAnonymousChange = (e) => {
  model.value.isAnonymous = e.detail.value;
};

const handleSubmit = () => {
  console.log(model.value.attachmentList, 'ppppppppppp')
  const attachmentList = model.value.attachmentList.map(item => {
    return {
      fileName: item?.fileName,
      fileType: item.fileType,
      filePath: item.key,
      fileSize: item.size + 'kb',
    }
  })
  form.value.validate().then(() => {
    uni.showLoading({
      title: '提交中...'
    });
    https.reportCreate({...model.value, attachmentList})
.then(res => {
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      });
      uni.hideLoading();
      setTimeout(() => {
        // 通知上一级页面刷新列表
        uni.$emit('refreshList');
        uni.navigateBack();
      }, 1000);
    }).catch(err => {
      uni.hideLoading();
    });
  })
};
</script>

<style lang="scss" scoped>
.report-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: #fff;

  .form-container {
    padding: 32rpx;
  }

  .picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 24rpx;
    border: 1px solid #e0e0e0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1a1a1a;
  }

  .word-count {
    display: block;
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
  }

  .anonymous-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    font-size: 28rpx;
    color: #1a1a1a;
  }

  .footer {
    padding: 0 32rpx;
    margin-bottom: 20px;
  }
}
</style>