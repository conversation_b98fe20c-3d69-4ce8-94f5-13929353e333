<template>
	<view class="lists-container">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<header-bar title="风险识别清单" :fixed="false" v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click="handleReview">
								<uni-icons type="search" size="24" color="#666666"></uni-icons>
							</view>
							<view class="nav-btn" @click="$tools.routeJump(`/pages/component/lists/riskForm`)">
								<button  class="btn-text">＋新增3</button>
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags :fixed="false" :top="0" :tags="tags" :active-index="activeIndex"
					@change="handleTagChange" />
			</template>
			<view class="risk-list pl-32 pr-32">
				<view class="list-item" v-for="(item, index) in dataList" :key="index"
					@click="$tools.routeJump(`/pages/component/lists/riskDetail?id=${item.id}`)">
					<view class="item-content">
						<text class="item-title">{{ item.title }}</text>
						<view class="item-meta">
							<view :class="['badge', `badge-${item.level}`]">{{ item.levelText }}</view>
							<text class="meta-divider">·</text>
							<text class="meta-text">{{ item.department }}</text>
							<text class="meta-divider">·</text>
							<text class="meta-date">{{ item.date }}</text>
						</view>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
			</view>
			<!-- </scroll-view> -->
		</z-paging>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		useUserStore
	} from '@/store/pinia.js';
	import headerBar from '@/components/headerBar.vue';
	import filterTags from '@/components/filterTags.vue'
	import homeApi from '@/api/home/<USER>'
	const activeIndex = ref(0);
	const tags = ref([{
		name: '全部',
		value: null
	}, {
		name: '待审',
		value: 'HIGH'
	}, {
		name: '已通过',
		value: 'MEDIUM'
	}, {
		name: '待更新',
		value: 'LOW'
	}]);
	const handleTagChange = (index) => {
		activeIndex.value = index
		paging.value.reload();
	}
	// const riskList = ref([{
	// 	title: '数据中心电力系统故障',
	// 	level: 'high',
	// 	levelText: '高危',
	// 	department: 'IT 基础设施部',
	// 	date: '2023-05-15'
	// }, ]);
	const searchValue = ref("") //
	const paging = ref(null)
	const dataList = ref([])
	// 列表查询
	const queryList = (pageNo, pageSize) => {
		const userStore = useUserStore();
		var params = {
			page: pageNo - 1,
			size: pageSize,
			// tenantId: userStore.tenantId,
		}
		// if (activeIndex.value > 0) {
		// 	params.reviewType = tabs_.value[activeIndex.value].value
		// }
		homeApi.complianceRiskMains(params).then((res) => {
			let arr = res.content
			console.log('arr', arr)
			paging.value.complete(res.content);
		}).catch((err) => {
			paging.value.complete(false);
		})
	}
	const handleItemClick = (item) => {
		console.log('点击风险项:', item);
		// 这里可以添加跳转逻辑
	};
</script>

<style lang="scss" scoped>
	.lists-container {
		// display: flex;
		// flex-direction: column;
		// height: 100%;
		// background-color: #F5F7FA;

		/* Header Styles */
		.header {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			height: 100rpx;
			background-color: #FFFFFF;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx;
			z-index: 10;
		}

		.header-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #666666;
		}

		.nav-right {
			display: flex;
			align-items: center;

			.nav-btn {
				display: flex;
				align-items: center;
				margin-left: 30rpx;
			}

			.btn-text {
				font-size: 26rpx;
				color: #1A73E8;
				margin-left: 8rpx;
			}
		}

		/* Tabs Styles */
		.tabs-container {
			position: fixed;
			top: 100rpx;
			left: 40rpx;
			right: 0;
			height: 100rpx;
			background-color: #FFFFFF;
			z-index: 10;
			white-space: nowrap;
			//     height: 100%;
			//    white-space: nowrap;
		}

		.filter-tags {
			height: 100%;
			display: flex;
			align-items: center;
			gap: 20rpx;
		}

		.tag {
			font-size: 28rpx;
			padding: 16rpx 24rpx;
			border-radius: 40rpx;
			background-color: #EFEFF4;
			color: #666;
		}

		.tag.active {
			background-color: #1A73E8;
			color: #fff;
		}

		/* Content Styles */
		.content {
			position: absolute;
			top: 200rpx;
			bottom: 100rpx;
			left: 0;
			right: 0;
			padding: 16rpx 32rpx;
		}

		.risk-list {
			display: flex;
			flex-direction: column;
			gap: 8rpx;
		}

		.list-item {
			height: 144rpx;
			background-color: white;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.item-title {
			font-size: 32rpx;
			font-weight: normal;
			color: #666666;
			margin-bottom: 8rpx;
		}

		.item-meta {
			display: flex;
			align-items: center;
			font-size: 24rpx;
		}

		.badge {
			display: inline-flex;
			align-items: center;
			padding: 8rpx 16rpx;
			border-radius: 16rpx;
			font-size: 24rpx;
			font-weight: 500;
		}

		.badge-high {
			background-color: #FFEAEA;
			color: #E53E3E;
		}

		.badge-medium {
			background-color: #FFF4E5;
			color: #DD6B20;
		}

		.badge-low {
			background-color: #E6FFFA;
			color: #319795;
		}

		.meta-divider {
			color: #999999;
			margin: 0 8rpx;
		}

		.meta-text {
			color: #999999;
		}

		.meta-date {
			color: #CCCCCC;
		}
	}
</style>