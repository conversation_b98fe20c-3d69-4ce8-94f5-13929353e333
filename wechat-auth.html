<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>微信服务号授权</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-container {
            background: white;
            border-radius: 16px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1A73E8, #4285F4);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1A73E8, #4285F4);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .auth-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .auth-info h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .auth-info h3::before {
            content: '🔔';
            margin-right: 8px;
        }
        
        .auth-info ul {
            list-style: none;
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .auth-info li {
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }
        
        .auth-info li::before {
            content: '•';
            color: #1A73E8;
            position: absolute;
            left: 0;
        }
        
        .auth-btn {
            background: linear-gradient(135deg, #1A73E8, #4285F4);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(26, 115, 232, 0.3);
        }
        
        .auth-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .loading.show {
            display: flex;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1A73E8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-message {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-message.show {
            display: block;
        }
        
        .footer {
            font-size: 12px;
            color: #999;
            margin-top: 20px;
        }
        
        @media (max-width: 480px) {
            .auth-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 20px;
            }
            
            .subtitle {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">🔔</div>
        <h1 class="title">微信服务号授权</h1>
        <p class="subtitle">授权后您将收到重要的业务通知和提醒</p>
        
        <div class="auth-info">
            <h3>授权后您将获得：</h3>
            <ul>
                <li>任务提醒和截止日期通知</li>
                <li>重要业务流程状态更新</li>
                <li>系统公告和政策变更提醒</li>
                <li>合规培训和考试通知</li>
            </ul>
        </div>
        
        <div class="loading">
            <div class="spinner"></div>
            <span id="loadingText">处理中...</span>
        </div>
        
        <div class="status-message" id="statusMessage"></div>
        <div class="auth-btn" id="authBtn"></div>
        <!-- class="auth-btn" id="authBtn" -->
        <button  onclick="handleAuth()">
            立即授权
        </button>
        
        <div class="footer">
            <p>我们承诺保护您的隐私，不会发送垃圾信息</p>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getQueryParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // 显示状态消息
        function showMessage(message, type = 'info') {
            const messageEl = document.getElementById('statusMessage');
            messageEl.textContent = message;
            messageEl.className = `status-message ${type} show`;
        }
        
        // 显示加载状态
        function showLoading(text = '处理中...') {
            document.getElementById('loadingText').textContent = text;
            document.querySelector('.loading').classList.add('show');
            document.getElementById('authBtn').disabled = true;
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.querySelector('.loading').classList.remove('show');
            document.getElementById('authBtn').disabled = false;
        }
        
        // 处理授权
        async function handleAuth() {
            try {
                showLoading('正在跳转到微信授权页面...');
                
                // 构建微信授权URL
                const authUrl = buildWechatAuthUrl();
                
                // 跳转到微信授权页面
                window.location.href = authUrl;
                
            } catch (error) {
                console.error('授权失败:', error);
                hideLoading();
                showMessage('授权失败，请重试', 'error');
            }
        }
        
        // 构建微信授权URL
        function buildWechatAuthUrl() {
            const appId = 'wx101caef81fb1b3b9'; // 您的服务号AppID
            const redirectUri = encodeURIComponent('https://dev.api.mbbhg.com/wechat-auth-callback.html');
            const scope = 'snsapi_userinfo';
            const state = generateState();
            
            return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}&connect_redirect=1#wechat_redirect`;
        }
        
        // 生成状态参数
        function generateState() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2);
            return `${timestamp}_${random}`;
        }
        
        // 处理授权回调
        async function handleAuthCallback() {
            const code = getQueryParam('code');
            const state = getQueryParam('state');
            
            if (!code) {
                showMessage('授权失败：未获取到授权码', 'error');
                return;
            }
            
            try {
                showLoading('正在处理授权信息...');
                
                // 调用后端接口处理授权
                const response = await fetch('https://dev.api.mbbhg.com/api/wechat/callback/oauth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        state: state
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('授权成功！您现在可以接收服务号消息推送', 'success');
                    
                    // 3秒后关闭页面或跳转
                    setTimeout(() => {
                        if (window.opener) {
                            // 如果是弹窗打开的，向父页面发送消息
                            window.opener.postMessage({
                                type: 'wechat_auth_success',
                                data: result.data
                            }, '*');
                            window.close();
                        } else {
                            // 如果是直接访问的，可以跳转到指定页面
                            window.location.href = 'https://dev.api.mbbhg.com/auth-success.html';
                        }
                    }, 3000);
                    
                } else {
                    showMessage(result.message || '授权失败，请重试', 'error');
                }
                
            } catch (error) {
                console.error('处理授权回调失败:', error);
                showMessage('网络错误，请重试', 'error');
            } finally {
                hideLoading();
            }
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否是授权回调
            if (getQueryParam('code')) {
                handleAuthCallback();
            }
            
            // 检查是否在微信浏览器中
            const isWechat = /micromessenger/i.test(navigator.userAgent);
            if (!isWechat && !getQueryParam('code')) {
                showMessage('请在微信中打开此页面进行授权', 'error');
                document.getElementById('authBtn').disabled = true;
            }
        });
    </script>
</body>
</html>
