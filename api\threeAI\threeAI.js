
import http from '@/utils/request'
function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 100000 // 设置超时时间为100秒
    })
}
const baseUrl = `/services`
export default {
        // 获取会话ID
    getSessionId() {
        return request(
            `${baseUrl}/whiskerguardaiservice/api/ai/conversations/generate`,
            {}, 'POST')
    },
    // 岗位职责综合分析
    analysisAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/duty/positions/ai/comprehensive/analysis`,
            params, 'POST')
    },
    //岗位职责岗位说明书
    getDutyPosition(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/duty/positions/parse/job/description`,
            params, 'POST')
    },
    //岗位 ai生成合规要求
    getCompliance(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/duty/positions/generate/compliance/requirements`,
            params, 'POST')
    },
    //岗位 ai生成八大项
    getEight(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/duty/positions/ai/power/analysis`,
            params, 'POST')
    },
    // AI风险识别综合分析
    getRiskAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/compliance/risk/list/ai/comprehensive/analysis`,
            params, 'POST')
    },
    // AI风险识别业务类型风险点
    getRiskTypeAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/compliance/risk/list/ai/analysis`,
            params, 'POST')
    },
    // AI风险识别分析风险点对应的法律法规
    getRiskLawAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/compliance/risk/list/ai/regulation/analysis`,
            params, 'POST')
    },
    // AI风险识别分析风险点对应的防控措施和责任主体
    getRiskControlAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/compliance/risk/list/ai/measure/analysis`,
            params, 'POST')
    },
    // AI风险识别分析风险点对应的风险等级
    getRiskRankAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/compliance/risk/list/model/analysis`,
            params, 'POST')
    },
    getRiskLevelAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/compliance/risk/list/ai/level/analysis`,
            params, 'POST')
    },
    // ai业务流程综合分析
    getProcessAI(params) {
        return request(
            `${baseUrl}/compliancelistservice/api/v2/biz/process/ai/analyze`,
            params, 'POST')
    },
}