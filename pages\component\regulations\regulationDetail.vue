<template>
  <view class="regulations-detail-container">
    <!-- Main Content -->
    <view class="main-content">
      <!-- Title Section -->
      <view class="card title-section">
        <view class="title-row">
          <view class="title-container">
            <text class="title">{{ detailForm.title }}</text>
          </view>
          <!-- <view class="status-container">
            <view class="status-badge">已发布</view>
          </view> -->
          <view class="collect-icon" @click="handleCollect">
            <uni-icons :type="detailForm.isCollect ? 'star-filled' : 'star'" 
                       :color="detailForm.isCollect ? '#FFD700' : '#999999'" 
                       size="20"></uni-icons>
          </view>
        </view>
        <view class="info-section">
          <view class="info-item">
            <text class="info-label">发布文件号：</text>
            <text class="info-value">{{ detailForm.code }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">法律类型：</text>
            <text class="info-value">{{ detailForm.lawType }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">发布日期：</text>
            <text class="info-value">{{ detailForm.pubDate }}</text>
          </view>
        </view>
      </view>

      <!-- Tab Navigation -->
      <view class="tab-bar">

        <view @click="active = index" class="tab-item" :class="{ active: active === index }"
          v-for="(tab, index) in tabs" :key="index">
          <text class="tab-text">{{ tab }}</text>
        </view>
      </view>
      <!-- Content Section @click.stop="goDocument"-->
      <view class="card content-section">
        <!-- {{ detailForm.content }} -->
          <mp-html :content="detailForm.content" />
      </view>
      <view class="card content-section">
        <view class="attachment-header">
          <text class="chapter-title">附件: </text>
          <FileLink :fileUrl="detailForm.detailUrl" />
        </view>
        <view class="divider"></view>
      </view>
    </view>
    <FooterBar @click="handleTransformation" :buttons="buttons" />
  </view>
</template>

<script setup>
import FooterBar from '@/components/footerBar.vue'
import FileLink from '@/components/FileLink/FileLink.vue'
import regulations from '@/api/regulations/regulations.js'
import collectApi from './regulations.js'
import { ref, onMounted, computed } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import getDictData from '@/utils/dict.js'

// 字典数据
const dictData = ref([]);

// 获取字典数据
onMounted(async () => {
  try {
    const res = await getDictData('4');
    if (res && res.length > 0) {
      dictData.value = res.map(item => ({
        name: item.name,
        value: item.value
      }));
    }
  } catch (error) {
  }
});

// 状态映射计算属性
const statusText = computed(() => {
  if (!dictData.value || dictData.value.length === 0) {
    return detailForm.value.status || '';
  }
  const statusItem = dictData.value.find(item => item.value === detailForm.value.status);
  return statusItem ? statusItem.name : detailForm.value.status || '';
});
const id = ref(null);
const loading = ref(false);
// 收藏相关状态
const collectLoading = ref(false);
let collectDebounceTimer = null;
const buttons = [
  {
    text: '转化为内部制度',
    type: 'submit',
    slotName: 'submit',
    bgColor: '#1a73e8',
    textColor: '#fff'
  }
]

//更新规章
function updatedDoc() {
  // proxy.$api('post', '/whiskerguardregulatoryservice/api/enterprise/regulations/update/' + id, {
  // }).then(res => {
  //   console.log(res)
  // })
}

// 立即转化
async function handleTransformation() {
  // 防止重复点击
  if (loading.value) {
    return;
  }

  // 二次确认提醒
  uni.showModal({
    title: '确认操作',
    content: '转化后将提交管理员审核，确认继续？',
    confirmText: '确定',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        try {
          loading.value = true;
          uni.showLoading({
            title: '转化中...'
          });

          const result = await regulations.saveTransforms({
            regulationId: Number(id.value)
          });

          console.log(result);

          uni.hideLoading();
          uni.showToast({
            title: '转化已提交成功，待管理员审核后生效。',
            icon: 'success'
          });

          // 延迟返回上一级页面，让用户看到成功提示
          setTimeout(() => {
            uni.navigateBack();
          }, 800);

        } catch (error) {
          // console.error('转化失败:', error);
          // uni.showToast({
          //   title: '转化失败',
          //   icon: 'error'
          // });
        } finally {
          uni.hideLoading();
          loading.value = false;
        }
      }
    }
  });
}

const tabs = ref(['法律法规内容']);
const detailForm = ref({});
const active = ref(0);
onLoad((options) => {
  id.value = options.id
  uni.showLoading({
    title: '加载中...'
  });
  regulations.lawsDetail({ id: id.value }).then(res => {
    detailForm.value = res
    // 检查收藏状态
    checkCollectStatus();
    uni.hideLoading();
  }).catch(err => {
    uni.hideLoading();
    console.error('获取详情失败:', err);
  })
})
const goDocument = () => {
  uni.navigateTo({
    url: '/pages/component/regulations/documentConversion',
  });
};
const goBack = () => {
  uni.navigateBack();
};
const handleEdit = () => {
  uni.showToast({
    title: '编辑功能',
    icon: 'none'
  });
};

const handleReview = () => {
  uni.showToast({
    title: '审查功能',
    icon: 'none'
  });
};

// 检查收藏状态
const checkCollectStatus = () => {
  // 收藏状态现在通过detailForm.isCollect字段获取，无需额外处理
  console.log('当前收藏状态:', detailForm.value.isCollect);
};

// 处理收藏/取消收藏
const handleCollect = () => {
  // 防抖处理，防止重复点击
  if (collectLoading.value) {
    return;
  }
  
  // 清除之前的定时器
  if (collectDebounceTimer) {
    clearTimeout(collectDebounceTimer);
  }
  
  // 设置防抖定时器
  collectDebounceTimer = setTimeout(() => {
    performCollectAction();
  }, 300); // 300ms防抖
};

// 执行收藏操作
const performCollectAction = async () => {
  if (collectLoading.value) {
    return;
  }
  
  collectLoading.value = true;
  
  try {
    if (detailForm.value.isCollect) {
      // 取消收藏
      const params = {
        type: 2, // 2表示法律法规
        regulationId: id.value
      };
      
      await collectApi.cancelCollect(params);
      detailForm.value.isCollect = false;
      
      uni.showToast({
        title: '取消收藏成功',
        icon: 'success',
        duration: 1500
      });
    } else {
      // 添加收藏
      const params = {
        regulationId: parseInt(id.value),
        regulationTitle: detailForm.value.title || '',
        type: 2 // 2表示法律法规
      };
      
      await collectApi.addCollect(params);
      detailForm.value.isCollect = true;
      
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
        duration: 1500
      });
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    
    // 根据错误信息给出不同的提示
    let errorMessage = '操作失败，请重试';
    if (error.message && error.message.includes('重复')) {
      errorMessage = detailForm.value.isCollect ? '已取消收藏' : '已收藏';
      // 如果是重复操作，更新状态
      detailForm.value.isCollect = !detailForm.value.isCollect;
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 1500
    });
  } finally {
    collectLoading.value = false;
  }
};

// 组件卸载时清除定时器
onUnload(() => {
  if (collectDebounceTimer) {
    clearTimeout(collectDebounceTimer);
  }
});


</script>

<style lang="scss" scoped>
.regulations-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  background-color: #F5F7FA;

  .main-content {
    flex: 1;
    padding-bottom: 112rpx;
  }

  .card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 32rpx;
    padding: 32rpx;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .title-section {
    .title-row {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 24rpx;
      position: relative;
    }
    
    .title-container {
      flex: 1;
      margin-right: 16rpx;
    }
    
    .title {
      font-size: 36rpx;
      color: #333333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
    
    .status-container {
      position: absolute;
      right: 60rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .collect-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background-color: #f8f9fa;
      transition: all 0.3s ease;
      
      &:active {
        background-color: #e9ecef;
        transform: scale(0.95);
      }
    }
    
    .info-section {
      margin-top: 16rpx;
    }
    
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .info-label {
      font-size: 28rpx;
      color: #666666;
      min-width: 160rpx;
    }
    
    .info-value {
      font-size: 28rpx;
      color: #333333;
      flex: 1;
    }
  }

  .status-badge {
    background-color: #E6F4FF;
    color: #1A73E8;
    border-radius: 16rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    white-space: nowrap;
  }

  .tab-bar {
    height: 88rpx;
    background-color: #FFFFFF;
    display: flex;
    margin: 0 32rpx;
  }

  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tab-item.active {
    border-bottom: 2rpx solid #1A73E8;
  }

  .tab-text {
    font-size: 28rpx;
  }

  .tab-item.active .tab-text {
    color: #1A73E8;
  }

  .tab-item:not(.active) .tab-text {
    color: #666666;
  }

  .content-section {
    margin-top: 16rpx;
  }

  .content-item {
    padding: 32rpx 0;
    border-bottom: 1px solid #E0E0E0;
  }

  .last-item {
    border-bottom: none;
  }

  .chapter-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    display: block;
    margin-bottom: 16rpx;
  }



  .content-text {
    font-size: 28rpx;
    color: #333333;
    line-height: 48rpx;
    display: block;
    margin-top: 16rpx;
  }

  .attachment-item {
    height: 96rpx;
    display: flex;
    align-items: center;
    padding: 0 16rpx;
  }

  .attachment-name {
    font-size: 28rpx;
    color: #333333;
    margin-left: 24rpx;
  }
}
</style>