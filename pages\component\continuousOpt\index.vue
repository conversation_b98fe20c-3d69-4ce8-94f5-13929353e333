<!-- 持续优化模块 - 滑动切换选项卡 -->
<template>
	<!-- 使用z-paging-swiper为根节点可以免计算高度 -->
	<z-paging-swiper>
		<!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
		<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
		<template #top>
			<z-tabs ref="tabs" :list="tabList" :current="current" @change="tabsChange" />
		</template>
		<!-- swiper必须设置height:100%，因为swiper有默认的高度，只有设置高度100%才可以铺满页面  -->
		<swiper class="swiper" :current="current" @transition="swiperTransition"
			@animationfinish="swiperAnimationfinish">
			<swiper-item v-show="current === 0">
				<surveyList></surveyList>
			</swiper-item>
			<swiper-item v-show="current === 1">
				<dutyList></dutyList>
			</swiper-item>
			<swiper-item v-show="current === 2">
				<improvementList></improvementList>
			</swiper-item>
		</swiper>
	</z-paging-swiper>
</template>

<script>
import surveyList from './surveyTask/surveyList.vue'
import dutyList from './dutyTask/dutyList.vue'
import improvementList from './improvementTask/improvementList.vue'

export default {
	components: {
		surveyList,
		dutyList,
		improvementList,
	},
	data() {
		return {
			tabList: ['调查', '整改', '持续'],
			current: 0, // tabs组件的current值，表示当前活动的tab选项
		};
	},
	onShow() {
		// 在页面onShow的时候，刷新当前列表（不是必须的）
		// this.$refs.listItem && this.reloadCurrentList();
	},
	methods: {
		// tabs通知swiper切换
		tabsChange(index) {
			console.log('tabs通知swiper切换', index);
			this.current = index;
		},
		// swiper滑动中
		swiperTransition(e) {
			this.$refs.tabs.setDx(e.detail.dx);
		},
		// swiper滑动结束
		swiperAnimationfinish(e) {
			this.current = e.detail.current;
			this.$refs.tabs.unlockDx();
		},
		// 如果要通知当前展示的z-paging刷新，请调用此方法
		reloadCurrentList() {
			// this.$refs.listItem[this.current].reload();
		}
	}
}
</script>

<style>
.swiper {
	height: 100%;
}
</style>