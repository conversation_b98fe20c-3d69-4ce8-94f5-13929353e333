<template>
    <view class="dashboard-container">
        <!-- 主体内容区域 -->
        <view class="content">
            <!-- 职责信息区 -->
            <view class="duty-section">
                <view class="duty-header" @click="toggleDutyPanel">
                    <text class="duty-title">我的岗位职责</text>
                    <uni-icons :type="isDutyExpanded ? 'top' : 'bottom'" size="14" color="#666" />
                </view>
                <view class="duty-panel" v-if="isDutyExpanded">
                    <view class="duty-info">
                        <view class="info-item">
                            <text class="info-label">员工姓名：</text>
                            <text class="info-value">{{ dutyPositionData.employeeName || '暂无数据' }}</text>
                        </view>
                        <view class="info-item">
                            <!-- <text class="info-label">员工ID：</text>
                            <text class="info-value">{{ dutyPositionData.employeeId || '暂无数据' }}</text> -->
                        </view>
                    </view>
                    
                    <!-- 为每条职责数据创建独立的标签页组 -->
                    <view v-if="dutyPositionData.positionDuties && dutyPositionData.positionDuties.length > 0">
                        <view v-for="(dutyItem, dutyIndex) in dutyPositionData.positionDuties" :key="dutyIndex" class="duty-group">
                            <!-- <view class="duty-group-title">职责 {{ dutyIndex + 1 }}</view> -->
                            <view class="duty-group-title">{{ dutyItem.orgUnitName }}</view>
                            <view class="duty-tabs">
                                <view v-for="(tab, tabIndex) in dutyTabs" :key="tabIndex" class="duty-tab"
                                    :class="{ active: currentDutyTab[dutyIndex] === tabIndex }" 
                                    @click="switchDutyTab(dutyIndex, tabIndex)">
                                    {{ tab }}
                                </view>
                            </view>
                            <view class="duty-content">
                                <view class="duty-list">
                                    <view v-if="getCurrentDutyContent(dutyItem, currentDutyTab[dutyIndex])" class="duty-item">
                                        {{ getCurrentDutyContent(dutyItem, currentDutyTab[dutyIndex]) }}
                                    </view>
                                    <view v-else class="duty-item empty">
                                        暂无相关职责数据
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-else class="duty-content">
                        <view class="duty-item empty">
                            暂无职责数据
                        </view>
                    </view>
                </view>
            </view>
            <!-- 统计卡片 -->
            <view class="stats-card">
                <view class="stat-item">
                    <text class="stat-value">{{ statistics.todayPendingCount || 0 }}</text>
                    <text class="stat-label">今日待办</text>
                </view>
                <view class="stat-item">
                    <text class="stat-value">{{ statistics.thisWeekCompletedCount || 0 }}</text>
                    <text class="stat-label">本周完成</text>
                </view>
                <view class="stat-item">
                    <text class="stat-value warning">2</text>
                    <text class="stat-label">风险提醒</text>
                </view>
                <view class="stat-item">
                    <text class="stat-value success">85%</text>
                    <text class="stat-label">学习进度</text>
                </view>
            </view>
            <!-- 今日任务 -->
            <view class="card">
                <view class="card-header">
                    <text class="card-title">今日任务</text>
                </view>
                <view class="task-list" v-if="todayTasks.length > 0">
                    <view 
                        class="task-item" 
                        :class="{ urgent: task.isUrgent }"
                        v-for="task in todayTasks" 
                        :key="task.id"
                        @click="handleTaskClick(task)"
                    >
                        <uni-icons 
                            :type="task.isUrgent ? 'notification-filled' : 'checkbox-filled'" 
                            size="20" 
                            :color="task.isUrgent ? '#FF5252' : '#1E88E5'" 
                        />
                        <view class="task-content">
                            <text class="task-title">{{ task.title }}</text>
                            <text class="task-time">{{ task.timeDisplay }}</text>
                            <text class="task-desc" v-if="task.description">{{ task.description }}</text>
                        </view>
                        <text class="task-action">处理</text>
                    </view>
                </view>
                <view class="task-empty" v-else>
                    <uni-icons type="checkmarkempty" size="40" color="#ccc" />
                    <text class="empty-text">暂无今日任务</text>
                </view>
            </view>
            <!-- 快捷功能 -->
            <view class="card" v-if="false">
                <view class="card-header">
                    <text class="card-title">快捷功能</text>
                </view>
                <view class="quick-grid">
                    <view class="quick-item">
                        <image class="quick-icon"
                            src="https://ai-public.mastergo.com/ai/img_res/23d9c49b890d7a17b5d46f22a42ff1d2.jpg" />
                        <text class="quick-text">合同查询</text>
                    </view>
                    <view class="quick-item">
                        <image class="quick-icon"
                            src="https://ai-public.mastergo.com/ai/img_res/cdabefef7d4587305f54891428f5e416.jpg" />
                        <text class="quick-text">风险上报</text>
                    </view>
                    <view class="quick-item">
                        <image class="quick-icon"
                            src="https://ai-public.mastergo.com/ai/img_res/fa0fd1b4da65d25b595c839cbe2954c6.jpg" />
                        <text class="quick-text">合规咨询</text>
                    </view>
                    <view class="quick-item">
                        <image class="quick-icon"
                            src="https://ai-public.mastergo.com/ai/img_res/914b24f9c7c5fe3a3e0e92c0f0b7856d.jpg" />
                        <text class="quick-text">培训中心</text>
                    </view>
                    <view class="quick-item">
                        <image class="quick-icon"
                            src="https://ai-public.mastergo.com/ai/img_res/e679478e2c84c08bd4321ebb959ba7ff.jpg" />
                        <text class="quick-text">法规库</text>
                    </view>
                    <view class="quick-item">
                        <image class="quick-icon"
                            src="https://ai-public.mastergo.com/ai/img_res/816559b9127c49ac46f2b08efdfd10db.jpg" />
                        <text class="quick-text">智能问答</text>
                    </view>
                </view>
            </view>
            <!-- 合规状态 -->
            <view class="card">
                <view class="card-header">
                    <text class="card-title">合规状态</text>
                </view>
                <view class="compliance">
                    <view class="compliance-chart" :class="complianceChartClass">
                        <view class="compliance-progress">
                            <text class="compliance-score">{{ complianceScore }}</text>
                            <text class="compliance-rating">{{ complianceRating }}</text>
                        </view>
                    </view>
                    <view class="compliance-metrics">
                        <view class="metric-item">
                            <text class="metric-label">任务完成率</text>
                            <view class="metric-bar">
                                <view class="metric-progress" :style="taskProgressStyle"></view>
                            </view>
                            <text class="metric-value">{{ formattedTaskCompletionRate }}%</text>
                        </view>
                        <view class="metric-item">
                            <text class="metric-label">培训完成率</text>
                            <view class="metric-bar">
                                <view class="metric-progress" :style="courseProgressStyle"></view>
                            </view>
                            <text class="metric-value">{{ formattedCourseCompletionRate }}%</text>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 学习培训 -->
            <view class="card">
                <view class="card-header">
                    <text class="card-title">学习培训</text>
                    <text class="card-link" @click="navigateToLearningCenter">学习中心</text>
                </view>
                
                <!-- 学习统计信息 -->
                <view v-if="false" class="learning-stats">
                    <view class="stat-row">
                        <view class="stat-item">
                            <text class="stat-label">总课程数</text>
                            <text class="stat-value">{{ learningData.totalCourses }}</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-label">已完成</text>
                            <text class="stat-value">{{ learningData.completedCourses }}</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-label">学习中</text>
                            <text class="stat-value">{{ learningData.inProgressCourses }}</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-label">未开始</text>
                            <text class="stat-value">{{ learningData.notStartedCourses }}</text>
                        </view>
                    </view>
                    <view class="activity-level">
                        <text class="activity-label">活跃度：</text>
                        <text class="activity-value">{{ learningData.activityLevel }}</text>
                    </view>
                </view>
                
                <!-- 当前学习课程（从 courseProgresses 中获取进行中的课程） -->
                <view v-if="learningData.courseProgresses && learningData.courseProgresses.length > 0" class="current-courses">
                    <text class="section-title">我的课程</text>
                    <view class="course-list">
                        <view 
                            v-for="course in learningData.courseProgresses.slice(0, 3)" 
                            :key="course.courseId" 
                            class="course-item"
                            @click="navigateToCourse(course)"
                        >
                            <image class="course-thumb" :src="course.courseThumbnail || 'https://ai-public.mastergo.com/ai/img_res/f6905e5133bca52e5caca7dfe0e6ae48.jpg'" />
                            <view class="course-info">
                                <text class="course-name">{{ course.courseName || course.courseTitle }}</text>
                                <view class="progress-container" v-if="course.progressPercentage !== undefined">
                                    <view class="progress-bar">
                                        <view class="progress" :style="`width: ${course.progressPercentage}%;`"></view>
                                    </view>
                                    <text class="progress-text">{{ course.progressPercentage }}%</text>
                                </view>
                                <text class="course-status">{{ course.status || '进行中' }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                
                <!-- 无课程时的提示 -->
                <view v-else class="no-courses">
                    <text class="no-course-text">暂无学习课程</text>
                    <uv-button type="primary" size="mini" @click="navigateToLearningCenter">开始学习</uv-button>
                </view>
            </view>
            <!-- 智能助手 -->
            <view class="card">
                <view class="assistant">
                    <image class="assistant-avatar"
                        src="https://ai-public.mastergo.com/ai/img_res/3b9301aee4f92d83c19f6c33bf791ecd.jpg" />
                    <view class="assistant-content">
                        <text class="assistant-greeting">您好，有什么可以帮您？</text>
                        <view class="quick-questions">
                            <text class="question-tag">法规查询</text>
                            <text class="question-tag">合同问题</text>
                        </view>
                        <uv-button @click="navigateToQa" type="primary" size="mini">开始对话</uv-button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
import dashboardApi from '@/api/dashboard/dashboard.js';
import studyApi from '@/api/study/index.js';

const currentDate = ref(new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
}));

// 加载状态管理
const loading = ref({
    statistics: false,
    tasks: false,
    learning: false,
    duty: false
});

// 统计数据
const statistics = ref({
    todayPendingCount: 0,
    thisWeekCompletedCount: 0,
    taskCompletionRate: 0,
});

// 今日任务数据
const todayTasks = ref([]);

// 学习培训数据
const learningData = ref({
    userId: '',
    statisticsTime: '',
    totalLearningDurationSeconds: 0,
    totalLearningDurationHours: 0.0,
    monthlyLearningDurationSeconds: 0,
    monthlyLearningDurationHours: 0.0,
    totalCourses: 0,
    completedCourses: 0,
    inProgressCourses: 0,
    notStartedCourses: 0,
    courseCompletionRate: null,
    totalChapters: 0,
    completedChapters: 0,
    chapterCompletionRate: null,
    learningDays: null,
    averageDailyLearningMinutes: null,
    lastLearningTime: null,
    consecutiveLearningDays: null,
    activityLevel: '未开始',
    behaviorStats: {},
    recentWeekStats: null,
    courseProgresses: [],
    trainingPlanProgresses: null
});

// 岗位职责数据
const dutyPositionData = ref({
    employeeName: '',
    employeeId: 0,
    positionDuties: []
});

const isDutyExpanded = ref(false);
const currentDutyTab = ref({}); // 改为对象，存储每个职责组的当前标签页索引
const dutyTabs = ['基本职责', '合规要求', '防控措施'];

// 计算属性 - 格式化显示数据
const formattedTaskCompletionRate = computed(() => {
    const rate = statistics.value.taskCompletionRate;
    return rate !== null && rate !== undefined ? Math.round(rate) : 0;
});

const formattedCourseCompletionRate = computed(() => {
    const rate = learningData.value.courseCompletionRate;
    return rate !== null && rate !== undefined ? Math.round(rate) : 0;
});

// 计算属性 - 进度条样式
const taskProgressStyle = computed(() => {
    const rate = formattedTaskCompletionRate.value;
    return `width: ${Math.min(Math.max(rate, 0), 100)}%;`;
});

const courseProgressStyle = computed(() => {
    const rate = formattedCourseCompletionRate.value;
    return `width: ${Math.min(Math.max(rate, 0), 100)}%;`;
});

// 计算属性 - 合规分数（任务完成率和培训完成率的平均值，向上取整）
const complianceScore = computed(() => {
    const taskRate = statistics.value.taskCompletionRate || 0;
    const courseRate = learningData.value.courseCompletionRate || 0;
    const average = (taskRate + courseRate) / 2;
    return Math.ceil(average); // 向上取整
});

// 计算属性 - 合规评级
const complianceRating = computed(() => {
    const score = complianceScore.value;
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 70) return '合格';
    if (score >= 60) return '待改进';
    return '不合格';
});

// 计算属性 - 合规图表样式类
const complianceChartClass = computed(() => {
    const score = complianceScore.value;
    if (score >= 90) return 'compliance-excellent';
    if (score >= 80) return 'compliance-good';
    if (score >= 70) return 'compliance-qualified';
    if (score >= 60) return 'compliance-needs-improvement';
    return 'compliance-poor';
});

// 初始化职责标签页状态
const initDutyTabs = () => {
    const duties = dutyPositionData.value.positionDuties || [];
    const tabState = {};
    duties.forEach((_, index) => {
        tabState[index] = 0; // 每个职责组默认显示第一个标签页
    });
    currentDutyTab.value = tabState;
};

// 切换岗位职责标签页
const switchDutyTab = (dutyIndex, tabIndex) => {
    currentDutyTab.value[dutyIndex] = tabIndex;
};

// 获取指定职责项和标签页的内容
const getCurrentDutyContent = (dutyItem, tabIndex) => {
    if (!dutyItem || typeof dutyItem !== 'object') {
        return '';
    }
    
    switch (tabIndex) {
        case 0: // 基本职责
            return dutyItem.basicDuty || '';
        case 1: // 合规要求
            return dutyItem.complianceRequirement || '';
        case 2: // 防控措施
            return dutyItem.controlMeasures || '';
        default:
            return '';
    }
};

// 获取统计数据
const getStatistics = async () => {
    loading.value.statistics = true;
    try {
        const res = await dashboardApi.getStatistics();
        if (res) {
            statistics.value = {
                todayPendingCount: res.todayPendingCount || 0,
                thisWeekCompletedCount: res.thisWeekCompletedCount || 0,
                taskCompletionRate: res.totalTaskCompletionRate || res.taskCompletionRate || 0,
            };
        }
    } catch (error) {
        console.error('获取统计数据失败:', error);
        uni.showToast({
            title: '获取数据失败',
            icon: 'none'
        });
    } finally {
        loading.value.statistics = false;
    }
};

// 获取今日任务数据
const getTodayTasks = async () => {
    try {
        const res = await dashboardApi.getTodoEvents();
        if (res && Array.isArray(res)) {
            //  isUrgent: task.eventType === 'URGENT' || task.status === 'URGENT' || isTaskUrgent(task),
            todayTasks.value = res
        } else {
            todayTasks.value = [];
        }
    } catch (error) {
        console.error('获取今日任务失败:', error);
        uni.showToast({
            title: '获取今日任务失败',
            icon: 'none'
        });
        todayTasks.value = [];
    }
};

// 判断任务是否紧急
const isTaskUrgent = (task) => {
    if (!task.createdAt) return false;
    
    try {
        let createDate;
        if (task.createdAt.seconds) {
            createDate = new Date(task.createdAt.seconds * 1000);
        } else {
            createDate = new Date(task.createdAt);
        }
        
        const now = new Date();
        const diffHours = (now.getTime() - createDate.getTime()) / (1000 * 60 * 60);
        
        // 如果任务创建超过4小时且未完成，标记为紧急
        return diffHours > 4 && task.status !== 'COMPLETED';
    } catch (error) {
        console.error('判断任务紧急程度失败:', error);
        return false;
    }
};



// 获取岗位职责统计数据
const getDutyPositionStats = async () => {
    try {
        const res = await dashboardApi.getDutyPositionStats();
        if (res) {
            dutyPositionData.value = {
                employeeName: res.employeeName || '',
                employeeId: res.employeeId || 0,
                positionDuties: res.positionDuties || []
            };
            // 数据加载完成后初始化标签页状态
            initDutyTabs();
        }
    } catch (error) {
        console.error('获取岗位职责数据失败:', error);
        uni.showToast({
            title: '获取岗位职责数据失败',
            icon: 'none'
        });
    }
};

// 获取学习培训数据
const getLearningData = async () => {
    loading.value.learning = true;
    try {
        const res = await studyApi.getLearningProgressOverview();
        if (res) {
            // 直接将接口返回的数据赋值给 learningData，确保数据完整性
            learningData.value = {
                ...learningData.value, // 保留默认值
                ...res, // 覆盖接口返回的数据
                // 确保关键字段有默认值
                courseCompletionRate: res.courseCompletionRate ?? null,
                courseProgresses: res.courseProgresses || []
            };
        }
    } catch (error) {
        console.error('获取学习培训数据失败:', error);
        uni.showToast({
            title: '获取学习数据失败',
            icon: 'none'
        });
    } finally {
        loading.value.learning = false;
    }
};

const toggleDutyPanel = () => {
    isDutyExpanded.value = !isDutyExpanded.value;
};

// 处理任务点击事件
const handleTaskClick = (task) => {
    if (!task || !task.id) {
        uni.showToast({
            title: '任务信息不完整',
            icon: 'none'
        });
        return;
    }
    
    // 根据任务类型跳转到对应的详情页面
    let url = '';
    
    // 根据事件类型判断跳转页面
    switch (task.eventType) {
        case 'CONTRACT':
            // 合同审查详情页
            url = `/pages/component/respond/contractReviewDetail?id=${task.businessId}`;
            break;
        case 'DECISION':
            // 决策审查详情页
            url = `/pages/component/respond/decisionReviewDetail?id=${task.businessId}`;
            break;
        case 'SUPPLEMENTAL':
            // 补充审查详情页
            url = `/pages/component/respond/supplementalReviewDetail?id=${task.businessId}`;
            break;
        case 'COURSE_LEARNING':
            // 课程学习详情页
            url = `/pages/component/study/courseDetail?id=${task.businessId}`;
            break;
        default:
            // 未知类型，显示提示
            uni.showToast({
                title: '暂不支持该类型任务',
                icon: 'none'
            });
            return;
    }
    
    // 执行页面跳转
    uni.navigateTo({
        url: url,
        fail: (error) => {
            console.error('页面跳转失败:', error);
            uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
            });
        }
    });
};

// 导航到学习中心
const navigateToLearningCenter = () => {
    uni.switchTab({
        url: '/pages/study/study',
    });
};

const navigateToQa = ()=>{
	uni.navigateTo({
		url: '/pages/component/qa/qaDetail',
	})
}

// 导航到课程详情
const navigateToCourse = (course) => {
    if (!course || (!course.courseId && !course.id)) {
        uni.showToast({
            title: '课程信息不完整',
            icon: 'none'
        });
        return;
    }
    
    // 使用 courseId 或 id 作为课程标识
    const courseId = course.courseId || course.id;
    
    uni.navigateTo({
        url: `/pages/component/study/courseDetail?id=${courseId}`,
        fail: (error) => {
            console.error('跳转课程详情失败:', error);
            uni.showToast({
                title: '跳转失败',
                icon: 'none'
            });
        }
    });
};

const onRefresh = () => {
    // 下拉刷新时重新获取数据
    getStatistics();
    getTodayTasks();
    getDutyPositionStats();
    getLearningData();
    setTimeout(() => {
        uni.stopPullDownRefresh();
    }, 1000);
};

// 页面加载时获取数据
onMounted(() => {
    getStatistics();
    getTodayTasks();
    getDutyPositionStats();
    getLearningData();
});
</script>
<style>
page {
    height: 100%;
}

.dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f7fa;
}

/* 职责信息区样式 */
.duty-section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.duty-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 72rpx;
    padding: 0 20rpx;
    background-color: #fff;
}

.duty-title {
    font-size: 14px;
    color: #666;
}

.duty-panel {
    background-color: #F0F7FF;
    padding: 20rpx;
}

.duty-info {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item {
    display: flex;
    align-items: center;
}

.info-label {
    font-size: 14px;
    color: #666;
    margin-right: 8rpx;
}

.info-value {
    font-size: 14px;
    color: #333;
}

.info-value.status {
    color: #4CAF50;
}

.duty-tabs {
    display: flex;
    margin: 20rpx 0;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 4rpx;
}

.duty-tab {
    flex: 1;
    text-align: center;
    padding: 12rpx 0;
    font-size: 14px;
    color: #666;
    border-radius: 4rpx;
}

.duty-tab.active {
    background-color: #1E88E5;
    color: #fff;
}

/* 职责组样式 */
.duty-group {
    margin-bottom: 30rpx;
}

.duty-group:last-child {
    margin-bottom: 0;
}

.duty-group-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
    padding: 12rpx 16rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border-left: 4rpx solid #1E88E5;
}

.duty-list {
    background-color: #fff;
    border-radius: 8rpx;
    padding: 20rpx;
}

.duty-item {
    font-size: 14px;
    color: #333;
    line-height: 1.8;
    padding: 8rpx 0;
}

.duty-item.empty {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40rpx 0;
}

.title {
    font-size: 18px;
    font-weight: bold;
}

/* 内容区域样式 */
.content {
    flex: 1;
    overflow: auto;
    padding-bottom: 100rpx;
}

/* 卡片通用样式 */
.card {
    margin: 20rpx;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.card-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.card-link {
    font-size: 14px;
    color: #1E88E5;
}

/* 统计卡片样式 */
.stats-card {
    display: flex;
    justify-content: space-between;
    margin: 20rpx;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
}

.stat-value.warning {
    color: #FF5252;
}

.stat-value.success {
    color: #4CAF50;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

/* 任务列表样式 */
.task-list {
    display: flex;
    flex-direction: column;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1px solid #eee;
}

.task-item:last-child {
    border-bottom: none;
}

.task-item.urgent .task-title {
    color: #FF5252;
}

.task-content {
    flex: 1;
    margin-left: 16rpx;
}

.task-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 4rpx;
}

.task-action {
    font-size: 12px;
    color: #1E88E5;
}

.task-time {
    font-size: 12px;
    color: #999;
}

.task-desc {
    font-size: 12px;
    color: #666;
    margin-top: 4rpx;
    line-height: 1.4;
}

/* 任务空状态样式 */
.task-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;
}

.empty-text {
    font-size: 14px;
    color: #999;
    margin-top: 16rpx;
}

/* 快捷功能样式 */
.quick-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.quick-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 30%;
    margin-bottom: 20rpx;
}

.quick-icon {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 8rpx;
}

.quick-text {
    font-size: 12px;
    color: #333;
}

/* 合规状态样式 */
.compliance {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.compliance-chart {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    margin: 20rpx 0;
    border-radius: 100rpx;
    background: #E3F2FD;
    display: flex;
    justify-content: center;
    align-items: center;
}

.compliance-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 160rpx;
    background-color: #fff;
    border-radius: 80rpx;
    position: relative;
    z-index: 2;
}

.compliance-score {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

.compliance-rating {
    font-size: 14px;
    color: #666;
    margin-top: 4rpx;
}

/* 合规等级样式 */
.compliance-poor {
    background: conic-gradient(#ff4757 0% 50%, #E3F2FD 50% 100%); /* 0-59分 不合格 */
}

.compliance-needs-improvement {
    background: conic-gradient(#ff6348 0% 65%, #E3F2FD 65% 100%); /* 60-69分 待改进 */
}

.compliance-qualified {
    background: conic-gradient(#ffa502 0% 75%, #E3F2FD 75% 100%); /* 70-79分 合格 */
}

.compliance-good {
    background: conic-gradient(#5352ed 0% 85%, #E3F2FD 85% 100%); /* 80-89分 良好 */
}

.compliance-excellent {
    background: conic-gradient(#2ed573 0% 95%, #E3F2FD 95% 100%); /* 90-100分 优秀 */
}

.compliance-metrics {
    width: 100%;
}

.metric-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.metric-label {
    width: 120rpx;
    font-size: 12px;
    color: #666;
}

.metric-bar {
    flex: 1;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    margin: 0 16rpx;
}

.metric-progress {
    height: 100%;
    background: linear-gradient(to right, #1E88E5, #64B5F6);
    border-radius: 4px;
}

.metric-value {
    width: 60rpx;
    font-size: 12px;
    color: #1E88E5;
}

/* 学习培训样式 */
.learning-stats {
    margin-bottom: 20rpx;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.stat-row .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.stat-row .stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4rpx;
}

.stat-row .stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #1E88E5;
}

.completion-rate {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
}

.rate-label {
    font-size: 12px;
    color: #666;
}

.rate-value {
    font-size: 14px;
    font-weight: bold;
    color: #1E88E5;
}

.activity-level {
    display: flex;
    align-items: center;
}

.activity-label {
    font-size: 12px;
    color: #666;
}

.activity-value {
    font-size: 14px;
    color: #FF9800;
    font-weight: bold;
}

.current-courses {
    margin-top: 20rpx;
}

.section-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 16rpx;
    font-weight: bold;
}

.course-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.course-item {
    display: flex;
    align-items: center;
    padding: 16rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    cursor: pointer;
}

.course-thumb {
    width: 120rpx;
    height: 80rpx;
    border-radius: 6rpx;
    margin-right: 16rpx;
    flex-shrink: 0;
}

.course-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 8rpx;
    font-weight: 500;
}

.progress-container {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: #eee;
    border-radius: 3px;
    margin-right: 12rpx;
}

.progress {
    height: 100%;
    background: linear-gradient(to right, #1E88E5, #64B5F6);
    border-radius: 3px;
}

.progress-text {
    font-size: 12px;
    color: #1E88E5;
    min-width: 40rpx;
}

.course-status {
    font-size: 12px;
    color: #666;
}

.no-courses {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
}

.no-course-text {
    font-size: 14px;
    color: #999;
    margin-bottom: 20rpx;
}

/* 智能助手样式 */
.assistant {
    display: flex;
    align-items: center;
}

.assistant-avatar {
    width: 60px;
    height: 60px;
    border-radius: 30px;
    margin-right: 20rpx;
}

.assistant-content {
    flex: 1;
}

.assistant-greeting {
    font-size: 14px;
    color: #333;
    margin-bottom: 12rpx;
}

.quick-questions {
    display: flex;
    margin-bottom: 16rpx;
}

.question-tag {
    font-size: 12px;
    color: #1E88E5;
    background-color: #E3F2FD;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    margin-right: 12rpx;
}
</style>