<template>
    <view class="page-container">
        <!-- Top Navigation Bar -->
        <view class="nav-bar">
            <view class="nav-left">
                <uni-icons type="arrowleft" size="24" color="#1A73E8" @click="goBack"></uni-icons>
            </view>
            <!-- <view class="nav-title">合规评分详情</view> -->
            <view class="nav-right" @click="toggleMonthPicker">
                <text class="month-text">本月</text>
                <uni-icons type="arrowdown" size="14" color="#1A73E8"></uni-icons>
            </view>
        </view>

        <!-- Main Content -->
        <scroll-view class="main-content" scroll-y>
            <!-- Score Card -->
            <view class="score-card">
                <view class="score-header">
                    <view>
                        <text class="score-label">合规得分</text>
                        <view class="score-value-container">
                            <text class="score-value">85</text>
                            <text class="score-unit">分</text>
                        </view>
                    </view>
                    <view class="detail-link" @click="viewScoreDetail">
                        <text>查看详情</text>
                        <uni-icons type="arrow-up-right" size="14" color="#1A73E8"></uni-icons>
                    </view>
                </view>

                <view class="dimension-grid">
                    <!-- Dimension 1 -->
                    <view class="dimension-item">
                        <view class="progress-ring-container">
                            <svg class="progress-ring" viewBox="0 0 48 48">
                                <circle class="progress-ring-bg" cx="24" cy="24" r="21"></circle>
                                <circle class="progress-ring-fill" cx="24" cy="24" r="21" stroke-dasharray="131.88"
                                    stroke-dashoffset="13.19"></circle>
                            </svg>
                            <text class="progress-text">90%</text>
                        </view>
                        <text class="dimension-label">制度管理</text>
                    </view>

                    <!-- Dimension 2 -->
                    <view class="dimension-item">
                        <view class="progress-ring-container">
                            <svg class="progress-ring" viewBox="0 0 48 48">
                                <circle class="progress-ring-bg" cx="24" cy="24" r="21"></circle>
                                <circle class="progress-ring-fill" cx="24" cy="24" r="21" stroke-dasharray="131.88"
                                    stroke-dashoffset="26.38"></circle>
                            </svg>
                            <text class="progress-text">80%</text>
                        </view>
                        <text class="dimension-label">培训</text>
                    </view>

                    <!-- Dimension 3 -->
                    <view class="dimension-item">
                        <view class="progress-ring-container">
                            <svg class="progress-ring" viewBox="0 0 48 48">
                                <circle class="progress-ring-bg" cx="24" cy="24" r="21"></circle>
                                <circle class="progress-ring-fill" cx="24" cy="24" r="21" stroke-dasharray="131.88"
                                    stroke-dashoffset="39.56"></circle>
                            </svg>
                            <text class="progress-text">70%</text>
                        </view>
                        <text class="dimension-label">风险处置</text>
                    </view>

                    <!-- Dimension 4 -->
                    <view class="dimension-item">
                        <view class="progress-ring-container">
                            <svg class="progress-ring" viewBox="0 0 48 48">
                                <circle class="progress-ring-bg" cx="24" cy="24" r="21"></circle>
                                <circle class="progress-ring-fill" cx="24" cy="24" r="21" stroke-dasharray="131.88"
                                    stroke-dashoffset="19.78"></circle>
                            </svg>
                            <text class="progress-text">85%</text>
                        </view>
                        <text class="dimension-label">审查</text>
                    </view>
                </view>
            </view>

            <!-- History Chart -->
            <view class="chart-card">
                <view class="chart-header">
                    <text class="chart-title">历史趋势</text>
                    <view class="chart-tabs">
                        <text :class="['tab', activeTab === 6 ? 'tab-active' : 'tab-inactive']"
                            @click="switchChartData(6)">6个月</text>
                        <text :class="['tab', activeTab === 12 ? 'tab-active' : 'tab-inactive']"
                            @click="switchChartData(12)">12个月</text>
                    </view>
                </view>
                <view class="chart-container">
                    <qiun-data-charts 
                        type="line"
                        :opts="opts"
                        :chartData="chartData"
                        />
                </view>
            </view>

            <!-- Score Details -->
            <view class="details-card">
                <text class="details-title">得分构成明细</text>

                <view class="details-list">
                    <!-- Detail 1 -->
                    <view class="detail-item">
                        <view>
                            <text class="detail-name">制度管理</text>
                            <text class="detail-formula">20% × 90 = 18</text>
                        </view>
                        <uni-icons type="arrowright" size="16" color="#999999"></uni-icons>
                    </view>

                    <!-- Detail 2 -->
                    <view class="detail-item">
                        <view>
                            <text class="detail-name">培训</text>
                            <text class="detail-formula">30% × 80 = 24</text>
                        </view>
                        <uni-icons type="arrowright" size="16" color="#999999"></uni-icons>
                    </view>

                    <!-- Detail 3 -->
                    <view class="detail-item">
                        <view>
                            <text class="detail-name">风险处置</text>
                            <text class="detail-formula">25% × 70 = 17.5</text>
                        </view>
                        <uni-icons type="arrowright" size="16" color="#999999"></uni-icons>
                    </view>

                    <!-- Detail 4 -->
                    <view class="detail-item">
                        <view>
                            <text class="detail-name">审查</text>
                            <text class="detail-formula">25% × 85 = 21.25</text>
                        </view>
                        <uni-icons type="arrowright" size="16" color="#999999"></uni-icons>
                    </view>
                </view>
            </view>

            <!-- Improvement Suggestions -->
            <view class="suggestions-card">
                <text class="suggestions-title">改进建议</text>

                <view class="suggestions-grid">
                    <view class="suggestion-item" @click="updateRegulations">
                        <view class="suggestion-icon">
                            <uni-icons type="cloud-download-filled" size="20" color="#1A73E8"></uni-icons>
                        </view>
                        <text class="suggestion-text">更新制度库</text>
                    </view>

                    <view class="suggestion-item" @click="completeTraining">
                        <view class="suggestion-icon">
                            <uni-icons type="checkbox" size="20" color="#1A73E8"></uni-icons>
                        </view>
                        <text class="suggestion-text">完成培训</text>
                    </view>

                    <view class="suggestion-item" @click="handleRisks">
                        <view class="suggestion-icon">
                            <uni-icons type="minus" size="20" color="#1A73E8"></uni-icons>
                        </view>
                        <text class="suggestion-text">处理风险项</text>
                    </view>

                    <view class="suggestion-item" @click="initiateReview">
                        <view class="suggestion-icon">
                            <uni-icons type="search" size="20" color="#1A73E8"></uni-icons>
                        </view>
                        <text class="suggestion-text">发起审查</text>
                    </view>
                </view>
            </view>
        </scroll-view>
        <goNavitor url="/pages/component/homeSon/assessmentNotice" />
    </view>
</template>
<script>
import goNavitor from '../../../components/goNavitor.vue';
export default {
    data() {
      return {
        activeTab: 6,
        chartData: {},
        //您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
        opts: {
          color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
          padding: [15,10,0,15],
          dataLabel: false,
          dataPointShape: false,
          enableScroll: false,
          legend: {},
          xAxis: {
            disableGrid: true
          },
          yAxis: {
            gridType: "dash",
            dashLength: 2,
            data: [
              {
                min: 0,
                max: 150
              }
            ]
          },
          extra: {
            line: {
              type: "curve",
              width: 2,
              activeType: "hollow",
              linearType: "custom"
            }
          }
        }
      };
    },
    onReady() {
      this.getServerData();
    },
    methods: {
        switchChartData(index) {
            this.activeTab = index;
        },
      getServerData() {
        //模拟从服务器获取数据时的延时
        setTimeout(() => {
          //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
          let res = {
              categories: ["2018","2019","2020","2021","2022","2023"],
              series: [
                {
                  name: "成交量A",
                  linearColor: [
                    [
                      0,
                      "#1890FF"
                    ],
                    [
                      0.25,
                      "#00B5FF"
                    ],
                    [
                      0.5,
                      "#00D1ED"
                    ],
                    [
                      0.75,
                      "#00E6BB"
                    ],
                    [
                      1,
                      "#90F489"
                    ]
                  ],
                  data: [15,45,15,45,15,45]
                },
                {
                  name: "成交量B",
                  linearColor: [
                    [
                      0,
                      "#91CB74"
                    ],
                    [
                      0.25,
                      "#2BDCA8"
                    ],
                    [
                      0.5,
                      "#2AE3A0"
                    ],
                    [
                      0.75,
                      "#C4D06E"
                    ],
                    [
                      1,
                      "#F2D375"
                    ]
                  ],
                  data: [55,85,55,85,55,85]
                },
                {
                  name: "成交量C",
                  linearColor: [
                    [
                      0,
                      "#FAC858"
                    ],
                    [
                      0.33,
                      "#FFC371"
                    ],
                    [
                      0.66,
                      "#FFC2B2"
                    ],
                    [
                      1,
                      "#FA7D8D"
                    ]
                  ],
                  data: [95,125,95,125,95,125]
                }
              ]
            };
          this.chartData = JSON.parse(JSON.stringify(res));
        }, 500);
      },
    }
  };
</script>
<script setup>
import { ref, onMounted } from 'vue';
import { onReady } from '@dcloudio/uni-app';

const goBack = () => {
    uni.navigateBack();
};

const toggleMonthPicker = () => {
    // 实现月份选择器逻辑
};

const viewScoreDetail = () => {
    // 查看得分详情逻辑
};

const updateRegulations = () => {
    // 更新制度库逻辑
};

const completeTraining = () => {
    // 完成培训逻辑
};

const handleRisks = () => {
    // 处理风险项逻辑
};

const initiateReview = () => {
    // 发起审查逻辑
};

// onReady(() => {
//     // 初始化图表
//     const ctx = uni.createCanvasContext('historyChart');

//     // 绘制网格线
//     ctx.setStrokeStyle('#F0F0F0');
//     ctx.setLineWidth(1);

//     // 绘制Y轴网格线
//     for (let i = 0; i <= 5; i++) {
//         const y = 160 - i * 30;
//         ctx.moveTo(30, y);
//         ctx.lineTo(330, y);
//     }
//     ctx.stroke();

//     // 绘制X轴
//     ctx.setStrokeStyle('#E0E0E0');
//     ctx.moveTo(30, 160);
//     ctx.lineTo(330, 160);
//     ctx.stroke();

//     // 绘制X轴标签
//     ctx.setFontSize(10);
//     ctx.setFillStyle('#666666');
//     const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
//     for (let i = 0; i < months.length; i++) {
//         ctx.fillText(months[i], 30 + i * 60, 180);
//     }

//     // 绘制Y轴标签
//     for (let i = 0; i <= 5; i++) {
//         ctx.fillText((60 + i * 10).toString(), 10, 160 - i * 30 + 4);
//     }

//     // 绘制折线
//     const data = [72, 78, 82, 85, 83, 85];
//     ctx.setStrokeStyle('#1A73E8');
//     ctx.setLineWidth(3);
//     ctx.setLineCap('round');
//     ctx.setLineJoin('round');

//     ctx.moveTo(30, 160 - (data[0] - 60) * 3);
//     for (let i = 1; i < data.length; i++) {
//         ctx.lineTo(30 + i * 60, 160 - (data[i] - 60) * 3);
//     }
//     ctx.stroke();

//     // 绘制数据点
//     ctx.setFillStyle('#1A73E8');
//     for (let i = 0; i < data.length; i++) {
//         ctx.beginPath();
//         ctx.arc(30 + i * 60, 160 - (data[i] - 60) * 3, 4, 0, 2 * Math.PI);
//         ctx.fill();
//     }

//     // 绘制区域渐变
//     const gradient = ctx.createLinearGradient(0, 0, 0, 160);
//     gradient.addColorStop(0, 'rgba(26, 115, 232, 0.2)');
//     gradient.addColorStop(1, 'rgba(26, 115, 232, 0)');
//     ctx.setFillStyle(gradient);

//     ctx.moveTo(30, 160);
//     ctx.lineTo(30, 160 - (data[0] - 60) * 3);
//     for (let i = 1; i < data.length; i++) {
//         ctx.lineTo(30 + i * 60, 160 - (data[i] - 60) * 3);
//     }
//     ctx.lineTo(330, 160);
//     ctx.closePath();
//     ctx.fill();

//     ctx.draw();
// });
</script>

<style lang="scss" scoped>

.tab {
    transition: all 0.3s ease;
    cursor: pointer;
}

.tab-active {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.page-container {
    display: flex;
    flex-direction: column;
    // height: 100%;
}

/* Navigation Bar */
.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 32rpx;
    background-color: #FFFFFF;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
}

.nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
}

.nav-right {
    display: flex;
    align-items: center;
}

.month-text {
    font-size: 28rpx;
    color: #1A73E8;
    margin-right: 8rpx;
}

/* Main Content */
.main-content {
    flex: 1;
    padding-top: 88rpx;
    padding-bottom: 100rpx;
    background-color: #F5F7FA;
}

/* Score Card */
.score-card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 32rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 48rpx;
}

.score-label {
    font-size: 28rpx;
    color: #999999;
}

.score-value-container {
    display: flex;
    align-items: flex-end;
}

.score-value {
    font-size: 64rpx;
    font-weight: bold;
    color: #1A73E8;
    margin-right: 8rpx;
}

.score-unit {
    font-size: 28rpx;
    color: #1A73E8;
    margin-bottom: 8rpx;
}

.detail-link {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #1A73E8;
}

/* Dimension Grid */
.dimension-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32rpx;
}

.dimension-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.progress-ring-container {
    position: relative;
    width: 96rpx;
    height: 96rpx;
    margin-bottom: 16rpx;
}

.progress-ring {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-ring-bg {
    stroke: #E0E0E0;
    stroke-width: 6px;
    fill: transparent;
}

.progress-ring-fill {
    stroke: #1A73E8;
    stroke-width: 6px;
    fill: transparent;
    stroke-linecap: round;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
}

.dimension-label {
    font-size: 24rpx;
    color: #999999;
}

/* Chart Card */
.chart-card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 0 32rpx 32rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
}

.chart-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
}

.chart-tabs {
    display: flex;
}

.tab-active {
    font-size: 24rpx;
    color: #1A73E8;
    background-color: rgba(26, 115, 232, 0.1);
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    margin-right: 16rpx;
}

.tab-inactive {
    font-size: 24rpx;
    color: #999999;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
}

.chart-container {
    width: 100%;
    height: 300rpx;
}

.chart-canvas {
    width: 100%;
    height: 100%;
}

/* Details Card */
.details-card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 0 32rpx 32rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.details-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 32rpx;
}

.details-list {
    display: flex;
    flex-direction: column;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #F0F0F0;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-name {
    font-size: 28rpx;
    color: #333333;
}

.detail-formula {
    font-size: 24rpx;
    color: #999999;
    margin-top: 8rpx;
}

/* Suggestions Card */
.suggestions-card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 0 32rpx 32rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.suggestions-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 32rpx;
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
}

.suggestion-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #F8F8F8;
    border-radius: 16rpx;
    padding: 32rpx 0;
}

.suggestion-icon {
    width: 80rpx;
    height: 80rpx;
    background-color: rgba(26, 115, 232, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16rpx;
}

.suggestion-text {
    font-size: 24rpx;
    color: #333333;
}

/* Bottom Tab Bar */
.bottom-tab-bar {
    display: flex;
    height: 100rpx;
    background-color: #FFFFFF;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tab-item.active .tab-text {
    color: #1A73E8;
}

.tab-text {
    font-size: 20rpx;
    color: #999999;
    margin-top: 8rpx;
}
</style>