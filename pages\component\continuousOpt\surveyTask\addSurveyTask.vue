<template>
    <view class="container">
      <uv-form labelPosition="top" label-width="200" :model="form">
        <!-- 任务基础信息 -->
        <view class="section">
          <uv-form-item label="任务标题">
            <uv-input
              v-model="form.title"
              placeholder="请输入任务标题"
              clearable
              maxlength="50"
              show-word-limit
              :disabled="isDetailMode"
            />
          </uv-form-item>
          <uv-form-item label="调查类型">
            <picker-input
              v-model="form.investigateType"
              :columns="[investigateTypes]"
              placeholder="请选择调查类型"
              display-key="label"
              value-key="value"
              :disabled="isDetailMode"
            />
          </uv-form-item>
          <uv-form-item label="来源部门">
            <picker-input
              v-model="form.investigateSource"
              :columns="[departments]"
              placeholder="请选择来源部门"
              display-key="label"
              value-key="value"
              :disabled="isDetailMode"
            />
          </uv-form-item>
          <uv-form-item label="风险等级">
            <FilterTags 
              v-model="form.level"
              :tags="riskLevels"
              :fixed="false"
              display-key="name"
              value-key="id"
              :disabled="isDetailMode"
            />
          </uv-form-item>
        </view>

        <!-- 负责人 & 协调人 -->
        <view class="section">
          <uv-form-item label="负责人">
            <employee-picker
              v-model="form.dutyEmployeeId"
              placeholder="请选择负责人"
              @confirm="onDutyEmployeeSelect"
              :disabled="isDetailMode"
            />
          </uv-form-item>
          <uv-form-item label="协调人 (可选)">
            <employee-picker
              v-model="form.coordinateEmployeeId"
              placeholder="请选择协调人"
              @confirm="onCoordinateEmployeeSelect"
              :disabled="isDetailMode"
            />
          </uv-form-item>
        </view>
  
        <!-- 调查目标说明 -->
        <view class="section">
          <view class="section-title">调查目标说明</view>
          <uv-form-item label="调查背景">
            <uv-input
              v-model="form.investigateBackground"
              type="textarea"
              :rows="3"
              placeholder="请输入调查背景"
              resize="none"
              :disabled="isDetailMode"
            />
          </uv-form-item>
          <uv-form-item label="调查目标">
            <uv-input
              v-model="form.investigateTarget"
              type="textarea"
              :rows="3"
              placeholder="请输入调查目标"
              resize="none"
              :disabled="isDetailMode"
            />
          </uv-form-item>
          <uv-form-item label="涉及范围">
            <uv-input
              v-model="form.investigateRange"
              type="textarea"
              :rows="3"
              placeholder="请输入涉及范围"
              resize="none"
              :disabled="isDetailMode"
            />
          </uv-form-item>
        </view>

        <!-- 涉及人员补充说明 -->
        <view class="section" v-if="false">
          <view class="section-header">
            <view class="section-title">涉及人员补充说明</view>
            <uv-button size="small" plain @click="addInvolveItem" v-if="!isDetailMode">
               添加
            </uv-button>
          </view>
          <view v-if="!form.involveList.length" class="empty">暂无涉及人员</view>
          <view v-else>
            <view
              v-for="(item, idx) in form.involveList"
              :key="idx"
              class="involve-item"
            >
              <uv-form-item label="类型">
                <picker-input
                  v-model="item.type"
                  :columns="[involveTypes]"
                  placeholder="请选择类型"
                  display-key="label"
                  value-key="value"
                  :disabled="isDetailMode"
                  @confirm="onTypeChange(item, $event)"
                />
              </uv-form-item>
              <uv-form-item label="人员">
                <picker-input
                  v-model="item.nameId"
                  :columns="[getInvolveOptions(item.type)]"
                  :placeholder="getInvolvePlaceholder(item.type)"
                  display-key="label"
                  value-key="value"
                  :disabled="isDetailMode"
                  @confirm="onInvolveChange(item, $event)"
                />
              </uv-form-item>
              <uv-form-item label="备注">
                <uv-input v-model="item.remark" placeholder="请输入备注" :disabled="isDetailMode" />
              </uv-form-item>
              <view class="delete-btn" @click="removeInvolveItem(idx)" v-if="!isDetailMode">
                 删除
              </view>
            </view>
          </view>
        </view>
  
        <!-- 底部按钮 -->
        <view class="footer" v-if="!isDetailMode">
          <uv-button type="primary" block @click="submitForm">
            发起调查任务
          </uv-button>
        </view>
      </uv-form>
    </view>
  </template>
  
  <script setup>
  import { reactive, ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import PickerInput from '@/components/picker-input.vue'
import EmployeePicker from '@/components/employee-picker.vue'
import FilterTags from '@/components/filterTags.vue'
import taskApi from '@/api/violation/task.js'
import codeApi from '@/api/common/index.js'
import orgApi from '@/api/org/index.js'
import { useUserStore } from '@/store/pinia.js'

  // 页面参数
  const pageType = ref('add') // 'add' 或 'detail'
  const taskId = ref(null)
  const isDetailMode = ref(false)
  
  // 获取用户store
  const userStore = useUserStore()
  
  // 员工列表数据
  const employeeList = ref([])
  
  // 计算属性：获取部门和员工数据
  const departmentOptions = computed(() => {
    const departments = userStore.getDepartments() || []
    return departments.map(dept => ({
      label: dept.name,
      value: dept.id
    }))
  })
  
  const employeeOptions = computed(() => {
    return employeeList.value.map(emp => ({
      label: emp.name,
      value: emp.id
    }))
  })

  // 页面加载时获取参数
  onLoad((options) => {
    if (options.type) {
      pageType.value = options.type
      isDetailMode.value = options.type === 'detail'
    }
    if (options.id) {
      taskId.value = options.id
    }
  })

  onMounted(async () => {
    try {
      const type = await codeApi.getCode('INVESTIGATE_TASK');
      form.investigateCode = type;
      
      // 确保部门数据已加载
      await ensureDataLoaded()
      
      // 加载员工列表
      await loadEmployeeList()
      
      // 如果是详情模式，加载任务数据
      if (isDetailMode.value && taskId.value) {
        await loadTaskDetail()
      }
    } catch (error) {
      console.error('初始化失败:', error)
    }
  })
  
  // 加载员工列表
  const loadEmployeeList = async () => {
    try {
      const result = await orgApi.getEmpList()
      if (result && result.content) {
        employeeList.value = result.content
      }
    } catch (error) {
      console.error('加载员工列表失败:', error)
    }
  }
  
  // 确保部门数据已加载
  const ensureDataLoaded = async () => {
    // 如果store中没有数据，这里可以触发数据加载
    // 通常这些数据应该在首页或登录后就已经加载了
    if (!userStore.getDepartments()) {
      console.warn('部门数据未加载，请确保在首页已正确加载数据')
    }
  }

  // 加载任务详情
  const loadTaskDetail = async () => {
    try {
      uni.showLoading({ title: '加载中...' })
      const result = await taskApi.getInvestigateTaskDetail(taskId.value)
      if (result) {
        // 填充表单数据
        Object.assign(form, {
          title: result.title || '',
          investigateType: result.investigateType || '',
          investigateSource: result.investigateSource || '',
          level: result.level || 'MIDDLE',
          dutyEmployeeId: result.dutyEmployeeId || '',
          coordinateEmployeeId: result.coordinateEmployeeId || '',
          investigateBackground: result.investigateBackground || '',
          investigateTarget: result.investigateTarget || '',
          investigateRange: result.investigateRange || '',
          involveList: (result.involveList || []).map(item => ({
            type: item.involve || '',
            name: getInvolveNameById(item.involve, item.involveId),
            nameId: item.involveId || '',
            remark: item.remark || ''
          })),
          investigateCode: result.investigateCode || form.investigateCode
        })
      }
      uni.hideLoading()
    } catch (error) {
      uni.hideLoading()
      console.error('加载任务详情失败:', error)
      uni.showToast({ title: '加载失败', icon: 'none' })
    }
  }
  const form = reactive({
    title: '',
    investigateType: '',
    investigateSource: '',
    level: 'MIDDLE',
    dutyEmployeeId: '',
    coordinateEmployeeId: '',
    investigateBackground: '',
    investigateTarget: '',
    investigateRange: '',
    involveList: [],
    investigateCode: '',
  })
  
  const investigateTypes = [
    { label: '广告合规', value: 'ADVERTISING_COMPLIANCE' },
    { label: '供应商管理', value: 'SUPPLIER_MANAGEMENT' },
    { label: '员工培训', value: 'EMPLOYEE_TRAINING' },
    { label: '财务审计', value: 'FINANCIAL_AUDITING' }
  ]
  
  const departments = [
    { label: '市场部', value: 'MARKETING' },
    { label: '采购部', value: 'PROCUREMENT' },
    { label: '人力资源部', value: 'HR' },
    { label: '财务部', value: 'FINANCE' }
  ]
  
  const involveTypes = [
    { label: '员工', value: 1 },
    { label: '部门', value: 2 }
  ]
  
  const riskLevels = [
    { name: '低', id: 'LOW' },
    { name: '中', id: 'MIDDLE' },
    { name: '高', id: 'HIGH' }
  ]
  
  function onDutyEmployeeSelect(employee) {
    console.log('选择负责人：', employee)
  }
  
  function onCoordinateEmployeeSelect(employee) {
    console.log('选择协调人：', employee)
  }
  
  function addInvolveItem() {
    form.involveList.push({ type: '', name: '', nameId: '', remark: '' })
  }
  
  function removeInvolveItem(index) {
    form.involveList.splice(index, 1)
  }
  
  // 根据类型获取涉及人员的选项
  function getInvolveOptions(type) {
    if (type === 1) { // 员工
      return employeeOptions.value
    } else if (type === 2) { // 部门
      return departmentOptions.value
    }
    return []
  }
  
  // 根据类型获取占位符
  function getInvolvePlaceholder(type) {
    if (type === 1) {
      return '请选择员工'
    } else if (type === 2) {
      return '请选择部门'
    }
    return '请先选择类型'
  }
  
  // 处理类型变化
  function onTypeChange(item, selectedType) {
    item.type = selectedType
    // 清空人员选择
    item.name = ''
    item.nameId = ''
  }
  
  // 根据ID获取涉及人员的名称（用于详情回填）
  function getInvolveNameById(type, id) {
    if (!type || !id) return ''
    
    const options = getInvolveOptions(type)
    const option = options.find(opt => opt.value === id)
    return option ? option.label : ''
  }
  
  // 处理涉及人员选择变化
  function onInvolveChange(item, selectedOption) {
    // confirm事件直接传递选中的对象
    console.log('selectedOption', selectedOption)
    if (selectedOption) {
      item.nameId = selectedOption.value
      item.name = selectedOption.label
    }
  }
  
  async function submitForm() {
    // 表单验证
    if (!form.title) {
      uni.showToast({ title: '请输入任务标题', icon: 'none' })
      return
    }
    if (!form.investigateType) {
      uni.showToast({ title: '请选择调查类型', icon: 'none' })
      return
    }
    if (!form.investigateSource) {
      uni.showToast({ title: '请选择来源部门', icon: 'none' })
      return
    }
    if (!form.dutyEmployeeId) {
      uni.showToast({ title: '请选择负责人', icon: 'none' })
      return
    }
    if (!form.investigateBackground) {
      uni.showToast({ title: '请输入调查背景', icon: 'none' })
      return
    }
    if (!form.investigateTarget) {
      uni.showToast({ title: '请输入调查目标', icon: 'none' })
      return
    }
    if (!form.investigateRange) {
      uni.showToast({ title: '请输入涉及范围', icon: 'none' })
      return
    }
    
    // 验证涉及人员列表
    console.log(form.involveList, 'pppppppppp')
    for (let i = 0; i < form.involveList.length; i++) {
      const item = form.involveList[i]
      if (!item.type) {
        uni.showToast({ title: `请选择第${i + 1}项涉及人员的类型`, icon: 'none' })
        return
      }
      if (!item.nameId) {
        uni.showToast({ title: `请选择第${i + 1}项涉及人员`, icon: 'none' })
        return
      }
    }

    // 构建提交数据
    const submitData = {
      title: form.title,
      investigateType: form.investigateType,
      investigateSource: form.investigateSource,
      level: form.level,
      dutyEmployeeId: form.dutyEmployeeId,
      coordinateEmployeeId: form.coordinateEmployeeId || null,
      investigateBackground: form.investigateBackground,
      investigateTarget: form.investigateTarget,
      investigateRange: form.investigateRange,
      investigateCode: form.investigateCode,
      involveList: form.involveList.map(item => ({
        involve: item.type,
        involveId: item.nameId || 0, // 使用选择的人员或部门ID
        remark: item.remark
      }))
    }
    try {
      uni.showLoading({ title: '提交中...' })
      const result = await taskApi.createInvestigateTask(submitData)
      uni.hideLoading()
      
      if (result && result.id) {
        uni.showToast({ 
          title: '调查任务创建成功', 
          icon: 'success',
          duration: 1500
        })
        
        // 延迟返回上一级页面，并传递problemInvestigateId
        setTimeout(() => {
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          prevPage.$vm.problemInvestigateId = result.id
          uni.navigateBack()
        }, 1500)
      }
    } catch (error) {
      uni.hideLoading()
      console.error('创建调查任务失败：', error)
      uni.showToast({ 
        title: '创建失败，请重试', 
        icon: 'none' 
      })
    }
  }
  
  </script>
  
  <style scoped>
  .container {
    background: #f0f2f5;
    padding: 16px;
    min-height: 100vh;
  }
  .section {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
  }
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
  }
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  .involve-item {
    position: relative;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fafafa;
  }
  .delete-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px;
    cursor: pointer;
    color: tomato;
  }
  .footer {
    padding: 16px 0;
    background: #fff;
  }
  .empty {
    text-align: center;
    color: #999;
    padding: 32px 0;
  }
  </style>
  