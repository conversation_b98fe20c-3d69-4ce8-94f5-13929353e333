<template>
  <view class="page">
    <!-- 考试信息区 -->
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <uni-load-more status="loading"
          :content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载中...' }"></uni-load-more>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!examDetail.examId" class="empty-container">
        <view class="empty-text">暂无考试详情</view>
        <button class="retry-btn" @click="loadExamDetail">重新加载</button>
      </view>

      <view class="exam-card" v-else-if="examDetail.examId">
        <view class="exam-title">{{ examDetail.examName }}</view>

        <view class="exam-desc">
          <rich-text :nodes="examDesc"></rich-text>
        </view>

        <view class="exam-info">
          <view class="info-item">
            <text class="info-label">考试时长</text>
            <text class="info-value">{{ examDetail.examDuration }}分钟</text>
          </view>
          <view class="info-item">
            <text class="info-label">题目数量</text>
            <text class="info-value">{{ examDetail.questionCount }}题</text>
          </view>
        </view>
        <view class="exam-info">
          <view class="info-item">
            <text class="info-label">总分</text>
            <text class="info-value">{{ examDetail.totalScore }}分</text>
          </view>
          <view class="info-item">
            <text class="info-label">及格分</text>
            <text class="info-value">{{ examDetail.passScore }}分</text>
          </view>
        </view>

        <view class="exam-status" :class="statusClass">
          {{ examStatusText }}
        </view>
      </view>

      <!-- 考试须知模块 -->
      <view class="notice-card" v-if="examDetail.examNotice || examDetail.examId">
        <view class="notice-title">考试须知</view>

        <!-- 显示接口返回的考试须知 -->
        <view v-if="examDetail.examNotice" class="notice-content">
          <rich-text :nodes="examDetail.examNotice"></rich-text>
        </view>

        <!-- 默认考试信息 -->
        <view class="notice-item">
          <uni-icons type="checkbox-filled" size="16" color="#666"></uni-icons>
          <text class="notice-text">考试时长：{{ examDetail.examDuration }}分钟</text>
        </view>

        <view class="notice-item">
          <uni-icons type="checkbox-filled" size="16" color="#666"></uni-icons>
          <text class="notice-text">题目数量：{{ examDetail.questionCount }}题</text>
        </view>

        <view class="notice-item">
          <uni-icons type="checkbox-filled" size="16" color="#666"></uni-icons>
          <text class="notice-text">每题分值：{{ examDetail.scorePerQuestion }}分</text>
        </view>

        <view class="notice-item">
          <uni-icons type="checkbox-filled" size="16" color="#666"></uni-icons>
          <text class="notice-text">及格分数：{{ examDetail.passScore }}分</text>
        </view>

        <view class="notice-item" v-if="examDetail.hasParticipated && examDetail.latestScore !== null">
          <uni-icons :type="examDetail.latestIsPassed ? 'checkmarkempty' : 'closempty'" size="16"
            :color="examDetail.latestIsPassed ? '#52c41a' : '#f5222d'"></uni-icons>
          <text class="notice-text">最近成绩：{{ examDetail.latestScore }}分 ({{ examDetail.latestIsPassed ? '合格' : '不合格'
            }})</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-bar">
      <!-- 如果latestExamRecordId为null，显示开始考试按钮 v-if="!examDetail.latestExamRecordId"  -->
      <uv-button v-if="!examDetail.latestExamRecordId"
                 type="primary" 
                 size="large" 
                 shape="round"
                 customStyle="width: 100%; margin: 0;"
                 @click="handleStartExam">
        开始考试
      </uv-button>

      <!-- 如果latestExamRecordId存在，显示错题解析按钮 -->
      <uv-button v-if="examDetail.latestExamRecordId" 
                 type="warning" 
                 size="large" 
                 shape="round"
                 customStyle="width: 100%; margin: 0;"
                 @click="handleWrongAnalysis">
        错题解析
      </uv-button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import {onLoad, onShow } from '@dcloudio/uni-app';
import assessApi from './assess.js';

// 页面参数
const examId = ref('');
const examRecordId = ref('');

// 考试详情数据
const examDetail = ref({});
const loading = ref(false);

// 考试状态映射
const getExamStatus = (latestExamStatus, hasParticipated) => {
  if (!hasParticipated) return 0; // 未开始
  if (latestExamStatus === 'IN_PROGRESS') return 1; // 进行中
  if (latestExamStatus === 'COMPLETED') return 2; // 已完成
  return 0;
};

// 动态计算的状态
const examStatus = computed(() => {
  return getExamStatus(examDetail.value.latestExamStatus, examDetail.value.hasParticipated);
});

// 动态考试描述
const examDesc = computed(() => {
  return examDetail.value.examDescription || '暂无考试描述';
});

const examStatusText = computed(() => {
  switch (examStatus.value) {
    case 0: return '未开始';
    case 1: return '进行中';
    case 2: return '已完成';
    default: return '';
  }
});

const statusClass = computed(() => {
  switch (examStatus.value) {
    case 0: return 'status-not-started';
    case 1: return 'status-in-progress';
    case 2: return 'status-completed';
    default: return '';
  }
});

const actionText = computed(() => {
  switch (examStatus.value) {
    case 0: return '开始考试';
    case 1: return '继续答题';
    case 2: return '查看成绩';
    default: return '';
  }
});

const handleBack = () => {
  uni.navigateBack();
};

// 处理开始考试
const handleStartExam = () => {
  if (examId.value) {
    uni.navigateTo({
      url: `/pages/component/study/examPage?examId=${examId.value}&examDuration=${examDetail.value.examDuration}`
    });
  } else {
    uni.showToast({ title: '缺少考试ID', icon: 'none' });
  }
};

// 处理错题解析
const handleWrongAnalysis = () => {
  if (examDetail.value.latestExamRecordId) {
    uni.navigateTo({
      url: `/pages/component/study/incorrect?examRecordId=${examDetail.value.latestExamRecordId}`
    });
  } else {
    uni.showToast({
      title: '缺少考试记录ID',
      icon: 'none'
    });
  }
};

// 获取考试详情
const loadExamDetail = async () => {
  if (!examId.value) {
    uni.showToast({
      title: '缺少考试ID',
      icon: 'none'
    });
    return;
  }

  uni.showLoading({
    title: '加载中...',
    mask: true
  });

  try {
    const response = await assessApi.getExamDetail(examId.value);
    if (response) {
      examDetail.value = response;
      console.log('考试详情:', response);
    }
  } catch (error) {
    console.error('获取考试详情失败:', error);
    uni.showToast({
      title: '获取考试详情失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 页面初始化
onLoad((options) => {
  if (options.examId) {
    examId.value = options.examId;
  }

  if (options.examRecordId) {
    examRecordId.value = options.examRecordId;
  }
});

// 页面显示时重新加载详情
onShow(() => {
  // 每次进入页面都重新加载考试详情
  loadExamDetail();
});

</script>

<style>
page {
  height: 100%;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}


/* 内容区域 */
.content {
  flex: 1;
  padding: 32rpx;
  overflow: auto;
}

/* 考试卡片样式 */
.exam-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.exam-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.exam-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.exam-time {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.time-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 12rpx;
}

.exam-info {
  display: flex;
  margin-bottom: 24rpx;
}

.info-item {
  flex: 1;
}

.info-label {
  font-size: 26rpx;
  color: #999;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-left: 8rpx;
}

.exam-status {
  display: inline-block;
  padding: 8rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
}

.status-not-started {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-in-progress {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.exam-repeat {
  display: flex;
  align-items: center;
}

.repeat-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 考试须知样式 */
.notice-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.notice-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.notice-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

.notice-content {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #1890ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 操作按钮样式 */
.action-bar {
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  background-color: #1890ff;
  border: none;
}

.action-btn.status-not-started {
  background-color: #1890ff;
}

.action-btn.status-in-progress {
  background-color: #fa8c16;
}

.action-btn.status-completed {
  background-color: #52c41a;
}

.analysis-btn {
  flex: 0 0 200rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #1890ff;
  background-color: #fff;
  border: 2rpx solid #1890ff;
}
</style>
