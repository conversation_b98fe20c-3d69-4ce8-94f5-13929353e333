<template>
    <view class="account-security-container">
        <view class="function-container">
            <view class="function-title">常见问题</view>
            <view class="function-list">
                <view class="function-item" @click="goToQA">
                    <text class="function-name">FQA列表</text>
                    <uni-icons type="right" size="16" color="#999"></uni-icons>
                </view>
            </view>
        </view>

        <!-- <view class="function-container">
            <view class="function-title">在线客服</view>
            <view class="function-list">
                <view class="function-item" @click="goToFeedback">
                    <text class="function-name">点击客服按钮进入会话</text>
                    <uni-icons type="right" size="16" color="#999"></uni-icons>
                </view>
            </view>
        </view> -->

        <view class="function-container">
            <view class="function-title">意见反馈</view>
            <view class="function-list">
                <view class="function-item" @click="goToFeedback">
                    <text class="function-name">点击进入意见反馈列表</text>
                    <uni-icons type="right" size="16" color="#999"></uni-icons>
                </view>
            </view>
        </view>
    </view>
</template>
<script setup>
  const goToQA = ()=>{
    uni.switchTab({
        url: '/pages/qa/qa'
    });
  }
  
  const goToFeedback = ()=>{
   uni.navigateTo({
        url: '/pages/component/profile/suggestions/index'
    });
  }

</script>
<style lang="scss" scoped>
  .account-security-container {
    background-color: #f8f8f8;
    height: 100vh;
    .function-container{
        padding: 0 32rpx;
    }
    .function-title{
        padding: 20rpx 0;
    }
    .function-list {
        display: flex;
        flex-direction: column;
        gap: 8rpx;
    }

    .function-item {
        height: 112rpx;
        background-color: #fff;
        border-radius: 16rpx;
        padding: 0 32rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    }

    .function-name {
        flex: 1;
        margin-left: 32rpx;
        font-size: 32rpx;
        color: #333;
        .function-tag {
            font-size: 20rpx;
            color: #999;
        }
    }
  }
</style>
