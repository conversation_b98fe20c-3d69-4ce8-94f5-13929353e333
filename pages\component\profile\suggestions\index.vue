<template>
	<view class="lists-container">
		<z-paging ref="paging" :auto="false" v-model="dataList" @query="queryList" :loading="loading">
			<template #top>
				<header-bar title="意见反馈" shape="circle" prefixIcon="search" clearable :fixed="false"
					v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click.stop="searchClick">
								<uni-icons type="search" size="24" color="#666666"></uni-icons>
							</view>
                         <view @click="handleAdd" class="nav-btn">
								<text class="btn-text">＋新增</text>
								<!-- <uv-button @click="handleAdd" type="primary" :plain="true" text="+新增"></uv-button> -->
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags v-model="tagValue" :fixed="false" :tags="tags" valueKey="value"
					@change="handleTagChange" />
			</template>
			<view class="suggestion-list pl-32 pr-32">
				<view class="list-item" v-for="(item, index) in dataList" :key="index"
					@click="viewDetail(item)">
					<view class="item-content">
						<text class="item-title">{{ item.detail || '暂无详情' }}</text>
						<view class="item-info">
							<text class="info-text" :class="getAnonymousClass(item.isAnonymous)">
								{{ getAnonymousText(item.isAnonymous) }}
							</text>
							<text class="info-tag" style="margin-left: 20rpx;">{{ item.createdBy || '匿名用户' }}</text>
							<text class="info-tag" style="margin-left: 20rpx;">{{ formatDate(item.createdAt) }}</text>
						</view>
						<view v-if="item.contactWay" class="contact-info">
							<text class="contact-text">联系方式：{{ item.contactWay }}</text>
						</view>
						<view v-if="item.attachments && item.attachments.length > 0" class="attachment-info">
							<uni-icons type="paperclip" size="16" color="#999"></uni-icons>
							<text class="attachment-text">{{ item.attachments.length }}个附件</text>
						</view>
					</view>
					<uni-icons type="right" size="16" color="#999" />
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import {
	ref,
	nextTick,
	onMounted
} from 'vue';
import {
	useUserStore
} from '@/store/pinia.js';
import headerBar from '@/components/headerBar.vue';
import filterTags from '@/components/filterTags.vue'
import suggestionsApi from './index.js'

const loading = ref(false)
const userStore = useUserStore();
const tagValue = ref(null);
const tags = ref([{
	name: '全部',
	value: null
}, {
	name: '实名',
	value: 0
}, {
	name: '匿名',
	value: 1
}]);

// 跳转到详情页面
const viewDetail = (item) => {
	console.log('查看详情:', item);
	// 这里可以跳转到详情页面或显示详情弹窗
	uni.showModal({
		title: '反馈详情',
		content: item.detail || '暂无详情',
		showCancel: false
	});
}

const handleTagChange = (index) => {
	reload();
}

function reload() {
	nextTick(() => {
		paging?.value.reload();
	})
}

const searchValue = ref("") //
const paging = ref(null)
const dataList = ref([])

function handleAdd() { 
    uni.navigateTo({
        url: '/pages/component/profile/suggestions/add',
    })
}
function searchClick() {
	reload();
}

function search() {
	reload();
}

onMounted(async () => {
	reload();
});

// 获取匿名状态文本
function getAnonymousText(isAnonymous) {
	return isAnonymous === 1 ? '匿名' : '实名';
}

// 获取匿名状态样式类名
function getAnonymousClass(isAnonymous) {
	return isAnonymous === 1 ? 'type-anonymous' : 'type-real-name';
}

// 格式化日期
function formatDate(dateObj) {
	if (!dateObj) return '';
	
	// 处理时间戳对象
	if (dateObj.seconds) {
		const date = new Date(dateObj.seconds * 1000);
		return date.toLocaleDateString('zh-CN');
	}
	
	// 处理普通日期字符串
	if (typeof dateObj === 'string') {
		const date = new Date(dateObj);
		return date.toLocaleDateString('zh-CN');
	}
	
	return '';
}

// 列表查询
const queryList = (pageNo, pageSize) => {
	uni.showLoading({
		title: '加载中...',
		mask: true
	});
	
	var params = {
		page: pageNo - 1,
		size: pageSize
	}

	suggestionsApi.querySuggestions(params).then((res) => {
		let arr = res.content || []
		
		// 根据匿名状态过滤
		if (tagValue.value !== null) {
			arr = arr.filter(item => item.isAnonymous === tagValue.value);
		}
		
		// 根据搜索关键词过滤
		if (searchValue.value) {
			arr = arr.filter(item => 
				item.detail && 
				item.detail.toLowerCase().includes(searchValue.value.toLowerCase())
			);
		}
		
		console.log('建议列表', arr)
		paging.value.complete(arr);
		uni.hideLoading();
	}).catch((err) => {
		console.error('获取建议列表失败:', err);
		paging.value.complete(false);
		uni.hideLoading();
	})
}

// 暴露变量供其他页面访问
defineExpose({
	reload
});
</script>

<style lang="scss" scoped>
@import '/static/css/nav.scss';
.lists-container {
	/* Content Styles */
	.suggestion-list {
		display: flex;
		flex-direction: column;
		margin-top: 20rpx;
	}

	.list-item {
		background-color: white;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		display: flex;
		margin-bottom: 16rpx;
		justify-content: space-between;
		align-items: center;
	}

	.item-content {
		flex: 1;
	}

	.item-title {
		font-size: 32rpx;
		font-weight: normal;
		color: #1a1a1a;
		margin-bottom: 8rpx;
		display: block;
		line-height: 1.4;
		/* 限制显示行数 */
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.item-info {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		margin-bottom: 8rpx;
	}

	.info-text {
		display: inline-block;
		padding: 6rpx 16rpx;
		color: white;
		font-weight: normal;
		border-radius: 6rpx;
		text-align: center;
		font-size: 24rpx;
	}

	/* 实名样式 - 蓝色背景 */
	.info-text.type-real-name {
		background-color: rgb(230, 247, 255);
		color: #1890ff;
	}

	/* 匿名样式 - 橙色背景 */
	.info-text.type-anonymous {
		background-color: rgb(255, 245, 230);
		color: #fa8c16;
	}

	.info-tag {
		font-size: 24rpx;
		color: $uni-text-color-grey;
	}

	.contact-info {
		margin-top: 8rpx;
	}

	.contact-text {
		font-size: 24rpx;
		color: #666;
	}

	.attachment-info {
		display: flex;
		align-items: center;
		margin-top: 8rpx;
	}

	.attachment-text {
		font-size: 24rpx;
		color: #999;
		margin-left: 8rpx;
	}
}
</style>