import myHttp from '@/utils/request'
import {
	config
} from '@/config'

function request(url, params, method) {
	return myHttp({
		url: url,
		method: method ? method : 'POST',
		data: params
	})
}
const services = `/services/whiskerguardcontractservice/api/`
//api集合
const home = {
	// 字典信息管理
	dict(params) {
		let ser = `/services/whiskerguardregulatoryservice/api/`
				return request(
					`${ser}dictionary/all`,
					params, 'GET')
	},



	// 合同审查
	contractReview(params, key) {
		switch (key) {
			case 'info':
			// return request(`/setting/info`, params)
			default:
				return request(
					`${services}contract/reviews/page?page=${params.page}&size=${params.size}`,
					params, 'GET')
		}
	},
	contractReviewDetail(params) {
		return request(
			`${services}contract/reviews/${params.id}`,
			params, 'GET')
	},

	// 合规审查管理
	complianceReview(params, key) {
		switch (key) {
			case 'info':
			// return request(`/setting/info`, params)
			default: //分页获取合规审核记录
				return request(
					`${services}compliance/reviews/page?page=${params.page}&size=${params.size}`,
					params,
					// 'GET'
				)
		}
	},


	// 合规风险主数据管理接口
	complianceRiskMains(params, key) {
		let baseUrl = `/services/compliancelistservice`
		switch (key) {
			case 'info':
				return request(
					`${baseUrl}/api/compliance/risk/list/${params.id}`,
					{}, 'get')
		   case 'update':
		   return request(
					`${baseUrl}/api/compliance/risk/list/update`,
					params, 'put')
			default:
				return request(
					`${baseUrl}/api/compliance/risk/list/page`,
					params, 'post')
		}
	},

	// 业务流程主数据管理接口
	complianceRiskProcess(params, key) {
		let ser = `/services/compliancelistservice/api/`
		switch (key) {
			case 'info': //获取单个业务流程主数据
				return request(
					`${ser}biz/process/lists/${params.id}`,
					{},
					'GET'
				)
			case 'update': //修改业务流程主数据
				return request(
					`${ser}biz/process/lists/update`,
					params,
					'PUT'
				)
			default: //分页获取业务流程主数据列表
				return request(
					`${ser}v2/biz/process/list/page`,
					params,
					'post'
				)
		}
	},
	// 首页关键词搜索
	homeSearch(params) {
		return request(`/services/whiskerguardregulatoryservice/api/laws/regulations/all/search`,
			params,
			'get'
		)
	},

}
export default home