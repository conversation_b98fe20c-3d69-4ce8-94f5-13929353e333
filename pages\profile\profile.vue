<template>
	<view class="profile-container">
		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 用户信息卡片 -->
			<view class="user-card">
				<view class="avatar-container">
					<image class="avatar"
						:src="avatarImg"
						mode="aspectFill"></image>
				</view>
				<view class="user-info">
					<view class="name-row">
						<text class="name" :title="userStore.userDetail?.realName || ''">{{ displayUserName }}</text>
						<text class="separator">·</text>
						<view class="role-container">
						<text class="job" v-if="!showAllRoles">
							{{ userStore.userDetail.roleList && userStore.userDetail.roleList.length > 0 ? userStore.userDetail.roleList[0].name : '暂无角色' }}
						</text>
						<text class="job" v-if="showAllRoles">
							{{ userStore.userDetail.roleList && userStore.userDetail.roleList.length > 0 ? userStore.userDetail.roleList.map(role => role.name).join('、') : '暂无角色' }}
						</text>
						<text class="more-btn" v-if="userStore.userDetail.roleList && userStore.userDetail.roleList.length > 1 && !showAllRoles" @click="toggleRoles">更多</text>
						<text class="more-btn" v-if="showAllRoles" @click="toggleRoles">收起</text>
					</view>
					</view>
					<view class="department-container">
						<view v-if="!showAllDepartments" class="department-single">
							<text class="department-text">{{ userStore.userDetail.orgUnitList && userStore.userDetail.orgUnitList.length > 0 ? userStore.userDetail.orgUnitList[0].name : '暂无部门' }}</text>
							<text v-if="userStore.userDetail.orgUnitList && userStore.userDetail.orgUnitList.length > 1"
								  class="more-btn"
								  @click="toggleDepartments">更多</text>
						</view>
						<view v-else class="department-multiple">
							<text v-for="(dept, index) in userStore.userDetail.orgUnitList" :key="index" class="department-text">
								{{ dept.name }}
							</text>
							<text class="more-btn" @click="toggleDepartments">收起</text>
						</view>
					</view>
					<view class="department-row">
						<text>{{ userStore.userDetail.code }}</text>
					</view>
					<view class="button-group">
						<button @click='editInfo' type="primary" size="mini">编辑资料</button>
						<button @click='switchAccount' size="mini" plain style="border-color:#CCC;">切换账号</button>
					</view>
				</view>
			</view>

			<!-- 功能列表 -->
			<view class="function-list">
				<view class="function-item" @click="goAccountSecurity">
					<uni-icons type="auth" size="24" color="#1A73E8"></uni-icons>
					<text class="function-name">账号与安全</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
				<view class="function-item" @click="goToHelp">
					<uni-icons type="help" size="24" color="#1A73E8"></uni-icons>
					<text class="function-name">帮助与反馈</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>

				<view class="function-item" @click="goToAboutUs">
					<uni-icons type="staff" size="24" color="#1A73E8"></uni-icons>
					<text class="function-name">关于我们</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>

				<view class="function-item" @click="goToCollects">
					<uni-icons type="heart" size="24" color="#1A73E8"></uni-icons>
					<text class="function-name">我的收藏</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>

				<!-- 浏览记录 -->
				 <view class="function-item" @click="goToBrowseRecord">
					<uv-icon size="24" color="#1A73E8" name="empty-history"></uv-icon>
					<text class="function-name">浏览记录</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				 </view>
				<view class="function-item" @click="goToMessageAuth">
					<uni-icons type="notification" size="24" color="#1A73E8"></uni-icons>
					<text class="function-name">服务号消息推送授权</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
				
				<view class="function-item" @click="goToMessageUnbind">
					<uni-icons type="notification-filled" size="24" color="#1A73E8"></uni-icons>
					<text class="function-name">服务号消息推送解绑</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>

			<!-- 退出登录按钮 -->
			<view  class="logout-btn" @click="logOut">
				<text class="logout-text">退出登录</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue';
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app'
	import {
		useUserStore
	} from '@/store/pinia.js';
	import https from '../../api/login/login.js';
	import wxApi from './wx.js';
	import uploadApi from '@/api/upload.js'
	const userStore = useUserStore();
	const avatarImg = ref("")
	const showAllRoles = ref(false)
	const showAllDepartments = ref(false)

	// 处理显示的用户名，避免过长
	const displayUserName = computed(() => {
		const realName = userStore.userDetail?.realName
		if (!realName) return ''

		// 在profile页面，可以稍微宽松一些，但仍需要限制
		const maxLength = 10 // profile页面允许更长的名字显示

		if (realName.length > maxLength) {
			return realName.substring(0, maxLength) + '...'
		}
		return realName
	})

	// 切换角色显示状态
	const toggleRoles = () => {
		showAllRoles.value = !showAllRoles.value
	}

	// 切换部门显示状态
	const toggleDepartments = () => {
		showAllDepartments.value = !showAllDepartments.value
	}
	onLoad((e) => {
		getAvatar()
	})
	onShow(() => {
		if(userStore.avatarUpdated){
			getAvatar()
		}
		userStore.avatarUpdated = false
	})
const unbind = async () => {
	// 先判断是否有微信openId
	if (!userStore.userDetail.wechatOpenId) {
		uni.showToast({
			title: '未绑定微信，无需解绑',
			icon: 'none'
		});
		return;
	}
	
	// 二次确认
	uni.showModal({
		title: '确认解绑',
		content: '确定要解除微信绑定吗？解绑后将无法接收微信消息推送。',
		success: async (res) => {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: '解绑中...',
						mask: true
					});
					
					await https.unbindWechat({openId: userStore.userDetail.wechatOpenId});
					
					uni.hideLoading();
					uni.showToast({
						title: '解绑成功',
						icon: 'success'
					});
					
					// 更新用户状态
					userStore.userDetail.wechatOpenId = null;
					
				} catch (error) {
					console.error('解绑失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: '解绑失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	});
}
	const goToMessageAuth = async () => {
		try {
			// 显示加载提示
			uni.showLoading({
				title: '获取授权链接中...',
				mask: true
			});

			// 调用后端接口获取授权URL
			const response = await wxApi.getAuthorize();

			if (response && response.authUrl) {
				// 跳转到微信授权页面
				uni.navigateTo({
					url: `/pages/webview/webview?url=${encodeURIComponent(response.data.authUrl)}`
				});
			} else {
				uni.showToast({
					title: response.message || '获取授权链接失败',
					icon: 'none'
				});
			}

		} catch (error) {
			console.error('微信授权失败:', error);
			uni.showToast({
				title: '授权失败，请重试',
				icon: 'none'
			});
		} finally {
			uni.hideLoading();
		}
	}
	// 用户小程序授权
	const goSubscribe = () =>{
       uni.navigateTo({
			url: '/pages/component/profile/subscribe'
		});
	}

	// 获取头像
	const getAvatar = async () => {
		try {
			if(userStore.userDetail.avatar){
              const res = await uploadApi.getFileUrl(userStore.userDetail.avatar)
			  console.log('头像数据获取成功:', res)
			  avatarImg.value = res
			}else{
			   avatarImg.value = "https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
			}
		} catch (error) {
			  avatarImg.value = "https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
		}
	}

	function editInfo() {
		uni.navigateTo({
			url: '/pages/profile/editInfo'
		});
	}
	const goToAboutUs = () => {
		uni.navigateTo({
			url: '/pages/component/profile/aboutUs'
		});
	};
	const goToCollects = () => {
		uni.navigateTo({
			url: '/pages/component/profile/collects'
		});
	}
	
	const goToBrowseRecord = () => {
		uni.navigateTo({
			url: '/pages/component/profile/browseHistory'
		});
	}
	const goToSuggestions = () => {
		uni.navigateTo({
			url: '/pages/component/profile/suggestions/index'
		});
	}
	const goToHelp = () => {
		uni.navigateTo({
			url: '/pages/component/profile/helpFeedback'
		});
	};

	const goAccountSecurity = () => {
		uni.navigateTo({
			url: '/pages/component/profile/accountSecurity'
		});
	};

	const switchAccount = () => {
		uni.showModal({
			title: '提示',
			content: '是否切换账号',
			success: (res) => {
				if (res.confirm) {
					uni.showLoading({
						title: '切换中...',
						mask: true,
					})
					https.logout(userStore.token).then(res => {
						userStore['token'] = null
						userStore['tenantId'] = null
						userStore['user'] = null
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
							uni.hideLoading()
						}, 1000)
					});
				} else if (res.cancel) {
					// 用户取消，不执行任何操作
				}
			}
		});
	};

	const logOut = () => {
		uni.showModal({
			title: '提示',
			content: '是否退出登录',
			success: (res) => {
				if (res.confirm) {
					uni.showLoading({
						title: '退出中...',
						mask: true,
					})
					https.logout(userStore.token).then(res => {
						userStore['token'] = null
						userStore['tenantId'] = null
						userStore['user'] = null
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
							uni.hideLoading()
						}, 1000)
					});
				} else if (res.cancel) {
					// 用户取消，不执行任何操作
				}
			}
		});
	};
</script>

<style lang="scss" scoped>
	.profile-container {
		height: 100vh;
		background-color: #f8f8f8;

		.content {
			padding: 20rpx 32rpx;
		}

		.user-card {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 32rpx;
			margin-bottom: 48rpx;
			display: flex;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
			min-width: 0; /* 允许卡片内容收缩 */
			overflow: hidden; /* 防止内容溢出 */
		}

		.avatar-container {
			width: 128rpx;
			height: 128rpx;
			border-radius: 50%;
			overflow: hidden;
			margin-right: 32rpx;
			background-color: #eee;
			flex-shrink: 0; /* 防止头像被压缩 */
		}

		.avatar {
			width: 100%;
			height: 100%;
		}

		.user-info {
			flex: 1;
			display: flex;
			flex-direction: column;
		}

		.name-row {
			display: flex;
			align-items: center;
			margin-bottom: 8rpx;
			min-width: 0; /* 允许子元素收缩 */
		}

		.name {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			flex: 1;
			min-width: 0; /* 允许文本收缩 */
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 300rpx; /* 限制最大宽度，为角色信息预留空间 */
		}

		.separator {
			margin: 0 12rpx;
			color: #666;
			flex-shrink: 0; /* 分隔符不压缩 */
		}

		.role-container {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 8rpx;
			flex: 1;
			min-width: 0; /* 允许角色容器收缩 */
		}

		.department-container {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 8rpx;
			margin-bottom: 8rpx;
			width: 100%;
		}

		.department-single,
		.department-multiple {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 8rpx;
			width: 100%;
		}

		.department-text {
			font-size: 28rpx;
			color: #666;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 300rpx; /* 限制部门名称的最大宽度 */
		}

		.job {
			font-size: 28rpx;
			color: #666;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 200rpx; /* 限制角色名称的最大宽度 */
		}

		.more-btn {
			font-size: 24rpx;
			color: #1A73E8;
			padding: 2rpx 8rpx;
			border-radius: 8rpx;
			background-color: rgba(26, 115, 232, 0.1);
			cursor: pointer;
			flex-shrink: 0; /* 防止"更多"按钮被压缩 */
		}

		/* 小屏幕适配 */
		@media screen and (max-width: 375px) {
			.name {
				font-size: 32rpx;
				max-width: 250rpx;
			}

			.job {
				font-size: 26rpx;
				max-width: 150rpx;
			}

			.department-text {
				font-size: 26rpx;
				max-width: 250rpx;
			}
		}

		.department-row {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #666;
			margin-bottom: 20rpx;
		}

		.button-group {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 16rpx;
		}

		.button-group button {
			margin: 0;
			padding: 4rpx 30rpx;
		}

		.function-list {
			display: flex;
			flex-direction: column;
			gap: 8rpx;
		}

		.function-item {
			height: 112rpx;
			background-color: #fff;
			border-radius: 16rpx;
			padding: 0 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		}

		.function-name {
			flex: 1;
			margin-left: 32rpx;
			font-size: 32rpx;
			color: #333;
		}

		.logout-btn {
			height: 112rpx;
			background-color: #fff;
			border-radius: 16rpx;
			margin-top: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		}

		.logout-text {
			font-size: 32rpx;
			font-weight: bold;
			color: #f44336;
		}
	}
</style>