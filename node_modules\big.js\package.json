{"_from": "big.js", "_id": "big.js@6.2.2", "_inBundle": false, "_integrity": "sha512-y/ie+Faknx7sZA5MfGA2xKlu0GDv8RWrXGsmlteyJQ2lvoKv9GBK/fpRMc2qlSoBAgNxrixICFCBefIq8WCQpQ==", "_location": "/big.js", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "big.js", "name": "big.js", "escapedName": "big.js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/big.js/-/big.js-6.2.2.tgz", "_shasum": "be3bb9ac834558b53b099deef2a1d06ac6368e1a", "_spec": "big.js", "_where": "G:\\项目集合\\酒店\\hotel-system-mini-program", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "big.js", "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "engines": {"node": "*"}, "exports": {".": {"import": "./big.mjs", "require": "./big.js"}, "./big.mjs": "./big.mjs", "./big.js": "./big.js", "./package.json": "./package.json"}, "files": ["big.js", "big.mjs"], "funding": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}, "homepage": "https://github.com/MikeMcl/big.js#readme", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "license": "MIT", "main": "big", "module": "big.mjs", "name": "big.js", "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/big.js.git"}, "scripts": {"test": "node ./test/runner.js"}, "version": "6.2.2"}