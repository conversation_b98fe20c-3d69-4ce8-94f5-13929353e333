import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}

const baseUrl = `/services/whiskerguardviolationservice/api/problem/investigate/reports`
const reportUrl = `/services/whiskerguardviolationservice/api/violation/`

export default {
    // 创建新的问题调查报告
    createInvestigateReport(data) {
        return request(baseUrl, data, 'post')
    },
    // 获取调查报告列表
    getInvestigateReportList(investigateId,params) {
        return request(`${baseUrl}/search?page=${params.page}&size=${params.size}`, {
          investigateId: investigateId
        }, 'post')
    },
    // 获取调查报告详情
    getInvestigateReportDetail(id) {
        return request(baseUrl + `/${id}`, {}, 'get')
    },
    // 更新调查报告
    updateInvestigateReport(id, data) {
        return request(baseUrl + `/${id}`, data, 'put')
    },
    // 删除调查报告
    deleteInvestigateReport(id) {
        return request(baseUrl + `/${id}`, {}, 'delete')
    },
    // 根据调查ID获取相关报告
    getReportsByInvestigateId(investigateId) {
        return request(baseUrl + `/by-investigate/${investigateId}`, {}, 'get')
    },

        // 获取列表
    getList(params, page) {
        return request(reportUrl + 'details/user' + `?page=${page.page}&size=${page.size}`, params, 'post')
    },
    // 获取详情
    getDetail(id) {
        return request(reportUrl + `details/${id}`, {}, 'get')
    },
    // 新增
    reportCreate(params) {
        return request(reportUrl + 'details', params, 'post')
    },
}