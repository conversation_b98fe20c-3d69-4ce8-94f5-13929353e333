<template>
    <view class="container">
        <uv-form labelPosition="top" label-width="200" :model="form">
            <!-- 报告基础信息 -->
            <view class="section">
                <uv-form-item label="报告标题">
                    <uv-input v-model="form.title" placeholder="请输入报告标题" clearable maxlength="100" show-word-limit />
                </uv-form-item>
                <!-- <uv-form-item label="调查报告编号">
                    <uv-input v-model="form.reportCode" disabled placeholder="请输入调查编号" clearable maxlength="50"
                        show-word-limit />
                </uv-form-item> -->
                <uv-form-item label="报告类型">
                    <picker-input v-model="form.reportType" :columns="[reportTypes]" placeholder="请选择报告类型"
                        display-key="label" value-key="value" />
                </uv-form-item>
                <uv-form-item label="调查来源">
                    <picker-input v-model="form.investigateSource" :columns="[investigateSources]" placeholder="请选择调查来源"
                        display-key="label" value-key="value" />
                </uv-form-item>
                <uv-form-item label="编制日期">
                    <uv-datetime-picker v-model="form.establishDate" mode="date" placeholder="请选择编制日期" />
                </uv-form-item>
                <uv-form-item label="优先级">
                    <FilterTags v-model="form.level" :tags="levelOptions" :fixed="false" display-key="name"
                        value-key="id" />
                </uv-form-item>
                <uv-form-item label="状态">
                    <picker-input v-model="form.status" :columns="[statusOptions]" placeholder="请选择状态"
                        display-key="label" value-key="value" />
                </uv-form-item>
            </view>

            <!-- 编制信息 -->
            <view class="section">
                <uv-form-item label="编制人">
                    <employee-picker v-model="form.employeeId" placeholder="请选择编制人" @confirm="onEmployeeSelect" />
                </uv-form-item>
                <uv-form-item label="编制部门">
                    <picker-input v-model="form.orgId" :columns="[departments]" placeholder="请选择编制部门"
                        display-key="label" value-key="value" />
                </uv-form-item>
            </view>

            <!-- 报告摘要 -->
            <view class="section">
                <!-- <view class="section-title">报告摘要</view> -->
                <uv-form-item label="摘要">
                    <uv-input v-model="form.summary" type="textarea" :rows="4" placeholder="请输入报告摘要" resize="none"
                        maxlength="500" show-word-limit />
                </uv-form-item>
            </view>

            <!-- 调查详情 -->
            <view class="section">
                <!-- <view class="section-title">调查详情</view> -->
                <uv-form-item label="调查背景">
                    <uv-input v-model="form.investigateBackground" type="textarea" :rows="4" placeholder="请输入调查背景"
                        resize="none" maxlength="1000" show-word-limit />
                </uv-form-item>
                <uv-form-item label="调查方法">
                    <uv-input v-model="form.investigateMethod" type="textarea" :rows="3" placeholder="请输入调查方法"
                        resize="none" maxlength="500" show-word-limit />
                </uv-form-item>
                <uv-form-item label="调查过程">
                    <uv-input v-model="form.investigateProcess" type="textarea" :rows="4" placeholder="请输入调查过程"
                        resize="none" maxlength="1000" show-word-limit />
                </uv-form-item>
                <uv-form-item label="调查发现">
                    <uv-input v-model="form.investigateFound" type="textarea" :rows="4" placeholder="请输入调查发现"
                        resize="none" maxlength="1000" show-word-limit />
                </uv-form-item>
                <uv-form-item label="调查结论">
                    <uv-input v-model="form.investigateConclusion" type="textarea" :rows="4" placeholder="请输入调查结论"
                        resize="none" maxlength="1000" show-word-limit />
                </uv-form-item>
                <uv-form-item label="建议措施">
                    <uv-input v-model="form.recommendMeasure" type="textarea" :rows="4" placeholder="请输入建议措施"
                        resize="none" maxlength="1000" show-word-limit />
                </uv-form-item>
            </view>

            <!-- 附件上传 -->
            <view class="section">
                <view class="section-title">附件上传</view>
                <uv-form-item label="相关附件">
                    <upload-file v-model="attachmentFiles" :limit="10" :size-limit="50"
                        tip-text="支持上传PDF、Word、Excel、图片等格式文件，单个文件不超过50MB"
                        service-name="violation" category-name="report" @upload-success="onAttachmentUploadSuccess"
                        @file-remove="onAttachmentRemove" />
                </uv-form-item>
            </view>

            <!-- 底部按钮 -->
            <view class="footer">
                <uv-button type="primary" block @click="submitForm">
                    提交调查报告
                </uv-button>
            </view>
        </uv-form>
    </view>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import PickerInput from '@/components/picker-input.vue'
import EmployeePicker from '@/components/employee-picker.vue'
import FilterTags from '@/components/filterTags.vue'
import UploadFile from '@/components/uploadFile.vue'
import reportApi from '@/api/violation/report.js'
import codeApi from '@/api/common/index.js'
import getDictData from '@/utils/dict.js'

const form = reactive({
    title: '',
    reportCode: '',
    investigateId: '',
    reportType: '',
    investigateSource: '',
    employeeId: 1,
    orgId: 1,
    establishDate: '',
    level: '',
    summary: '',
    status: '',
    investigateBackground: '',
    investigateMethod: '',
    investigateProcess: '',
    investigateFound: '',
    investigateConclusion: '',
    recommendMeasure: '',
    attachmentList: [],
})

// 防抖控制
const isSubmitting = ref(false)

// 附件文件列表
const attachmentFiles = ref([])

// 报告类型选项
const reportTypes = ref([])

// 调查来源选项
const investigateSources = [
    { label: '市场部', value: 'MARKETING' },
    { label: '采购部', value: 'PROCUREMENT' },
    { label: '人力资源部', value: 'HR' },
    { label: '财务部', value: 'FINANCE' }
]

// 优先级选项
const levelOptions = [
    { name: '低', id: 'LOW' },
    { name: '中', id: 'MIDDLE' },
    { name: '高', id: 'HIGH' }
]

// 状态选项
const statusOptions = [
    { label: '需修改', value: 'MODIFY' },
    { label: '待审查', value: 'PENDING' },
    { label: '发布', value: 'PUBLISHED' },
    { label: '审核中', value: 'REVIEWING' },
    { label: '已撤回', value: 'REVOKE' }
]

// 部门选项
const departments = [
    { label: '市场部', value: 1 },
    { label: '采购部', value: 2 },
    { label: '人力资源部', value: 3 },
    { label: '财务部', value: 4 }
]


// 初始化报告编号
onMounted(async () => {
    try {
        // 获取调查报告编号
        const code = await codeApi.getCode('INVESTIGATE_REPORT')
        form.reportCode = code
        
        // 获取报告类型字典数据
        const dictData = await getDictData('32', null)
        reportTypes.value = dictData.map(item => ({
            label: item.name,
            value: item.value
        }))
    } catch (error) {
        uni.showToast({
            title: '获取编号失败，使用默认编号',
            icon: 'none'
        })
    }
})

// 获取页面参数
onLoad((options) => {
    if (options && options.id) {
        form.investigateId = parseInt(options.id)
    }
})

// 员工选择回调
function onEmployeeSelect(employee) {
    console.log('选择编制人：', employee)
}

// 附件上传成功回调
function onAttachmentUploadSuccess({ file, result }) {
    console.log('附件上传成功：', file, result)
}

// 附件删除回调
function onAttachmentRemove({ file, index }) {
    console.log('附件删除：', file, index)
}

// 处理附件列表数据
const processAttachmentList = computed(() => {
    return attachmentFiles.value
        .filter(file => file.status === 'success')
        .map(file => ({
            relatedType: 2, // 调查报告
            fileName: file.fileName || file.name,
            filePath: file.filePath || file.key || file.path,
            fileType: file.fileType || file.type,
            fileSize: file.fileSize || file.size,
            fileDesc: file.fileDesc || ''
        }))
})

// 提交表单
async function submitForm() {
    // 防抖检查
    if (isSubmitting.value) {
        return
    }
    
    // 表单验证
    if (!form.title) {
        uni.showToast({ title: '请输入报告标题', icon: 'none' })
        return
    }
    if (!form.reportCode) {
        uni.showToast({ title: '请输入调查报告编号', icon: 'none' })
        return
    }
    if (!form.employeeId) {
        uni.showToast({ title: '请选择编制人', icon: 'none' })
        return
    }
    if (!form.orgId) {
        uni.showToast({ title: '请选择编制部门', icon: 'none' })
        return
    }
    if (!form.summary) {
        uni.showToast({ title: '请输入报告摘要', icon: 'none' })
        return
    }
    if (!form.investigateBackground) {
        uni.showToast({ title: '请输入调查背景', icon: 'none' })
        return
    }
    if (!form.investigateMethod) {
        uni.showToast({ title: '请输入调查方法', icon: 'none' })
        return
    }
    if (!form.investigateProcess) {
        uni.showToast({ title: '请输入调查过程', icon: 'none' })
        return
    }
    if (!form.investigateFound) {
        uni.showToast({ title: '请输入调查发现', icon: 'none' })
        return
    }
    if (!form.investigateConclusion) {
        uni.showToast({ title: '请输入调查结论', icon: 'none' })
        return
    }
    if (!form.recommendMeasure) {
        uni.showToast({ title: '请输入建议措施', icon: 'none' })
        return
    }

    try {
        isSubmitting.value = true
        uni.showLoading({ title: '提交中...' })

        // 构建提交数据
        const submitData = {
            ...form,
            attachmentList: processAttachmentList.value
        }

        // 调用API接口
        const result = await reportApi.createInvestigateReport(submitData)

        uni.hideLoading()

        if (result && result.id) {
            uni.showToast({
                title: '调查报告提交成功',
                icon: 'success',
                duration: 1500
            })

            // 延迟返回上一级页面
            setTimeout(() => {
                uni.navigateBack()
            }, 1500)
        } else {
            throw new Error('提交失败')
        }
    } catch (error) {
        uni.hideLoading()
        console.error('提交调查报告失败：', error)
        uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
        })
    } finally {
        // 重置提交状态
        setTimeout(() => {
            isSubmitting.value = false
        }, 2000)
    }
}
</script>

<style scoped>
.container {
    background: #f0f2f5;
    padding: 16px;
    min-height: 100vh;
}

.section {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #333;
}

.footer {
    padding: 16px 0;
    background: #fff;
    border-radius: 8px;
}
</style>