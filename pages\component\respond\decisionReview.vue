<template>
	<view class="decision-review-container">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<header-bar shape="circle" prefixIcon="search" clearable :fixed="false" v-model="searchValue"
					@search="search()">
					<template #right>
						<view class="nav-right rel z-100">
							<view class="nav-btn" @click.stop="search()">
								<!-- <uni-icons type="search" size="20" /> -->
								<text class="btn-sou">搜索</text>
							</view>
						</view>
					</template>
				</header-bar>
			</template>
			<view>
				<view @click="showDetail(i)" class="mb-10" v-for="i,j in dataList" :key="j">
					<view class="bg-fff p-32 flex aic jcsb">
						<view class="">
							<view class="f-32 uv-line-1 aaa" style="color: #1F2937;;">
								{{i.name||''}}
							</view>
							<view class="f-24 fw-400 mt-16" style="color: #6B7280;">
								发起：{{i.createdBy||''}} · 负责人：{{i.assignee}} · {{i.createdAt}}
							</view>
						</view>
						<view class="ml-20">
							<view v-if="i.status==='MODIFY'" class="fcc"
								style="width: fit-content; padding: 6rpx 16rpx;background-color: #FFEDD5;border-radius: 30rpx;">
								<text class="f-24 wsn" style="color: #C2410C;">需修改</text>
							</view>
							<view v-if="i.status==='PENDING'" class="fcc"
								style="width: fit-content; padding: 6rpx 16rpx;background-color: #FFEDD5;border-radius: 30rpx;">
								<text class="f-24 wsn" style="color: #C2410C;">待审查</text>
							</view>
							<view v-if="i.status==='PUBLISHED'" class="fcc"
								style="width: fit-content; padding: 6rpx 16rpx;background-color: #FFEDD5;border-radius: 30rpx;">
								<text class="f-24 wsn" style="color: #C2410C;">发布</text>
							</view>
							<view v-if="i.status==='REVIEWING'" class="fcc"
								style="width: fit-content; padding: 6rpx 16rpx;background-color: #FFEDD5;border-radius: 30rpx;">
								<text class="f-24 wsn" style="color: #C2410C;">审核中</text>
							</view>
							<view v-if="i.status==='REVOKE'" class="fcc"
								style="width: fit-content; padding: 6rpx 16rpx;background-color: #FFEDD5;border-radius: 30rpx;">
								<text class="f-24 wsn" style="color: #C2410C;">已撤回</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</z-paging>

	</view>
</template>

<script setup>
	import {
		onMounted,
		ref
	} from 'vue'
	import headerBar from '@/components/headerBar.vue'
	import baseList from '@/components/baseList.vue'

	import complianceApi from '@/api/compliance/index.js'
	import {
		useUserStore
	} from '@/store/pinia.js';
	const loading = ref(false)

	// 显示详情
	const showDetail = (item) => {
		uni.navigateTo({
			url: `/pages/component/respond/decisionReviewDetail?id=${item.id}`
		})
	}

	// 获取状态文本
	const getStatusText = (status) => {
		const statusMap = {
			'MODIFY': '需修改',
			'PENDING': '待审查',
			'PUBLISHED': '发布',
			'REVIEWING': '审核中',
			'REVOKE': '已撤回'
		}
		return statusMap[status] || status
	}



	// 触发搜索
	function search(e) {
		paging.value.reload();
	}
	const searchValue = ref("") //
	const paging = ref(null)
	const dataList = ref([])
	// 列表查询
	const queryList = (pageNo, pageSize) => {
		const userStore = useUserStore();
		var params = {
			page: pageNo - 1,
			size: pageSize,
			// tenantId: userStore.tenantId,
		}
		if (searchValue.value) {
			params.name = searchValue.value
		}

		complianceApi.decisionList(params).then((res) => {
			let arr = res.content
			console.log('arr', arr)
			paging.value.complete(res.content);
		}).catch((err) => {
			paging.value.complete(false);
		})

	}
</script>

<style lang="scss" scoped>
	@import '/static/css/nav.scss';

	.decision-review-container {
		height: 100%;
		background: #F5F5F5;

		.custom-item {
			padding: 24rpx;
			background: #fff;
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;


			.item-header {
				display: flex;
				justify-content: space-between;
				margin-bottom: 16rpx;

				.status-badge {
					padding: 4rpx 12rpx;
					border-radius: 32rpx;

					&.done {
						background: #E8F0FE;
						color: #1A73E8;
					}
				}
			}

			.deadline {
				font-size: 24rpx;
				color: #666;
			}
		}

		.custom-empty {
			text-align: center;
			padding-top: 20vh;

			image {
				width: 280rpx;
				height: 280rpx;
				opacity: 0.8;
			}

			text {
				color: #999;
				margin-top: 32rpx;
				display: block;
			}
		}
	}

	// bottomPop 相关样式
	.info-card {
		background: #FFFFFF;
		border-radius: 12rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		box-shadow: 0px 2rpx 8rpx rgba(0, 0, 0, 0.1);

		.card-title {
			font-size: 28rpx;
			font-weight: 600;
			color: #1A73E8;
			margin-bottom: 16rpx;
			display: block;
		}

		.bullet-point {
			font-size: 28rpx;
			color: #1F2937;
			line-height: 1.6;
			padding-left: 24rpx;
			position: relative;

			&::before {
				content: '•';
				position: absolute;
				left: 0;
				color: #1A73E8;
				font-weight: bold;
			}
		}
	}

	.attachment-header {
		margin-bottom: 16rpx;
	}

	.divider {
		height: 2rpx;
		background: #E5E7EB;
		margin-bottom: 24rpx;
	}

	.attachment-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #F3F4F6;

		&:last-child {
			border-bottom: none;
		}

		.attachment-left {
			flex: 1;
			display: flex;
			align-items: center;

			.attachment-name {
				font-size: 26rpx;
				color: #374151;
				margin-left: 16rpx;
			}
		}

		.attachment-actions {
			display: flex;
			gap: 16rpx;
		}
	}

	.empty-attachment {
		text-align: center;
		padding: 40rpx 0;

		.empty-text {
			font-size: 26rpx;
			color: #9CA3AF;
		}
	}
</style>