<template>
	<view class="supplemental-review-detail-container">
		<!-- 基本信息 -->
		<view class="info-card">
			<text class="card-title">补充审查名称：{{ detailInfo.name || '暂无' }}</text>
			<text class="card-title">状态：{{ getStatusText(detailInfo.status) }}</text>
			<text class="card-title">发起人：{{ detailInfo.createdBy || '暂无' }}</text>
			<text class="card-title">负责人：{{ detailInfo.assignee || '暂无' }}</text>
			<text class="card-title">创建日期：{{ detailInfo.createdAt || '暂无' }}</text>
			<text class="card-title">截止日期：{{ detailInfo.deadline || '暂无' }}</text>
		</view>

		<!-- 说明信息 -->
		<view class="info-card">
			<text class="card-title">说明信息</text>
			<view class="bullet-point">{{ detailInfo.explain || '暂无内容' }}</view>
		</view>

		<!-- 补充内容 -->
		<view class="info-card" v-if="detailInfo.supplementalContent">
			<text class="card-title">补充内容</text>
			<view class="bullet-point">{{ detailInfo.supplementalContent }}</view>
		</view>

		<!-- 审查要求 -->
		<view class="info-card" v-if="detailInfo.reviewRequirements">
			<text class="card-title">审查要求</text>
			<view class="bullet-point">{{ detailInfo.reviewRequirements }}</view>
		</view>

		<!-- 审查意见 -->
		<view class="info-card" v-if="detailInfo.reviewComments">
			<text class="card-title">审查意见</text>
			<view class="bullet-point">{{ detailInfo.reviewComments }}</view>
		</view>

		<!-- 处理结果 -->
		<view class="info-card" v-if="detailInfo.result">
			<text class="card-title">处理结果</text>
			<view class="bullet-point">{{ detailInfo.result }}</view>
		</view>

		<!-- 附件列表 -->
		<view class="info-card">
			<view class="attachment-header">
				<text class="card-title">附件列表 ({{ detailInfo.supplementalAttachments?.length || 0 }})</text>
			</view>
			<view class="divider"></view>
			<view class="attachment-item" v-for="item in detailInfo.supplementalAttachments" :key="item.id">
				<view class="attachment-left">
					<text class="attachment-name">{{ item.fileName || '未命名文件' }}{{ item.fileType ? '.' + item.fileType :
						'' }}</text>
				</view>
				<view class="attachment-actions">
					<FilePreview :item="item" />
					<FileDownload :item="item" />
				</view>
			</view>
			<view class="empty-attachment"
				v-if="!detailInfo.supplementalAttachments || detailInfo.supplementalAttachments.length === 0">
				<text class="empty-text">暂无附件</text>
			</view>
		</view>

		<!-- 一键审核按钮 -->
		<view class="box-btn" v-if="showReviewButton">
			<view class="own-btn">
				<uv-button :disabled="detailInfo.status === 'PUBLISHED'" type="primary" @click="startReview">
					一键审核
				</uv-button>
			</view>
			<view class="own-btn">
				<uv-button @click="reviewRecord">
					审查记录
				</uv-button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import FilePreview from '@/components/FilePreview/FilePreview.vue'
import FileDownload from '@/components/FileDownload/FileDownload.vue'
import complianceApi from '@/api/compliance/index.js'
import contractApi from '@/api/contract/index.js'

const detailInfo = ref({})
const loading = ref(false)
const showReviewButton = ref(true) // 控制一键审核按钮显示，默认显示

// 获取页面参数
const getPageParams = () => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	return currentPage.options
}

// 获取详情数据
const getDetailInfo = async () => {
	const params = getPageParams()
	if (!params.id) {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		return
	}

	uni.showLoading({
		title: '加载中...'
	})

	try {
		const res = await complianceApi.supplementalDetail({
			id: params.id
		})
		detailInfo.value = res
		uni.hideLoading()
	} catch (err) {
		uni.hideLoading()
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	}
}

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		'MODIFY': '需修改',
		'PENDING': '待审查',
		'PUBLISHED': '发布',
		'REVIEWING': '审核中',
		'REVOKE': '已撤回',
		'COMPLETED': '已完成',
		'IN_PROGRESS': '进行中'
	}
	return statusMap[status] || status
}

// 检查审核权限
const checkAuditPermission = async () => {
	const params = getPageParams()
	if (!params.id) {
		return false
	}

	try {
		const res = await contractApi.getComplianceProcess({
			objectId: params.id,
			reviewType: 'SUPPLEMENTAL'
		})
		return res
	} catch (err) {
		console.error('检查审核权限失败:', err)
		return false
	}
}

// 审查记录
const reviewRecord = () => {
	if (detailInfo.value.complianceReview?.id === null) {
		uni.showToast({
			title: '暂无审查记录',
			icon: 'none'
		})
		return
	}
	const params = getPageParams()
	if (!params.id) {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		return
	}

	uni.navigateTo({
		url: `/pages/component/respond/reviewRecord?id=${params.id}&type=supplemental`
	})
}

// 一键审核
const startReview = async () => {
	const params = getPageParams()
	if (!params.id) {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		return
	}

	// 根据complianceReview字段判断跳转页面
	if (detailInfo.value.complianceReview?.id === null) {
		// complianceReview为null，进入initiate页面
		uni.navigateTo({
			url: `/pages/component/respond/initiate?id=${params.id}&type=supplemental`
		})
	} else {
		// complianceReview不为null，先检查审核权限
		const hasAuditPermission = await checkAuditPermission()
		if (!hasAuditPermission) {
			uni.showToast({
				title: '暂无审核权限',
				icon: 'none'
			})
			return
		}

		if (hasAuditPermission.isSubmit) {
			console.log('开始审核')
			uni.navigateTo({
				url: `/pages/component/respond/initiate?id=${params.id}&type=supplemental&complianceReview=${JSON.stringify(detailInfo.value.complianceReview)}`
			})
			return
		} else {
			if (!hasAuditPermission.isAudit) {
				uni.showToast({
					title: '暂无审核权限',
					icon: 'none'
				})
				return
			}
		}


		// 进入complianceRegulations页面
		uni.showModal({
			title: '确认操作',
			content: '确定要进行合规审查吗？',
			confirmText: '确定',
			cancelText: '取消',
			success: (res) => {
				if (res.confirm) {
					// 进入otherReview页面
					const detailObj = {
						name: detailInfo.value.name,
						status: getStatusText(detailInfo.value.status),
						contractType: '其他',
						createdBy: detailInfo.value.createdBy,
						assignee: detailInfo.value.assignee,
						createdAt: detailInfo.value.createdAt,
						explain: detailInfo.value.explain,
					}
					uni.navigateTo({
						url: `/pages/component/respond/otherReview?id=${params.id}&type=supplemental&detailInfo=${encodeURIComponent(JSON.stringify(detailObj))}`
					})
				} else if (res.cancel) {
					// 用户点击了取消
					// 不进行智能审查操作
				}
			}
		})
	}
}

// 每次进入页面都重新获取最新数据
onShow(async () => {
	await getDetailInfo()
})
</script>

<style lang="scss" scoped>
.supplemental-review-detail-container {
	padding: 32rpx;
	background: #F5F5F5;
	min-height: 100vh;

	.info-card {
		background: #FFFFFF;
		border-radius: 12rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		box-shadow: 0px 2rpx 8rpx rgba(0, 0, 0, 0.1);

		.card-title {
			font-size: 28rpx;
			font-weight: 600;
			color: #1A73E8;
			margin-bottom: 16rpx;
			display: block;
		}

		.bullet-point {
			font-size: 28rpx;
			color: #1F2937;
			line-height: 1.6;
			padding-left: 24rpx;
			position: relative;

			&::before {
				content: '•';
				position: absolute;
				left: 0;
				color: #1A73E8;
				font-weight: bold;
			}
		}
	}

	.attachment-header {
		margin-bottom: 16rpx;
	}

	.divider {
		height: 2rpx;
		background: #E5E7EB;
		margin-bottom: 24rpx;
	}

	.attachment-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #F3F4F6;

		&:last-child {
			border-bottom: none;
		}

		.attachment-left {
			flex: 1;
			display: flex;
			align-items: center;

			.attachment-name {
				font-size: 26rpx;
				color: #374151;
				margin-left: 16rpx;
			}
		}

		.attachment-actions {
			display: flex;
			gap: 16rpx;
		}
	}

	.empty-attachment {
		text-align: center;
		padding: 40rpx 0;

		.empty-text {
			font-size: 26rpx;
			color: #9CA3AF;
		}
	}

	.box-btn {
		display: flex;
		gap: 16rpx;
		padding: 32rpx;
		background: #F5F5F5;

		.own-btn {
			flex: 1;
		}
	}
}
</style>