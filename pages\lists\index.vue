<!-- 滑动切换选项卡演示(标准写法) -->
<template>
	<!-- 使用z-paging-swiper为根节点可以免计算高度 -->
	<z-paging-swiper>
		<!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
		<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
		<template #top>
			<z-tabs ref="tabs" :list="tabList" :current="current" @change="tabsChange" />
		</template>
		<!-- swiper必须设置height:100%，因为swiper有默认的高度，只有设置高度100%才可以铺满页面  -->
		<!-- 这里的swiper-list-item为demo中为演示用定义的组件，列表及分页代码在swiper-list-item组件内 -->
		<!-- 请注意，swiper-list-item非z-paging内置组件，在自己的项目中必须自己创建，若未创建则会报组件不存在的错误 -->
		<!-- <swiper-list-item ref="listItem" :tabIndex="index" :currentIndex="current"></swiper-list-item> -->
		<swiper class="swiper" :current="current" @transition="swiperTransition"
			@animationfinish="swiperAnimationfinish">
			<!-- <swiper-item class="swiper-item" v-for="(item, index) in swiperList" :key="index">
				<component :is="item.key" />
			</swiper-item> -->
			<swiper-item>
				<risk v-if="loadedTabs.includes(0)" ref="riskRef" :key="'risk-' + tabKeys[0]"></risk>
			</swiper-item>
			<swiper-item>
				<listResponsibility v-if="loadedTabs.includes(1)" ref="listResponsibilityRef" :key="'listResponsibility-' + tabKeys[1]"></listResponsibility>
			</swiper-item>
			<swiper-item>
				<processList v-if="loadedTabs.includes(2)" ref="processListRef" :key="'processList-' + tabKeys[2]"></processList>
			</swiper-item>
		</swiper>
	</z-paging-swiper>
</template>

<script>
import risk from './risk.vue'
import listResponsibility from './listResponsibility.vue'
import ProcessList from './processList';
export default {
	components: {
		risk,
		listResponsibility,
		ProcessList,
	},
	data() {
		return {
			tabList: ['风险清单', '职责清单', '流程清单'],
			swiperList: [{
				name: '风险清单',
				key: 'risk'
			},
			{
				name: '职责清单',
				key: 'listResponsibility'
			},
			{
				name: '流程清单',
				key: 'ProcessList'
			},
			],
			current: 0, // tabs组件的current值，表示当前活动的tab选项
			loadedTabs: [0], // 已加载的tab索引数组，默认加载第一个
			tabKeys: [0, 0, 0], // 用于强制重新渲染组件的key值
		};
	},
	onShow() {
		// 在页面onShow的时候，刷新当前列表（不是必须的）
		// this.$refs.listItem && this.reloadCurrentList();
	},
	methods: {
		// tabs通知swiper切换
		tabsChange(index) {
			this.current = index;
			// 懒加载：如果该tab还未加载，则加载它
			if (!this.loadedTabs.includes(index)) {
				this.loadedTabs.push(index);
			}
		},
		// swiper-item左右移动，通知tabs切换
		swiperTransition(e) {
			const dx = e.detail.dx;
			this.$refs.tabs.setDx(dx);
		},
		// swiper切换结束
		swiperAnimationfinish(e) {
			const current = e.detail.current;
			this.current = current;
			// 懒加载：如果该tab还未加载，则加载它
			if (!this.loadedTabs.includes(current)) {
				this.loadedTabs.push(current);
			}
			this.$refs.tabs.unlockDx();
			this.$refs.tabs.setFinishCurrent(current);
		},
		// 刷新当前tab的数据
		refreshCurrentTab() {
			// 增加当前tab的key值，强制重新渲染组件
			this.tabKeys[this.current]++;
			// 确保当前tab在已加载列表中
			if (!this.loadedTabs.includes(this.current)) {
				this.loadedTabs.push(this.current);
			}
			
			// 调用当前tab组件的reload方法刷新数据
			this.$nextTick(() => {
				switch(this.current) {
					case 0:
						if (this.$refs.riskRef && this.$refs.riskRef.reload) {
							this.$refs.riskRef.reload();
						}
						break;
					case 1:
						if (this.$refs.listResponsibilityRef && this.$refs.listResponsibilityRef.reload) {
							this.$refs.listResponsibilityRef.reload();
						}
						break;
					case 2:
						if (this.$refs.processListRef && this.$refs.processListRef.reload) {
							this.$refs.processListRef.reload();
						}
						break;
				}
			});
		},
	},
	onShow() {
		// 从详情页返回时，只刷新当前tab的数据
		this.refreshCurrentTab();
	},
}
</script>

<style>
.swiper {
	height: 100%;
}
</style>