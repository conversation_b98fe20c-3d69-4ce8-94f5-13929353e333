import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 100000 // 设置超时时间为100秒
    })
}
const baseUrl = `/services/whiskerguardaiservice/api/ai/`
const aiToolsUrl = `/services/whiskerguardaiservice/api/ai-tools/`

export default {
    // 获取AI对话
    queryChat(params) {
        return request(
            `${baseUrl}invoke`,
            params, 'POST')
    },
    // 获取AI对话流
    queryChatStream(params) {
        return request(
            `${baseUrl}stream`,
            params, 'POST')
    },
    // 获取聊天记录列表
    queryChatList(params) {
        return request(
            `/services/whiskerguardaiservice/api/ai-requests/employee/query-by-status?employeeId=${params.employeeId}&status=${params.status}&page=${params.pageNo}&size=${params.pageSize}`,
            params, 'POST')
    },
    // 获取所有模型列表
    getModelList() {
        return request(
            `${aiToolsUrl}models`,
            {}, 'GET')
    },
    // 获取会话ID
    getSessionId() {
        return request(
            `/services/whiskerguardaiservice/api/ai/conversations/generate`,
            {}, 'POST')
    },
}