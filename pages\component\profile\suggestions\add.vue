<template>
    <view class="feedback-container">
        <!-- 提示说明区域 -->
        <view class="hint-area">
            <text class="hint-text">欢迎反馈您在使用中的问题或建议，我们会尽快处理</text>
        </view>
        <!-- 内容输入框 -->
        <view class="input-area">
            <textarea v-model="feedbackContent" placeholder="请描述您遇到的问题或建议" placeholder-class="placeholder-style"
                maxlength="500" auto-height class="feedback-textarea" />
            <text class="word-count">{{ feedbackContent.length }}/500</text>
        </view>
        <!-- 图片上传区域 -->
        <view class="upload-area">
            <text class="upload-title">添加图片（最多3张）</text>
            <uploadFile v-model="imageList" :limit="3" fileType="image" :sizeLimit="3" tipText="支持jpg、png格式，单张图片不超过5MB"
                :autoUpload="true" serviceName="suggestions" categoryName="attachments"
                @upload-success="handleUploadSuccess" @upload-error="handleUploadError" />
        </view>

        <!-- 匿名提交开关 -->
        <view class="anonymous-area">
            <text>匿名提交</text>
            <switch :checked="isAnonymous" @change="toggleAnonymous" color="#4CD964" />
        </view>
        <!-- AI客服入口 -->
        <view class="ai-service-area">
            <view class="ai-service-content">
                <view>
                    <view class="ai-service-title">🤖 AI客服助手</view>
                    <view class="ai-service-desc">快速查找常见问题或制度说明，7×24小时随时可用</view>
                </view>
            </view>
            <button class="ai-btn" @click="startAIConsult">
                <uni-icons type="chat" size="20" color="#fff" />
                <text>启动 AI 咨询</text>
            </button>
        </view>
        <!-- 提交按钮 -->
        <view class="submit-area">
            <button class="submit-btn" :disabled="!feedbackContent" @click="submitFeedback">
                提交反馈
            </button>
        </view>
        <!-- 成功提示弹窗 -->
        <uni-popup ref="successPopup" :mask-click="false" type="center">
            <view class="popup-content">
                <uni-icons type="checkmarkempty" size="48" color="#4CD964" />
                <text class="popup-text">感谢您的反馈，我们将尽快处理</text>
                <button class="popup-btn" @click="closePopup">确定</button>
            </view>
        </uni-popup>
    </view>
</template>
<script setup>
import {
    ref,
    onMounted
} from 'vue';

import uploadFile from '@/components/uploadFile.vue';
import { onLoad } from '@dcloudio/uni-app';
import suggestionApi from './index.js';

const feedbackContent = ref('');
const imageList = ref([]);
const isAnonymous = ref(false);

const successPopup = ref();
// 图片上传功能已由 uploadFile 组件处理
const toggleAnonymous = (e) => {
    isAnonymous.value = e.detail.value;
};
const submitFeedback = async () => {
    if (!feedbackContent.value) {
        uni.showToast({
            title: '请填写反馈内容',
            icon: 'none'
        });
        return;
    }

    try {
        uni.showLoading({
            title: '提交中...',
            mask: true
        });

        // 构建请求参数
        const params = {
            detail: feedbackContent.value,
            isAnonymous: isAnonymous.value ? 1 : 0,
            contactWay: '',
            attachments: imageList.value.map(item => ({
                fileName: item.name || item.fileName,
                filePath: item.key,
                fileType: item.type || 'image',
                fileSize: item.size,
                fileDesc: '投诉建议附件'
            }))
        };

        // 调用API
        const result = await suggestionApi.createSuggestion(params);

        uni.hideLoading();

        if (result) {
            successPopup.value.open();
            // 重置表单
            feedbackContent.value = '';
            imageList.value = [];
            isAnonymous.value = false;
        }
    } catch (error) {
        uni.hideLoading();
        console.error('提交失败:', error);
        uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
        });
    }
};
const closePopup = () => {
    if (successPopup.value) {
        successPopup.value.close()
        //返回上一页
        uni.navigateBack();
    }

};
const makePhoneCall = () => {
    uni.makePhoneCall({
        phoneNumber: '4001234567',
        fail: () => {
            uni.showToast({
                title: '拨打电话失败',
                icon: 'none'
            });
        }
    });
};
const startAIConsult = () => {
    uni.navigateTo({
        url: '/pages/component/qa/qaDetail'
    });
};

const handleUploadSuccess = (fileInfo) => {
    console.log('文件上传成功:', fileInfo);
    // 更新图片列表
    if (fileInfo && fileInfo.url) {
        imageList.value.push(fileInfo);
    }
};

const handleUploadError = (error) => {
    console.error('文件上传失败:', error);
    uni.showToast({
        title: '图片上传失败',
        icon: 'none'
    });
};
onLoad(() => {
    // 页面加载时初始化
});


</script>
<style lang="scss" scoped>
.feedback-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 0 24rpx;
    padding-bottom: calc(120rpx + env(safe-area-inset-bottom));

    .hint-area {
        background-color: #f0f7ff;
        border-radius: 12rpx;
        padding: 20rpx 24rpx;
        margin: 24rpx 0;
    }

    .hint-text {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }

    .input-area {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        position: relative;
    }

    .feedback-textarea {
        width: 100%;
        min-height: 200rpx;
        font-size: 16px;
        color: #333;
    }

    .placeholder-style {
        color: #999;
        font-size: 16px;
    }

    .word-count {
        position: absolute;
        right: 24rpx;
        bottom: 24rpx;
        font-size: 12px;
        color: #999;
    }

    .upload-area {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
    }

    .upload-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 20rpx;
        display: block;
    }

    /* 图片上传样式已由 uploadFile 组件处理 */

    .anonymous-area {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .anonymous-area text {
        font-size: 16px;
        color: #333;
    }

    .submit-area {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 24rpx;
        padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
        background-color: #fff;
        border-top: 1px solid #f0f0f0;
        z-index: 999;
    }

    .submit-btn {
        background-color: #007aff;
        color: #fff;
        border-radius: 50rpx;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 16px;
    }

    .submit-btn[disabled] {
        background-color: #ccc;
    }

    .call-service-area {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 32rpx 24rpx;
        margin-bottom: 24rpx;
    }

    .call-service-content {
        margin-bottom: 24rpx;
    }

    .ai-service-area {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 32rpx 24rpx;
        margin-bottom: 24rpx;
    }

    .ai-service-content {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .ai-service-title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
    }

    .ai-service-desc {
        font-size: 14px;
        color: #666;
    }

    .ai-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #6236ff;
        color: #fff;
        border-radius: 50rpx;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 16px;
    }

    .ai-btn text {
        margin-left: 8rpx;
    }

    .call-service-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 12rpx;
    }

    .phone-number {
        font-size: 24px;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
    }

    .working-hours {
        font-size: 14px;
        color: #999;
    }

    .call-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #11c76f;
        color: #fff;
        border-radius: 50rpx;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 16px;
    }

    .call-btn text {
        margin-left: 8rpx;
    }

    .popup-content {
        background-color: #fff;
        border-radius: 24rpx;
        padding: 48rpx;
        width: 500rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .popup-text {
        font-size: 16px;
        color: #333;
        margin: 24rpx 0 40rpx;
        text-align: center;
    }

    .popup-btn {
        background-color: #007aff;
        color: #fff;
        border-radius: 50rpx;
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 16px;
        margin-top: 20rpx;
    }
}
</style>