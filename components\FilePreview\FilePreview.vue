<template>
  <view class="action-btn" @click="handlePreview">
    <uni-icons type="eye" size="14" color="#1A73E8"></uni-icons>
    <text class="action-text">预览</text>
  </view>
</template>

<script setup>
import { previewableTypes } from '@/utils/file-utils'
import { ref } from 'vue'
import uploads from '@/api/upload.js'// 假设你的 getFileUrl 来自这里

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const handlePreview = async () => {
  const filePath = props.item.filePath
  const fileUrl = await uploads.getFileUrl(filePath)
  if (!fileUrl) {
    uni.showToast({ title: '文件路径不存在', icon: 'none' })
    return
  }

  // 从fileName提取文件扩展名
  let fileExtension = ''
  const fileName = props.item.filePath
  if (fileName) {
    const lastDotIndex = fileName.lastIndexOf('.')
    if (lastDotIndex !== -1) {
      fileExtension = fileName.substring(lastDotIndex + 1).toLowerCase()
    }
  }
  
  // 检查文件扩展名是否在支持的类型中
  if (!fileExtension || !previewableTypes.includes(fileExtension)) {
    uni.showToast({ title: '该文件类型不支持预览', icon: 'none' })
    return
  }
  
  const actualFileType = fileExtension
  if (['jpg', 'jpeg', 'png', 'gif'].includes(actualFileType)) {
    uni.previewImage({ urls: [fileUrl], current: fileUrl })
  } else {
    uni.showLoading({ title: '加载中...' })
    uni.downloadFile({
      url: fileUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.openDocument({
          filePath: res.tempFilePath,
          fileType: actualFileType, // 指定文件类型，解决iOS端预览问题
          showMenu: true,
          success: () => {
            if (actualFileType === 'txt') {
              uni.showToast({ title: 'txt文件预览成功', icon: 'success' })
            }
          },
          fail: () => {
            uni.showToast({ title: '预览失败', icon: 'none' })
          }
        })
        }
        uni.hideLoading()
      },
      fail: () => {
        uni.hideLoading()
        uni.showToast({ title: '文件下载失败', icon: 'none' })
      }
    })
  }
}
</script>

<style scoped>
.action-btn {
  display: flex;
  align-items: center;
}
.action-text {
  margin-left: 4rpx;
}
</style>