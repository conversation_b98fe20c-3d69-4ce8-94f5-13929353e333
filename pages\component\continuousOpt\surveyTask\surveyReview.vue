<template>
  <view class="container">
    <uv-form labelPosition="top" label-width="200" :model="form">
      <!-- 调查记录基础信息 -->
      <view class="section">
        <uv-form-item label="记录编号">
          <uv-input
            v-model="form.recordCode"
            placeholder="请输入记录编号"
            clearable
            disabled
            maxlength="50"
            show-word-limit
          />
        </uv-form-item>
       <uv-form-item label="记录类型">
          <picker-input
            v-model="form.recordType"
            :columns="[recordTypes]"
            placeholder="请选择记录类型"
            display-key="label"
            value-key="value"
          />
        </uv-form-item>
        <uv-form-item label="地点或渠道">
          <uv-input
            v-model="form.location"
            placeholder="请输入地点或渠道"
            clearable
            maxlength="100"
          />
        </uv-form-item>
      </view>

      <!-- 调查内容 -->
      <view class="section">
        <uv-form-item label="记录内容">
          <uv-textarea
            autoHeight
            v-model="form.content"
            placeholder="请输入记录内容"
            maxlength="500"
          />
        </uv-form-item>
        <uv-form-item label="问题发现">
          <uv-textarea
            autoHeight
            v-model="form.discover"
            placeholder="请输入关键发现"
            resize="none"
            maxlength="300"
          />
        </uv-form-item>
      </view>

      <!-- 附件上传 -->
      <view class="section">
        <view class="section-header">
          <view class="section-title">附件上传</view>
        </view>
        <UploadFile
          v-model="form.attachmentList"
          :limit="9"
          :size-limit="10"
          :auto-upload="true"
          :tenant-id="'1'"
          :service-name="'survey'"
          :category-name="'record'"
          tip-text="支持上传图片、文档等文件，单个文件不超过10MB"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        />
      </view>

      <!-- 涉及人员 -->
      <view class="section" v-if="false">
        <view class="section-header">
          <view class="section-title">涉及人员</view>
          <uv-button size="small" plain @click="addInvolveItem">
            <uv-icon name="add" /> 添加人员
          </uv-button>
        </view>
        <view v-if="!form.involveList.length" class="empty">暂无涉及人员</view>
        <view v-else>
          <view
            v-for="(item, idx) in form.involveList"
            :key="idx"
            class="involve-item"
          >
            <uv-form-item label="涉及类型">
              <picker-input
                v-model="item.involve"
                :columns="[involveTypes]"
                placeholder="请选择涉及类型"
                display-key="label"
                value-key="value"
              />
            </uv-form-item>
            <uv-form-item label="人员">
              <uv-input
                v-model="item.involveId"
                type="number"
                placeholder="请输入人员"
              />
            </uv-form-item>
            <uv-form-item label="备注">
              <uv-input
                v-model="item.remark"
                placeholder="请输入备注"
                maxlength="100"
              />
            </uv-form-item>
            <view class="delete-btn" @click="removeInvolveItem(idx)">
              <uv-icon name="delete" color="#ff4757" size="18" />
            </view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="footer">
        <uv-button type="primary" block @click="submitForm">
          提交调查记录
        </uv-button>
      </view>
    </uv-form>
  </view>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import PickerInput from '@/components/picker-input.vue'
import UploadFile from '@/components/uploadFile.vue'
import recordApi from '@/api/violation/record.js'
import codeApi from '@/api/common/index.js'
import getDictData from '@/utils/dict.js'


const form = reactive({
  investigateId: '',
  recordCode: '',
  location: '',
  recordType: '',
  content: '',
  discover: '',
  attachmentList: [],
  involveList: []
})

// 防抖控制
const isSubmitting = ref(false)

// 记录类型选项
const recordTypes = ref([])

// 涉及类型选项
const involveTypes = [
  { label: '员工', value: 1 },
  { label: '部门', value: 2 }
]

// 初始化记录编号
onMounted(async () => {
  try {
    // 获取调查记录编号
    const code = await codeApi.getCode('INVESTIGATE_RECORD')
    form.recordCode = code
    // 获取记录类型字典数据
    const dictData = await getDictData('39', null)
      recordTypes.value = dictData.map(item => ({
        label: item.name,
        value: item.value
      }))
  } catch (error) {
    uni.showToast({
      title: '获取编号失败，使用默认编号',
      icon: 'none'
    })
  }
})

// 获取页面参数
onLoad((options) => {
  if (options && options.id) {
    form.investigateId = parseInt(options.id)
  }
})

// 处理文件上传成功
function handleUploadSuccess(event) {
  console.log('文件上传成功:', event)
  // 可以在这里添加额外的处理逻辑
}

// 处理文件上传失败
function handleUploadError(event) {
  console.error('文件上传失败:', event)
  uni.showToast({
    title: '文件上传失败',
    icon: 'none'
  })
}

// 添加涉及人员
function addInvolveItem() {
  form.involveList.push({
    involve: 1,
    involveId: 0,
    remark: ''
  })
}

// 删除涉及人员
function removeInvolveItem(index) {
  form.involveList.splice(index, 1)
}

// 提交表单
async function submitForm() {
  // 防抖检查
  if (isSubmitting.value) {
    return
  }
  
  // 表单验证
  if (!form.recordCode) {
    uni.showToast({ title: '请输入记录编号', icon: 'none' })
    return
  }
  if (!form.location) {
    uni.showToast({ title: '请输入地点或渠道', icon: 'none' })
    return
  }
  if (!form.content) {
    uni.showToast({ title: '请输入记录内容', icon: 'none' })
    return
  }
  if (!form.discover) {
    uni.showToast({ title: '请输入关键发现', icon: 'none' })
    return
  }

  // 验证涉及人员数据
  for (let i = 0; i < form.involveList.length; i++) {
    const item = form.involveList[i]
    if (!item.involveId) {
      uni.showToast({ title: `请输入第${i + 1}个涉及人员的有效ID`, icon: 'none' })
      return
    }
  }
  
  try {
    isSubmitting.value = true
    uni.showLoading({ title: '提交中...' })
    // 调用API接口
    const result = await recordApi.createInvestigateRecord(form)
    
    uni.hideLoading()
    
    if (result && result.id) {
      uni.showToast({ 
        title: '调查记录提交成功', 
        icon: 'success',
        duration: 1500
      })
      
      // 延迟返回上一级页面
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error('提交失败')
    }
  } catch (error) {
    uni.hideLoading()
    console.error('提交调查记录失败：', error)
    uni.showToast({ 
      title: '提交失败，请重试', 
      icon: 'none' 
    })
  } finally {
    // 重置提交状态
    setTimeout(() => {
      isSubmitting.value = false
    }, 2000)
  }
}
</script>

<style scoped>
.container {
  background: #f0f2f5;
  padding: 16px;
  min-height: 100vh;
}

.section {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.attachment-item,
.involve-item {
  position: relative;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fafafa;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px;
  cursor: pointer;
}

.footer {
  padding: 16px 0;
  background: #fff;
  border-radius: 8px;
}

.empty {
  text-align: center;
  color: #999;
  padding: 32px 0;
  font-size: 14px;
}
</style>