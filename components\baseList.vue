<!-- 
基础列表组件

功能：
- 提供通用的列表展示功能
- 支持分页加载
- 支持空状态显示

Props:
- items: {Array} 必需 - 要展示的数据列表
- loading: {Boolean} 可选(false) - 是否显示加载状态
- emptyText: {String} 可选('暂无数据') - 空状态提示文字
- pageSize: {Number} 可选(10) - 每页显示数量
- currentPage: {Number} 可选(1) - 当前页码

Events:
- load-more: 当滚动到底部触发加载更多
- item-click: 当点击列表项时触发，参数为当前项数据

Slots:
- item: 自定义列表项内容，作用域参数为{item, index}
- header: 列表头部内容
- footer: 列表底部内容
- empty: 自定义空状态内容

使用示例：
<baseList 
  :items="listData" 
  :loading="isLoading"
  @load-more="fetchData"
  @item-click="handleItemClick"
>
  <template #item="{ item }">
    <div>{{ item.name }}</div>
  </template>
</baseList>
-->
<template>

	<view class="main-content">
		<!-- 列表容器 -->
		<view class="list-container" :style="containerStyle">
			<!-- 默认插槽：用于自定义整个列表内容 -->
			<slot v-if="$slots.default" />

			<!-- 默认列表项渲染 -->
			<template v-else>
				<!-- 作用域插槽：允许自定义单个列表项 -->
				<view v-for="(item, index) in listData" :key="index" class="list-item" :style="itemStyle"
					@click="handleItemClick(item, index)">
					<!-- 套个content-wrapper 使得子元素继承父元素框架 -->
					<view class="content-wrapper">
						<slot name="item" :item="item" :index="index">
							<!-- 默认列表项内容 -->
							<view class="item-content">
								<view class="item-t-box">
									<text class="item-title">{{ getDepartmentName(item.postName) }}</text>
									<text class="info-status" :data-status="item.approvalStatus">{{ getStatusText(item.approvalStatus) }}</text>
								</view>
								<view class="item-info">
									<!-- 风险等级标签 -->
									<text v-if="item.riskControlInfo.riskLevel" class="info-text" :data-risk="item.riskControlInfo.riskLevel">
										{{ item.riskControlInfo.riskLevel === 'HIGH' ? '高危' : item.riskControlInfo.riskLevel === 'MEDIUM' ? '中危' : '低危' }}
									</text>
									<text class="item-desc">{{ item.orgUnitName }} 创建人：{{ item.updatedBy }} </text>
								</view>
							</view>
						</slot>
						<uni-icons type="right" size="16" color="#999" />
					</view>
				</view>
			</template>
		</view>
	</view>
</template>

<script setup>
	import {
		defineProps,
		defineEmits
	} from 'vue'
	import {
		useUserStore
	} from '@/store/pinia.js';

	const userStore = useUserStore();
	const props = defineProps({
		// 列表数据
		listData: {
			type: Array,
			default: () => []
		},
		// 列表容器样式
		containerStyle: {
			type: Object,
			default: () => ({
				padding: '20rpx 32rpx'
			})
		},
		// 列表项样式
		itemStyle: {
			type: Object,
			default: () => ({
				marginBottom: '20rpx'
			})
		},
		// 状态映射函数
		getStatusText: {
			type: Function,
			default: (status) => status || '草稿'
		}
	})

	const emit = defineEmits(['item-click'])

	const handleItemClick = (item, index) => {
		emit('item-click', {
			item,
			index
		})
	}

	function getDepartmentName(departmentId) {
		if (!departmentId) return '未设置';
		const departments = userStore.getDepartments();
		if (!departments) return departmentId;

		const department = departments.find(dept => dept.id === departmentId);
		return department ? department.name : departmentId;
	}

	// 根据岗位ID获取岗位名称
	function getPositionName(positionId) {
		if (!positionId) return '未设置';
		const postList = userStore.getPostList();
		if (!postList) return positionId;

		const position = postList.find(post => post.id === positionId);
		return position ? position.name : positionId;
	}
</script>

<style lang="scss" scoped>
	/* 保持原有样式不变 */
	/* @import '/static/css/base.scss'; */
	@import '@/uni.scss';

	.main-content {
		flex: 1;
		background-color: $uni-background-primary;
	}

	.content-wrapper {
		flex: 1;
		min-width: 0;
		/* 必须！解决Flex溢出问题 */
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.list-container {
		flex: 1;
	}

	.list-item {
		width: 100%;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 24rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		box-sizing: border-box;
	}

	.item-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.item-t-box {
		display: flex;
		align-items: center;
		// justify-content: space-between;
	}
	
	.info-status {
		margin-left: 20rpx;
		white-space: nowrap;
		padding: 2rpx 8rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
	}
	
	/* 状态颜色样式 */
	.info-status[data-status="DRAFT"] {
		background-color: #f3f7fd;
		color: #777777;
	}
	
	.info-status[data-status="PENDING"] {
		background-color: #fff7e6;
		color: #fa8c16;
	}
	
	.info-status[data-status="APPROVED"] {
		background-color: #f6ffed;
		color: #52c41a;
	}
	
	.info-status[data-status="REJECTED"] {
		background-color: #fff2f0;
		color: #ff4d4f;
	}

	.item-title {
		font-size: 32rpx;
		color: #1a1a1a;
		display: block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.item-info {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8rpx;
		margin-top: 8rpx;
	}

	.item-desc {
		font-size: 24rpx;
		color: $uni-text-color-grey;
		display: block;
	}

	.info-text {
		display: inline-block;
		padding: 6rpx 16rpx;
		color: white;
		font-weight: normal;
		border-radius: 6rpx;
		text-align: center;
		font-size: 24rpx;
	}

	/* 高危样式 - 红色背景 */
	.info-text.high-risk,
	.info-text[data-risk="HIGH"] {
		background-color: rgb(255, 234, 234);
		color: #ff7875;
	}

	/* 中危样式 - 橙色背景 */
	.info-text.medium-risk,
	.info-text[data-risk="MEDIUM"] {
		background-color: rgb(255, 244, 229);
		color: #ffc069;
	}

	/* 低危样式 - 绿色背景 */
	.info-text.low-risk,
	.info-text[data-risk="LOW"] {
		background-color: rgb(230, 255, 250);
		color: rgb(49, 151, 149);
	}

	.item-process {
		font-size: 30rpx;
		color: $uni-color-primary;
	}
</style>