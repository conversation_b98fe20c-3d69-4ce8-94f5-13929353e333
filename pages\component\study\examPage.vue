<template>
    <view class="exam-page">
        <!-- 顶部导航栏 -->
         <uv-navbar :placeholder="true" :title="`第${ currentIndex + 1 }/${ questions.length }题`" @leftClick="handleBack"></uv-navbar>
        <!-- <view class="header">
            <view class="header-left">
                <uni-icons type="left" size="24" color="#333" @click="handleBack"></uni-icons>
            </view>
            <view class="header-title">
                <text>第 {{ currentIndex + 1 }} / {{ questions.length }} 题</text>
            </view>
            <view class="header-right">
                <text class="timer">{{ formatTime(countdown) }}</text>
            </view>
        </view> -->
        <!-- 题目内容区域 -->
        <scroll-view class="content" scroll-y>
            <!-- 加载状态 -->
            <view v-if="loading" class="loading-container">
                <uni-icons type="spinner-cycle" size="40" color="#2979FF"></uni-icons>
                <text class="loading-text">正在加载考试题目...</text>
            </view>
            <!-- 题目内容 -->
            <view v-else-if="questions.length > 0" class="question-container">
                <view class="question-type">
                    <text>{{ getQuestionType(currentQuestion.type) }}</text>
                </view>
                <view class="question-text">
                    <text>{{ currentQuestion.text }}</text>
                    <image v-if="currentQuestion.image" :src="currentQuestion.image" mode="widthFix"
                        @click="previewImage(currentQuestion.image)"></image>
                </view>
                <!-- 选项区域 -->
                <view class="options">
                    <view v-for="(option, index) in currentQuestion.options" :key="index" class="option-item"
                        :class="{ 'selected': isSelected(index) }" @click="handleSelectOption(index)">
                        <view class="option-prefix">
                            <text>{{ String.fromCharCode(65 + index) }}.</text>
                        </view>
                        <view class="option-content">
                            <text>{{ option.text }}</text>
                            <image v-if="option.image" :src="option.image" mode="widthFix"
                                @click.stop="previewImage(option.image)"></image>
                        </view>
                        <view class="option-selector">
                            <uni-icons :type="currentQuestion.type === 'single' ? 'circle' : 'checkbox-filled'"
                                size="20" :color="isSelected(index) ? '#2979FF' : '#999'"></uni-icons>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 无题目状态 -->
            <view v-else class="empty-container">
                <text class="empty-text">暂无考试题目</text>
            </view>
        </scroll-view>
        <!-- 底部操作栏 -->
        <view class="footer">
            <view class="footer-btn" @click="handlePrevQuestion">
                <text>上一题</text>
            </view>
            <view class="footer-btn" @click="showQuestionCard = true">
                <text>题卡</text>
            </view>
            <view class="footer-btn" @click="handleSubmit">
                <text>提交</text>
            </view>
            <view class="footer-btn" @click="handleNextQuestion">
                <text>下一题</text>
            </view>
        </view>
        <!-- 题卡弹窗 -->
        <uni-popup ref="popup" type="bottom" :mask-click="false">
            <view class="question-card">
                <view class="card-header">
                    <text>答题进度: {{ answeredCount }} / {{ questions.length }}</text>
                    <uni-icons type="close" size="24" color="#999" @click="showQuestionCard = false"></uni-icons>
                </view>
                <scroll-view class="card-content" scroll-y>
                    <view class="question-grid">
                        <view v-for="(item, index) in questions" :key="index" class="question-number" :class="{
                            'answered': item.answer.length > 0,
                            'marked': item.marked,
                            'current': index === currentIndex
                        }" @click="handleJumpQuestion(index)">
                            <text>{{ index + 1 }}</text>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </uni-popup>
    </view>
</template>
<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import assessApi from './assess.js';

// 题目数据
const questions = ref([]);
const currentIndex = ref(0);
const showQuestionCard = ref(false);
const popup = ref();
const countdown = ref(3600); // 60分钟
const timer = ref(null);
const loading = ref(false);
const examRecordId = ref(null);
const isExamEnded = ref(false); // 考试是否结束
const currentQuestion = computed(() => questions.value[currentIndex.value]);
const answeredCount = computed(() => questions.value.filter(q => q.answer.length > 0).length);

// 监听题卡显示状态
watch(showQuestionCard, (newVal) => {
    if (newVal) {
        popup.value?.open();
    } else {
        popup.value?.close();
    }
});

const isSubmit = ref(false);
// 格式化时间显示
const formatTime = (seconds) => {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = seconds % 60;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};
// 获取题型文字
const getQuestionType = (type) => {
    switch (type) {
        case 'single': return '单选题';
        case 'multi': return '多选题';
        case 'judge': return '判断题';
        default: return '';
    }
};
// 判断选项是否被选中
const isSelected = (index) => {
    return currentQuestion.value.answer.includes(index);
};
// 处理选项选择
const handleSelectOption = (index) => {
    // 检查考试是否已结束
    if (isExamEnded.value) {
        uni.showToast({
            title: '考试时间已结束，无法答题',
            icon: 'none'
        });
        return;
    }
    
    if (currentQuestion.value.type === 'single') {
        questions.value[currentIndex.value].answer = [index];
    } else if (currentQuestion.value.type === 'multi') {
        const answerIndex = currentQuestion.value.answer.indexOf(index);
        if (answerIndex === -1) {
            questions.value[currentIndex.value].answer.push(index);
        } else {
            questions.value[currentIndex.value].answer.splice(answerIndex, 1);
        }
    } else if (currentQuestion.value.type === 'judge') {
        questions.value[currentIndex.value].answer = [index];
    }
};
// 上一题
const handlePrevQuestion = () => {
    if (currentIndex.value > 0) {
        currentIndex.value--;
    } else {
        uni.showToast({
            title: '已经是第一题了',
            icon: 'none'
        });
    }
};
// 下一题
const handleNextQuestion = () => {
    if (currentIndex.value < questions.value.length - 1) {
        currentIndex.value++;
    } else {
        uni.showToast({
            title: '已经是最后一题了',
            icon: 'none'
        });
    }
};
// 跳转到指定题目
const handleJumpQuestion = (index) => {
    currentIndex.value = index;
    showQuestionCard.value = false;
};
// 返回处理
const handleBack = () => {
    if(isSubmit.value){
        uni.navigateBack();
        return;
    }
    // 检查是否有未提交的答案
        uni.showModal({
            title: '提示',
            content: '您还没有提交考试，确定要离开吗？离开将自动提交当前答案。',
            confirmText: '确定离开',
            cancelText: '继续考试',
            success: async (res) => {
                if (res.confirm) {
                    // 停止倒计时
                    if (timer.value) {
                        clearInterval(timer.value);
                    }
                    // 调用提交考试接口（默认提交）
                    await submitExam(true);
                }
            }
        });
    // if (hasAnswers) {
    //     uni.showModal({
    //         title: '提示',
    //         content: '您还没有提交考试，确定要离开吗？',
    //         confirmText: '确定离开',
    //         cancelText: '继续考试',
    //         success: (res) => {
    //             if (res.confirm) {
    //                 // 停止倒计时
    //                 if (timer.value) {
    //                     clearInterval(timer.value);
    //                 }
    //                 uni.navigateBack();
    //             }
    //         }
    //     });
    //     return true; // 阻止默认返回行为
    // } else {
    //     // 没有答案，直接返回
    //     if (timer.value) {
    //         clearInterval(timer.value);
    //     }
    //     return false; // 允许默认返回行为
    // }
};
// 预览图片
const previewImage = (url) => {
    uni.previewImage({
        urls: [url]
    });
};

// 提交考试
const handleSubmit = () => {
    // 检查考试是否已结束
    if (isExamEnded.value) {
        uni.showToast({
            title: '考试时间已结束',
            icon: 'none'
        });
        return;
    }
    
    console.log('点击提交按钮');
    const totalQuestions = questions.value.length;
    const answeredQuestions = answeredCount.value;

    console.log('总题数:', totalQuestions, '已答题数:', answeredQuestions);

    if (answeredQuestions < totalQuestions) {
        // 未答完所有题目，给出提醒
        console.log('未答完所有题目，显示确认弹窗');
        uni.showModal({
            title: '提示',
            content: `您还有 ${totalQuestions - answeredQuestions} 道题未作答，是否确认提交？`,
            confirmText: '确认提交',
            cancelText: '继续答题',
            success: (res) => {
                console.log('弹窗结果:', res);
                if (res.confirm) {
                    console.log('用户确认提交，调用submitExam');
                    submitExam();
                }
            }
        });
    } else {
        // 已答完所有题目，二次确认
        uni.showModal({
            title: '提示',
            content: `您已完成所有题目，确认提交考试吗？`,
            confirmText: '确认提交',
            cancelText: '继续答题',
            success: (res) => {
                console.log('弹窗结果:', res);
                if (res.confirm) {
                    console.log('用户确认提交，调用submitExam');
                    submitExam();
                }
            }
        });

        // uni.showModal({
        //     title: '提示',
        //     content: `您已完成所有题目，确认提交考试吗？`,
        //     confirmText: '确认提交',
        //     cancelText: '继续答题',
        //     success: (res) => {
        //         console.log('弹窗结果:', res);
        //         if (res.confirm) {
        //             console.log('用户确认提交，调用submitExam');
        //             submitExam();
        //         }
        //     }
        // });
    }
};

// 执行提交考试
const submitExam = async (isDefaultSubmit = false) => {
    console.log('开始提交考试，examRecordId:', examRecordId.value);
    console.log('当前题目数据:', questions.value);
    isSubmit.value = true
    if (!examRecordId.value) {
        console.error('缺少考试记录ID');
        uni.showToast({
            title: '缺少考试记录ID',
            icon: 'none'
        });
        return;
    }

    try {
        uni.showLoading({ title: '提交中...' });

        // 构造提交数据
        const submitData = {};
        questions.value.forEach(question => {
            // 将答案索引转换为字母
            const answerLetters = question.answer.map(index => String.fromCharCode(65 + index));
            const userAnswer = question.type === 'multi' ? answerLetters.join(',') : (answerLetters[0] || '');
            submitData[question.id] = userAnswer;
        });

        console.log('提交数据:', submitData);
        console.log('开始调用API...');

        const result = await assessApi.submitExam(examRecordId.value,submitData);

        console.log('API调用结果:', result);
        uni.hideLoading();

        if (result) {
            uni.showToast({
                title: '提交成功',
                icon: 'success'
            });

            // 停止倒计时
            if (timer.value) {
                clearInterval(timer.value);
            }

            // 跳转到成绩页面或返回上一页
            if (isDefaultSubmit) {
                // 默认提交时立即返回
                uni.navigateBack();
            } else {
                // 正常提交时延迟返回
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            }
        } else {
            console.log('API返回结果为空或false');
            uni.showToast({
                title: '提交失败，请重试',
                icon: 'none'
            });
        }
    } catch (error) {
        uni.hideLoading();
        console.error('提交考试失败:', error);
        uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
        });
    }
};
// 倒计时
const startCountdown = () => {
    timer.value = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--;
        } else {
            clearInterval(timer.value);
            isExamEnded.value = true; // 设置考试结束状态
            uni.showModal({
                title: '提示',
                content: '考试时间已结束，将自动提交考试',
                showCancel: false,
                success: async () => {
                    // 自动提交考试
                    await submitExam(true);
                }
            });
        }
    }, 1000);
};
onMounted(() => {
    startCountdown();
});

// 监听返回按钮
onUnload(() => {
    console.log('返回');
     handleBack();
});
onUnmounted(() => {
    if (timer.value) {
        clearInterval(timer.value);
    }
});
onLoad(async (options) => {
    try {
        loading.value = true;

        // 获取从详情页传递的考试ID和考试时长
        const examId = options.examId;
        const examDuration = options.examDuration;
        
        if (!examId) {
            uni.showToast({
                title: '缺少考试ID参数',
                icon: 'error'
            });
            return;
        }

        // 如果URL参数中有examDuration，优先使用它设置倒计时
        if (examDuration && !isNaN(examDuration)) {
            countdown.value = parseInt(examDuration) * 60; // 转换为秒
        }

        // 调用开始考试接口
        const startExamResult = await assessApi.startExam(examId);
        if (startExamResult && startExamResult.id) {
            examRecordId.value = startExamResult.id;

            // 使用考试记录ID获取题目
            const questionsResult = await assessApi.getExamQuestion(startExamResult.id);
            if (questionsResult && Array.isArray(questionsResult)) {
                // 转换API返回的题目格式为组件需要的格式
                questions.value = questionsResult.map((item, index) => {
                    const options = [];
                    if (item.optionA) options.push({ text: item.optionA, image: null });
                    if (item.optionB) options.push({ text: item.optionB, image: null });
                    if (item.optionC) options.push({ text: item.optionC, image: null });
                    if (item.optionD) options.push({ text: item.optionD, image: null });

                    // 根据题目类型确定type
                    let type = 'single';
                    if (item.questionType === '多选题') {
                        type = 'multi';
                    } else if (item.questionType === '判断题') {
                        type = 'judge';
                    }

                    // 处理用户已选答案
                    let userAnswerArray = [];
                    if (item.userAnswer && item.isAnswered) {
                        if (type === 'multi') {
                            // 多选题答案可能是逗号分隔的字符串
                            userAnswerArray = item.userAnswer.split(',').map(ans => {
                                // 将答案字母转换为索引 (A->0, B->1, C->2, D->3)
                                return ans.charCodeAt(0) - 65;
                            }).filter(index => index >= 0 && index < options.length);
                        } else {
                            // 单选题和判断题
                            const answerIndex = item.userAnswer.charCodeAt(0) - 65;
                            if (answerIndex >= 0 && answerIndex < options.length) {
                                userAnswerArray = [answerIndex];
                            }
                        }
                    }

                    return {
                        id: item.questionId,
                        type: type,
                        text: item.questionContent,
                        image: null,
                        options: options,
                        answer: userAnswerArray,
                        marked: false
                    };
                });

                // 设置考试时长（如果接口返回了考试信息且URL参数中没有examDuration）
                if (!examDuration && startExamResult.examInfo && startExamResult.examInfo.examDuration) {
                    countdown.value = startExamResult.examInfo.examDuration * 60; // 转换为秒
                }
            }
        } else {
            uni.showToast({
                title: '开始考试失败',
                icon: 'error'
            });
        }
    } catch (error) {
        // console.error('加载考试数据失败:', error);
        // uni.showToast({
        //     title: '加载考试数据失败',
        //     icon: 'error'
        // });
        setTimeout(() => { 
          isSubmit.value = true
           handleBack()
        }, 1000);
 
    } finally {
        loading.value = false;
    }
});
</script>
<style scoped>
/* page {
    height: 100%;
    background-color: #f5f5f5;
} */

.exam-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}

.header-left,
.header-right {
    width: 100rpx;
}

.header-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    color: #333;
}

.timer {
    font-size: 28rpx;
    color: #FF5252;
}

/* 内容区域样式 */
.content {
    flex: 1;
    overflow: auto;
    background-color: #fff;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 20rpx;
    text-align: center;
}

.loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
}

.empty-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 100rpx 20rpx;
    text-align: center;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
}

.question-container {
    padding: 30rpx;
    margin-bottom: 100rpx;
}

.question-type {
    padding: 10rpx 20rpx;
    background-color: #2979FF;
    color: #fff;
    border-radius: 40rpx;
    display: inline-block;
    margin-bottom: 30rpx;
    font-size: 24rpx;
}

.question-text {
    margin-bottom: 40rpx;
    font-size: 32rpx;
    line-height: 1.6;
    color: #333;
}

.question-text image {
    width: 100%;
    margin-top: 20rpx;
    border-radius: 8rpx;
}

/* 选项样式 */
.options {
    margin-top: 30rpx;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 25rpx;
    margin-bottom: 20rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    border: 1px solid #eee;
}

.option-item.selected {
    background-color: #E3F2FD;
    border-color: #90CAF9;
}

.option-prefix {
    margin-right: 20rpx;
    font-size: 28rpx;
    color: #666;
}

.option-content {
    flex: 1;
    font-size: 28rpx;
    color: #333;
}

.option-content image {
    width: 100%;
    margin-top: 15rpx;
    border-radius: 8rpx;
}

.option-selector {
    margin-left: 20rpx;
}

/* 底部样式 */
.footer {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-top: 1px solid #eee;
}

.footer-btn {
    flex: 1;
    text-align: center;
    padding: 20rpx 10rpx;
    font-size: 28rpx;
    color: #2979FF;
    max-width: 120rpx;
}

/* 题卡样式 */
.question-card {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    max-height: 70vh;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    font-size: 32rpx;
    color: #333;
}

.card-content {
    max-height: 60vh;
}

.question-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.question-number {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    font-size: 32rpx;
    color: #666;
}

.question-number.answered {
    background-color: #4CAF50;
    color: #fff;
}

.question-number.marked {
    background-color: #FFC107;
    color: #fff;
}

.question-number.current {
    background-color: #2979FF;
    color: #fff;
    border: 2rpx solid #1565C0;
}
</style>
