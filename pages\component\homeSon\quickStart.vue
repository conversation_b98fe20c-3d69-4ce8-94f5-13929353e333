<!-- pages/process/index.vue -->
<template>
    <view class="process-page">
        <!-- 步骤指示器 -->
        <progress-indicator :steps="steps" :current-step="currentStep" />
        <!-- <step /> -->
        <!-- 内容卡片区 -->
        <view class="content-card">
            <text class="content-title">步骤 4 — 持续应对</text>
            <view class="divider"></view>

            <view class="step-item">
                <!-- <uni-icons type="search" size="20" color="#1A73E8"></uni-icons> -->
                <view class="step-content">
                    <text class="step-title">创建 <text class="highlight">违规问题调查</text></text>
                    <text class="step-desc">定义调查任务并收集证据</text>
                </view>
            </view>

            <view class="step-item">
                <!-- <uni-icons type="document" size="20" color="#1A73E8"></uni-icons> -->
                <view class="step-content">
                    <text class="step-title">查看 <text class="highlight">调查报告</text></text>
                    <text class="step-desc">阅读并下载调查结果</text>
                </view>
            </view>

            <view class="step-item">
                <!-- <uni-icons type="hammer" size="20" color="#1A73E8"></uni-icons> -->
                <view class="step-content">
                    <text class="step-title">发起 <text class="highlight">责任追究</text></text>
                    <text class="step-desc">制定并落实处理措施</text>
                </view>
            </view>

            <view class="step-item">
                <!-- <uni-icons type="checkmarkcircle" size="20" color="#1A73E8"></uni-icons> -->
                <view class="step-content">
                    <text class="step-title">跟踪 <text class="highlight">整改落实</text></text>
                    <text class="step-desc">确认处理结果并记录</text>
                </view>
            </view>

            <view class="step-item">
                <uni-icons type="stats" size="20" color="#1A73E8"></uni-icons>
                <view class="step-content">
                    <text class="step-title">访问 <text class="highlight">持续改进优化</text></text>
                    <text class="step-desc">总结经验教训并提出改进方案</text>
                </view>
            </view>

            <button  class="action-btn" @click="handleGoToStep">立即前往"持续应对"</button>
        </view>
        <!-- 底部操作栏 -->
        <view class="action-bar safe-area">
            <button  class="nav-button secondary" :disabled="currentStep === 0" @click="prevStep">
                <uni-icons type="arrowleft" size="16" color="#666" />
                <text>上一步</text>
            </button>

            <button  class="nav-button primary" @click="nextStep">
                <text>{{ currentStep === steps.length - 1 ? '完成设置' : '下一步' }}</text>
                <uni-icons :type="currentStep === steps.length - 1 ? 'check' : 'arrowright'" size="16" color="#fff" />
            </button>
        </view>
        <goNavitor url="/pages/component/homeSon/score" />
    </view>
</template>

<script setup>
import goNavitor from '@/components/goNavitor.vue'
// import step from '../../../components/step1.vue' 
import progressIndicator from '@/components/progressIndicator.vue'
import { ref } from 'vue'

const steps = ref([
    { title: '制度建立', description: '基础框架搭建' },
    { title: '清单配置', description: '权限清单管理' },
    { title: '合规审查', description: '风险审查流程' },
    { title: '持续监控', description: '执行情况跟踪' }
])

const currentStep = ref(2)

const prevStep = () => currentStep.value > 0 && currentStep.value--
const nextStep = () => {
    if (currentStep.value < steps.value.length - 1) {
        currentStep.value++
    } else {
        uni.showToast({ title: '设置完成', icon: 'success' })
    }
}
</script>

<style lang="scss" scoped>
.process-page {
    background: #f8f9fa;
    min-height: 100vh;
    overflow: auto;

    /* 内容卡片样式 */
    .content-card {
        margin: 32rpx;
        padding: 48rpx;
        background-color: #fff;
        border-radius: 24rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        margin-bottom: 200rpx;
    }

    .content-title {
        font-size: 40rpx;
        font-weight: bold;
        color: #1A73E8;
        margin-bottom: 32rpx;
        display: block;
    }

    .divider {
        height: 2rpx;
        background-color: #f0f0f0;
        margin: 32rpx 0;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        padding: 24rpx;
        background-color: #f9f9f9;
        border-radius: 16rpx;
        margin-bottom: 24rpx;
    }

    .step-item:last-child {
        margin-bottom: 0;
    }

    .step-content {
        margin-left: 24rpx;
    }

    .step-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
    }

    .highlight {
        color: #1A73E8;
    }

    .step-desc {
        font-size: 28rpx;
        color: #999;
        margin-top: 8rpx;
        display: block;
    }

    .action-btn {
        margin-top: 64rpx;
        background-color: #1A73E8;
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        height: 96rpx;
        line-height: 96rpx;
        border-radius: 48rpx;
        box-shadow: 0 4rpx 12rpx rgba(26, 115, 232, 0.2);
    }

    .action-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 24rpx 32rpx;
        display: flex;
        gap: 24rpx;
        box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.06);

        .nav-button {
            flex: 1;
            height: 96rpx;
            // border-radius: 64rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;
            font-size: 28rpx;
            transition: all 0.2s ease;

            &[disabled] {
                opacity: 0.6;
                filter: grayscale(0.5);
            }
        }

        .secondary {
            background: #f8f9fa;
            color: #666;
            border: 2rpx solid #eee;
        }

        .primary {
            background: linear-gradient(135deg, #1A73E8, #0d47a1);
            color: #fff;
            box-shadow: 0 4rpx 16rpx rgba(26, 115, 232, 0.3);
        }
    }

    .safe-area {
        padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    }
}
</style>