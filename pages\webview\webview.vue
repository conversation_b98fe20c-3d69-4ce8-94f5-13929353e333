<template>
	<view class="webview-container">
		<web-view :src="currentUrl" @message="handleMessage" @load="handleLoad" @error="handleError"></web-view>
	</view>
</template>

<script setup>
	import { ref } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	import wx from '../profile/wx.js'

	const currentUrl = ref('')
	const isAuthMode = ref(false)

	// 页面加载时获取传入的URL参数
	onLoad((options) => {
		if (options.url) {
			currentUrl.value = decodeURIComponent(options.url)
			// 判断是否为微信授权模式
			isAuthMode.value = currentUrl.value.includes('oauth2/authorize') || currentUrl.value.includes('connect/oauth2')
			console.log('加载URL:', currentUrl.value, '授权模式:', isAuthMode.value)
		} else {
			uni.showToast({
				title: '缺少链接地址',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	})



	// 处理webview消息
	const handleMessage = (event) => {
		console.log('收到webview消息:', event)

		// 处理微信授权相关消息
		if (event.detail && event.detail.data) {
			const data = event.detail.data[0] || event.detail.data

			// 处理授权成功消息
			if (data.type === 'wechat_auth_success') {
				console.log('微信授权成功:', data)
				uni.showToast({
					title: '授权成功！',
					icon: 'success'
				})

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			}

			// 处理授权完成消息
			if (data.type === 'wechat_auth_complete') {
				console.log('微信授权流程完成:', data)
				if (data.action === 'back_to_app') {
					uni.navigateBack()
				}
			}
		}
	}

	// 处理页面加载完成
	const handleLoad = (event) => {
		console.log('webview加载完成:', event)
		// 只在授权模式下处理授权逻辑
		if (isAuthMode.value) {
			// 检查URL是否包含授权成功的回调参数
			const loadedUrl = event.detail.src
			if (loadedUrl && loadedUrl.includes('code=')) {
				// 授权成功，提取code参数
				const urlParams = new URLSearchParams(loadedUrl.split('?')[1])
				const code = urlParams.get('code')
				const state = urlParams.get('state')
				
				if (code) {
					console.log('获取到授权码:', code)
					// 这里可以调用后端接口，用code换取openId
					handleAuthSuccess(code, state)
				}
			}
		} else {
			// 文件预览模式，不需要特殊处理
			console.log('文件预览加载完成')
		}
	}

	// 处理授权成功
	const handleAuthSuccess = async (code, state) => {
		try {
			uni.showLoading({
				title: '处理授权信息...',
				mask: true
			})
			
			// 调用后端接口，用code换取openId
			const result = await wx.handleAuthCallback(code, state)
			
			if (result && result.success) {
				uni.showToast({
					title: '微信绑定成功',
					icon: 'success'
				})
				console.log('微信授权成功，openId:', result.openId)
			} else {
				uni.showToast({
					title: result.message || '授权失败',
					icon: 'none'
				})
			}
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
			
		} catch (error) {
			console.error('处理授权失败:', error)
			uni.showToast({
				title: '授权处理失败，请重试',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	}

	// 处理加载错误
	const handleError = (event) => {
		console.error('webview加载错误:', event)
		uni.showToast({
			title: '页面加载失败',
			icon: 'none'
		})
	}
</script>

<style scoped>
	.webview-container {
		width: 100%;
		height: 100vh;
	}
</style>