<template>
    <view class="addDocumentC-container">
        <!-- Main Content -->
        <view class="main-content">
            <view class="form-container">
                <!-- 1. 文档标题 -->
                <view class="form-item">
                    <text class="form-label">1. 文档标题</text>
                    <input class="form-input" type="text" placeholder="请输入文档标题" v-model="docTitle" />
                </view>

                <!-- 2. 文档类别 -->
                <view class="form-item">
                    <text class="form-label">2. 文档类别</text>
                    <picker class="form-select" mode="selector" :range="docTypes" @change="onDocTypeChange">
                        <view class="picker-content">
                            <text>{{ docType }}</text>
                            <uni-icons type="arrowdown" size="16" color="#9b9b9b"></uni-icons>
                        </view>
                    </picker>
                </view>

                <!-- 3. 版本号 -->
                <view class="form-item">
                    <text class="form-label">3. 版本号</text>
                    <input class="form-input" type="text" v-model="version" />
                </view>

                <!-- 4. 生效日期 -->
                <view class="form-item">
                    <text class="form-label">4. 生效日期</text>
                    <picker class="form-date" mode="date" @change="onDateChange">
                        <view class="picker-content">
                            <uni-icons type="calendar" size="16" color="#9b9b9b"></uni-icons>
                            <text>{{ effectiveDate }}</text>
                        </view>
                    </picker>
                </view>

                <!-- 5. 适用范围 -->
                <!-- <view class="form-item">
                    <text class="form-label">5. 适用范围</text>
                    <filter-tags :tags="['全组织', '部门', '岗位']" />
                </view> -->

                <!-- 6. 负责人 -->
                <view class="form-item">
                    <text class="form-label">6. 负责人</text>
                    <view class="form-search">
                        <uni-icons type="search" size="16" color="#9b9b9b"></uni-icons>
                        <input class="search-input" type="text" placeholder="选择责任人" v-model="responsiblePerson" />
                    </view>
                </view>

                <!-- 7. 文档内容 -->
                <view class="form-item">
                    <text class="form-label">7. 文档内容</text>
                    <view class="editor-wrapper">
                        <!-- 富文本编辑器工具栏 -->
                        <view class="editor-toolbar">
                            <button  class="tool-btn" @click="formatText('bold')">
                                <uni-icons type="bold" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                            <button  class="tool-btn" @click="formatText('italic')">
                                <uni-icons type="italic" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                            <button  class="tool-btn" @click="formatText('underline')">
                                <uni-icons type="underline" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                            <view class="tool-divider"></view>
                            <button  class="tool-btn" @click="formatText('ul')">
                                <uni-icons type="list" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                            <button  class="tool-btn" @click="formatText('ol')">
                                <uni-icons type="orderedlist" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                            <view class="tool-divider"></view>
                            <button  class="tool-btn" @click="formatText('link')">
                                <uni-icons type="link" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                            <button  class="tool-btn" @click="insertImage">
                                <uni-icons type="image" size="16" color="#4a4a4a"></uni-icons>
                            </button>
                        </view>
                        <!-- 富文本编辑器内容区 -->
                        <textarea class="editor-content" placeholder="在此输入文档内容..." v-model="docContent"></textarea>
                    </view>
                </view>

                <!-- 8. 附件 -->
                <view class="form-item">
                    <text class="form-label">8. 附件</text>
                    <button  class="add-attachment" @click="addAttachment">
                        <uni-icons type="plus" size="16" color="#9b9b9b"></uni-icons>
                        <text>添加附件</text>
                    </button>
                    <view>
                        <view class="attachment-item" v-for="(item, index) in attachmentList" :key="index">
                            <view class="attachment-info">
                                <uni-icons type="file" size="16" color="#f44336"></uni-icons>
                                <text class="attachment-name">{{ item.fileName }}</text>
                            </view>
                            <view>
                                <button  class="delete-btn" @click="removeAttachment(item.id)">
                                    <uni-icons type="trash" size="16" color="#f44336"></uni-icons>
                                </button>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- Bottom Action Bar -->
        <view class="action-bar">
            <button  class="action-btn cancel" @click="cancel">取消</button>
            <button  class="action-btn draft" @click="saveDraft">保存草稿</button>
            <button  class="action-btn submit" @click="submitReview">提交审核</button>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import filterTags from '@/components/filterTags.vue';

const docTitle = ref('');
const docTypes = ref(['操作手册', '规章制度', '工作流程', '技术文档']);
const docType = ref('操作手册');
const version = ref('v1.0');
const effectiveDate = ref('2025-04-19');
const scopeAll = ref(true);
const scopeDept = ref(true);
const scopePosition = ref(true);
const responsiblePerson = ref('');
const docContent = ref('在此输入文档内容...');

const attachmentList = ref([
    {
        fileName: '附件1.docx',
        fileSize: '10KB',
        fileUrl: 'https://example.com/attachment1.docx',
        id: '1'
    },
    {
        fileName: '附件2.pdf',
        fileSize: '20KB',
        fileUrl: 'https://example.com/attachment2.pdf',
        id: '2'
    },
    {
        fileName: '附件3.jpg',
        fileSize: '30KB',
        fileUrl: 'https://example.com/attachment3.jpg',
        id: '3'
    }
]);

const onDocTypeChange = (e) => {
    docType.value = docTypes.value[e.detail.value];
};

const onDateChange = (e) => {
    effectiveDate.value = e.detail.value;
};

const toggleScope = (type) => {
    if (type === 'all') {
        scopeAll.value = !scopeAll.value;
    } else if (type === 'dept') {
        scopeDept.value = !scopeDept.value;
    } else if (type === 'position') {
        scopePosition.value = !scopePosition.value;
    }
};

const formatText = (type) => {
    console.log('Format text:', type);
    // 实际应用中这里需要实现富文本编辑功能
};

const insertImage = () => {
    console.log('Insert image');
    // 实际应用中这里需要实现图片插入功能
};

const addAttachment = () => {
    attachmentList.value.push({
        id: attachmentList.value.length,
        name: 'Attachment',
        type: 'file',
    });
};

const removeAttachment = (id) => {
    attachmentList.value = attachmentList.value.filter((item) => item.id !== id);
};

const goBack = () => {
    uni.navigateBack();
};

const saveDraft = () => {
    console.log('Save draft');
    uni.showToast({
        title: '已保存草稿',
        icon: 'success'
    });
};

const submitReview = () => {
    console.log('Submit review');
    uni.showToast({
        title: '已提交审核',
        icon: 'success'
    });
};

const cancel = () => {
    uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.addDocumentC-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f8f8f8;
    box-sizing: border-box;
    overflow-x: hidden;

    /* Main Content */
    .main-content {
        flex: 1;
        margin-bottom: 88rpx;
        box-sizing: border-box;
    }

    .form-container {
        box-sizing: border-box;
        background-color: #ffffff;
        border-radius: 16rpx;
        padding: 30rpx;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
    }

    .form-item {
        margin-bottom: 40rpx;
    }

    .form-label {
        display: block;
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 20rpx;
    }

    .form-input,
    .form-select,
    .form-date,
    .form-search {
        box-sizing: border-box;
        width: 100%;
        height: 80rpx;
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #333333;
        background-color: #ffffff;
    }

    .form-select,
    .form-date,
    .form-search {
        display: flex;
        align-items: center;
    }

    .picker-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .scope-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
    }

    .scope-tag {
        padding: 10rpx 30rpx;
        border-radius: 40rpx;
        font-size: 26rpx;
        background-color: #EFEFF4;
        color: #333333;
    }

    .scope-tag.active {
        background-color: #1A73E8;
        color: #ffffff;
    }

    .form-search {
        position: relative;
    }

    .search-input {
        flex: 1;
        padding-left: 60rpx;
    }

    /* Editor */
    .editor-wrapper {
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
    }

    .editor-toolbar {
        display: flex;
        align-items: center;
        padding: 10rpx;
        border-bottom: 1px solid #e0e0e0;
        background-color: #f5f5f5;
    }

    .tool-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        padding: 0;
        margin: 0 5rpx;
    }

    .tool-divider {
        width: 1px;
        height: 30rpx;
        background-color: #e0e0e0;
        margin: 0 15rpx;
    }

    .editor-content {
        width: 100%;
        min-height: 300rpx;
        padding: 20rpx;
        font-size: 28rpx;
        color: #333333;
    }

    /* Attachment */
    .add-attachment {
        width: 100%;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px dashed #e0e0e0;
        border-radius: 8rpx;
        color: #9b9b9b;
        font-size: 28rpx;
        margin-bottom: 20rpx;
        background-color: transparent;
    }

    .attachment-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
    }

    .attachment-info {
        display: flex;
        align-items: center;
    }

    .attachment-name {
        font-size: 28rpx;
        color: #333333;
        margin-left: 20rpx;
    }

    .delete-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        padding: 0;
    }

    /* Action Bar */
    .action-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        background-color: #ffffff;
        box-shadow: 0 -1px 0 0 rgba(0, 0, 0, 0.1);
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1;
    }

    .action-btn {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        margin: 0 16rpx;
    }

    .cancel {
        border: 1px solid #e0e0e0;
        color: #666666;
        background-color: #ffffff;
    }

    .draft {
        background-color: #EFEFF4;
        color: #666666;
    }

    .submit {
        background-color: #1A73E8;
        color: #ffffff;
    }
}
</style>