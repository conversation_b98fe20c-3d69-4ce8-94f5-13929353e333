<template>
  <view class="page-container">

    <!-- 考试列表 -->
    <z-paging class="exam-list" ref="paging" v-model="examList" @query="queryList">
      <template #top>
        	<header-bar shape="circle" prefixIcon="search" clearable @confirm="search" :fixed="false" v-model="searchValue" @search="search()">
				</header-bar>
        <!-- 分类筛选区域 -->
        <view class="filter-container">
          <!-- Tab分类 -->
          <scroll-view class="tab-container" scroll-x>
            <view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', activeTab === index ? 'active' : '']" @click="handleTabChange(index)">
              {{ tab }}
            </view>
          </scroll-view>
        </view>
      </template>
      
      <view v-for="(exam, index) in examList" :key="index" class="exam-card" @click="goToDetail(exam.examId)">
        <view class="exam-header">
          <text class="exam-name">{{ exam.examName }}</text>
          <view :class="['status-tag', exam.examStatus]">
            {{ getStatusText(exam.examStatus) }}
          </view>
        </view>

        <!-- <view class="exam-time">
          <uni-icons type="calendar" size="14" color="#999" />
          <text>{{ exam.startTime }} - {{ exam.endTime }}</text>
        </view> -->
        <view class="exam-details">
          <text class="detail-item" v-if="exam.examDuration">时长: {{ exam.examDuration }}分钟</text>
          <text class="detail-item" v-if="exam.questionCount">题目: {{ exam.questionCount }}题</text>
          <text class="detail-item" v-if="exam.passScore">及格分: {{ exam.passScore }}分</text>
        </view>
        <view class="exam-footer">
          <view class="score-section">
            <text class="score" v-if="exam.score !== null && exam.score !== undefined">
              成绩: {{ exam.score }}分
              <text :class="['pass-status', exam.isPassed ? 'passed' : 'failed']" v-if="exam.status === 'completed'">
                ({{ exam.isPassed ? '合格' : '不合格' }})
              </text>
            </text>
            <text class="score" v-else>暂无成绩</text>
          </view>
          <view class="detail-button">
            <text>查看详情</text>
            <uni-icons type="arrowright" size="14" color="#666" />
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>
<script setup>
import { ref, nextTick, onMounted } from 'vue';
import assessApi from './assess.js';
import headerBar from '@/components/headerBar.vue';

const searchValue = ref(null);
const loading = ref(false);
const tagValue = ref(null);

// z-paging相关
const paging = ref(null);
const examList = ref([]);
// Tab分类
const tabs = ref(['全部', '待考核', '已完成']);
const activeTab = ref(0);

// 搜索方法
function search(){
  reload();
}

// 重新加载数据
function reload() {
  nextTick(() => {
    paging?.value.reload();
  })
}

// 标签变化处理
const handleTagChange = (index) => {
  reload();
}

// Tab切换处理
const handleTabChange = (index) => {
  activeTab.value = index;
  // 根据选择的tab设置tagValue
  if (index === 0) {
    tagValue.value = null; // 全部
  } else if (index === 1) {
    tagValue.value = 'PENDING'; // 待考核
  } else if (index === 2) {
    tagValue.value = 'COMPLETED'; // 已完成
  }
  reload();
}
// 查询考试列表
function queryList(pageNo, pageSize) {
  console.log('查询考试列表:', assessApi);
  loading.value = true;
  
  // 构建请求参数
  const params = {
    page: pageNo - 1, // 接口页码从0开始
    size: pageSize,
    examStatus: tagValue.value,
    searchTerm: searchValue.value || null
  };
  
  // 根据tagValue添加状态筛选
  if (tagValue.value) {
    params.examStatus = tagValue.value;
  }
  
  // 调用API
  assessApi.getExamList(params).then((response) => {
    // 处理返回数据
    if (response && response.content) {
      const processedData = response.content
      
      paging.value.complete(processedData);
    } else {
      paging.value.complete([]);
    }
    loading.value = false;
  }).catch((err) => {
    console.error('获取考试列表失败:', err);
    paging.value.complete(false);
    loading.value = false;
  });
}
// 获取状态文本
const getStatusText = (status) => {
  const statusTextMap = {
    'PENDING': '待考核',
    'ONGOING': '进行中',
    'COMPLETED': '已完成'
  };
  return statusTextMap[status] || '待考核';
};

// 组件挂载时初始化数据
// onMounted(() => {
//   reload(); // z-paging组件会自动触发初始查询，无需手动调用
// });
// 进入详情页
const goToDetail = (examId) => {
  let url = `/pages/component/study/assessDetail?examId=${examId}`;
  console.log(url, 'ppppppp');
  
  uni.navigateTo({
    url: url
  });
};

</script>
<style>
page {
  height: 100%;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f8f8;
}


/* 搜索区域 */
.search-container {
  padding: 12px 16px;
  background-color: #fff;
}

/* 筛选区域 */
.filter-container {
  background-color: #fff;
  padding: 0 16px;
  margin-bottom: 8px;
}

.tab-container {
  white-space: nowrap;
  padding: 12px 0;
}

.tab-item {
  display: inline-block;
  margin-right: 24px;
  font-size: 14px;
  color: #666;
  padding-bottom: 4px;
}

.tab-item.active {
  color: #2979ff;
  font-weight: 500;
  border-bottom: 2px solid #2979ff;
}

/* 考试列表 */
.exam-list {
  flex: 1;
  padding: 0 16px;
}

.exam-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.exam-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-tag.PENDING{
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-tag.ONGOING {
  background-color: #fff2e6;
  color: #fa8c16;
}

.status-tag.COMPLETED {
  background-color: #f6ffed;
  color: #52c41a;
}



.exam-time {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.exam-time text {
  margin-left: 4px;
}

.exam-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.detail-item {
  font-size: 11px;
  color: #999;
  background-color: #f8f8f8;
  padding: 2px 6px;
  border-radius: 3px;
}

.exam-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.score-section {
  flex: 1;
}

.score {
  font-size: 12px;
  color: #666;
}

.pass-status {
  font-size: 11px;
  margin-left: 4px;
}

.pass-status.passed {
  color: #52c41a;
}

.pass-status.failed {
  color: #ff4d4f;
}

.detail-button {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.detail-button text {
  margin-right: 4px;
}


</style>
