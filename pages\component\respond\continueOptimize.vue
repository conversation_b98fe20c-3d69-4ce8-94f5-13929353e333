<template>
    <view class="list-responsibility-container">
        <!-- 顶部导航栏 -->
        <header-bar>
            <template #right>
                <!-- 自定义操作按钮 -->
                <view class="nav-right">
                    <view class="nav-btn">
                        <uni-icons type="search" size="20" />
                    </view>
                    <view class="nav-btn" @click="handleAdd">
                        <text class="btn-text">＋新增</text>
                    </view>
                </view>
            </template>
        </header-bar>
        <filter-tags :tags="tabs" :active-index="activeIndex" @change="handleTagChange" />

        <!-- 内容区域 -->
        <base-list :list-data="dutyList" @item-click="handleItemClick" />
        <go-navitor url="/pages/component/respond/complianceReview" />
        <popUp :visible="showModal" title="岗位详情" @close="showModal = false">
            <template #footerBtn>
                <view class="action-bar">
                    <button  class="action-button close" @tap="handleClose">
                    关闭
                </button>
                <button  class="action-button edit" @tap="handleEdit">
                    编辑
                </button>
                <button  class="action-button delete" @tap="handleDelete">
                    删除
                </button>
                </view>
            </template>
        </popUp>
    </view>
</template>

<script setup>
import goNavitor from '../../../components/goNavitor.vue';
import { ref } from 'vue';
import headerBar from '@/components/headerBar.vue'
import filterTags from '@/components/filterTags.vue'
import baseList from '@/components/baseList.vue'
import popUp from '@/components/bottomPop.vue';


const activeIndex = ref(0);
const tabs = ref(['全部', '未启动', '进行中', '已完成']);

const showModal = ref(false);

const handleClose = () => {
    showModal.value = false
}
const handleTagChange = (index) => {
    activeIndex.value = index
};

const dutyList = ref([
    {
        title: '产品经理岗位职责',
        department: '产品部',
        responsible: '张明远',
        date: '2023-05-12'
    },
    {
        title: '人力资源专员职责',
        department: '人力资源部',
        responsible: '陈志强',
        date: '2023-03-15'
    },
    {
        title: '财务主管岗位职责',
        department: '财务部',
        responsible: '刘芳',
        date: '2023-02-28'
    },
    {
        title: '客户服务经理职责',
        department: '客户服务部',
        responsible: '孙伟',
        date: '2023-08-09'
    },
    {
        title: '数据分析师岗位职责',
        department: '数据科学部',
        responsible: '周静怡',
        date: '2023-01-25'
    }
]);



const handleAdd = () => {
    // 新增职责逻辑
    showModal.value = !showModal.value;
    console.log('新增职责');

};

const handleItemClick = (item) => {
    // 点击职责项逻辑
    console.log('点击职责项:', item);
};

const handleTouchStart = (index) => {
    // 触摸开始效果
    const item = document.querySelectorAll('.list-item')[index];
    if (item) {
        item.style.transform = 'scale(0.98)';
        item.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
    }
};

const handleTouchEnd = (index) => {
    // 触摸结束效果
    const item = document.querySelectorAll('.list-item')[index];
    if (item) {
        item.style.transform = 'scale(1)';
        item.style.boxShadow = '0 1px 4px rgba(0,0,0,0.05)';
    }
};
</script>

<style lang="scss" scoped>
@import '/static/css/nav.scss';
@import '/static/css/buttons.scss';

.list-responsibility-container {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    background-color: #F5F7FA;
}


.drag-handle-container {
    width: 100%;
    padding-top: 12rpx;
    display: flex;
    justify-content: center;
}

.drag-handle {
    width: 64rpx;
    height: 8rpx;
    background-color: #CCCCCC;
    border-radius: 4rpx;
}

.header {
    height: 88rpx;
    padding: 0 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.back-button,
.edit-button {
    width: 88rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    margin: 0;
    padding: 0;
}

.title {
    font-size: 16px;
    font-weight: bold;
    color: #4a4a4a;
}

.content {
    flex: 1;
    padding: 0 32rpx;
    overflow: auto;
}

.info-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title {
    font-size: 14px;
    font-weight: bold;
    color: #1A73E8;
    margin-bottom: 16rpx;
    display: block;
}

.info-text {
    font-size: 14px;
    color: #4a4a4a;
    margin-bottom: 16rpx;
    display: block;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
}

.copy-button {
    background-color: transparent;
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.bullet-point {
    font-size: 14px;
    color: #4a4a4a;
    margin-bottom: 16rpx;
    display: block;
}

.bullet-point::before {
    content: "•";
    margin-right: 16rpx;
}

.attachment-header {
    padding-bottom: 16rpx;
}

.divider {
    height: 2rpx;
    background-color: #f0f0f0;
    margin: 0 -32rpx;
}

.attachment-item {
    padding: 24rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attachment-left {
    display: flex;
    align-items: center;
}

.attachment-name {
    font-size: 14px;
    color: #1A73E8;
    margin-left: 24rpx;
}

</style>