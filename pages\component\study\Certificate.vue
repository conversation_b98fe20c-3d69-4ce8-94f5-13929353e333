<template>
	<view class="certificate-container">
		<z-paging class="paging-content" ref="paging" v-model="certificates" @query="queryList">
			<template #top>
			</template>
			<!-- Main Content -->
			<view class="main-content">
				<view class='main-content-list'>
					<!-- Certificate List -->
					<view class="certificate-list">
						<view class="certificate-item" @click="getCertificateDetail(item.id)"
							v-for="(item, index) in certificates" :key="index">
							<view class="certificate-info">
								<text class="certificate-name">{{ item.certificateName }}</text>
								<text class="certificate-date">颁发日期 {{ item.validFrom }} · 有效期至
									{{ item.validTo }}</text>
							</view>
							<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			<popUp :visible="showModal" title="证书详情" @close="showModal = false">
				<template>
					<view class="info-card">
						<view class="info-item">
							<text class="bullet">•</text>
							<view class="info-content">
								<text class="info-label">证书名称：</text>
								<text class="info-value">{{cerObj.certificateName }}</text>
							</view>
						</view>
						<view class="info-item">
							<text class="bullet">•</text>
							<view class="info-content">
								<text class="info-label">颁发机构：</text>
								<text class="info-value">{{ cerObj.issuingOrganization }}</text>
							</view>
						</view>
						<view class="info-item">
							<text class="bullet">•</text>
							<view class="info-content">
								<text class="info-label">颁发日期：</text>
								<text class="info-value">{{ cerObj.achievedAt }}</text>
							</view>
						</view>
						<view class="info-item">
							<text class="bullet">•</text>
							<view class="info-content">
								<text class="info-label">有效期至：</text>
								<text class="info-value">{{ cerObj.validFrom }} ~ {{ cerObj.validTo }}</text>
							</view>
						</view>
						<view class="info-item">
							<text class="bullet">•</text>
							<view class="info-content">
								<text class="info-label">状态：</text>
							<text :class="['status-badge', getCertificateStatusClass(cerObj.certificateStatus)]">{{ getCertificateStatusText(cerObj.certificateStatus) }}</text>
							</view>
						</view>
					</view>

					<!-- 证书预览块 -->
					<view class="preview-card">
						<text class="preview-title">证书预览</text>
						<view class="preview-thumbnail" @tap="showFullPreviewFun">
							<!-- {{cerObj.certificateTemplate }} -->
							<image class="preview-image" mode="aspectFill"
								:src="certificateImageUrl || cerObj.certificatePreviewUrl">
							</image>
						</view>
						<text class="preview-hint">点击全屏预览</text>
					</view>

					<!-- PDF预览块 -->
					<view class="preview-card" v-if="cerObj.certificateFileUrl">
						<text class="preview-title">PDF文件</text>
						<view class="pdf-preview" @tap="openPdfPreview">
							<view class="pdf-icon">
								<uni-icons type="paperplane" size="48" color="#FF6B6B"></uni-icons>
							</view>
							<text class="pdf-text">点击查看PDF证书</text>
						</view>
					</view>

					<!-- 证书描述块 -->
					<view class="description-card">
						<text class="description-title">证书描述</text>
						<text class="description-content">
							{{cerObj.certificateDescription || '暂无描述信息'}}
						</text>
					</view>
				</template>
				<!-- <template #footerBtn>
					<view class="action-bar">
						<button class="action-button close" @tap="handleClose">
							关闭
						</button>
						<button class="action-button edit" @tap="handleDownload">
							下载
						</button>
						<button class="action-button share" @tap="handleShare">
							分享
						</button>
					</view>
				</template> -->
			</popUp>

			<view v-if="showFullPreview" class="fullscreen-wrapper" @tap="closeFullPreview">
				<image class="fullscreen-image" :src="currentPreviewImage" mode="widthFix"></image>
				<view class="close-btn" @tap="closeFullPreview">
					<uni-icons type="closeempty" size="32" color="#fff"></uni-icons>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed
	} from 'vue';
	import https from '@/api/study/index.js';
	import popUp from '@/components/bottomPop.vue';
	import getDictData from '@/utils/dict.js';
	import uploadApi from '@/api/upload.js';

	const showModal = ref(false)
	const paging = ref(null);
	const searchTerm = ref(''); // 搜索关键词
	const activeIndex = ref(0); // 当前选中的标签索引
	const cerObj = ref({}); // 证书对象
	const totalPages = ref(0); // 总页数

	// 新增状态控制
	const showFullPreview = ref(false);
	const currentPreviewImage = ref('https://ai-public.mastergo.com/ai/img_res/5e27f8fe6114b7427c053d1927a4a403.jpg');
	const cerList = ref([]); // 证书列表数据
	const certificateStatusDict = ref({}); // 证书状态字典
	const certificateImageUrl = ref(''); // 证书预览图片真实地址
	const certificatePdfUrl = ref(''); // 证书PDF文件真实地址
	function queryList(pageNo, pageSize) {
		uni.showLoading({
			title: '加载中...'
		});
		var params = {
			page: pageNo - 1,
			size: pageSize,
			searchTerm: searchTerm.value,
			// status: 1,
			// tenantId: userStore.tenantId
		}
		https.getCertificateList(
			params,
		).then(res => {
			paging.value.complete(res.content)
			totalPages.value = res.totalElements
			uni.hideLoading();
		}).catch(err => {
			paging.value.complete(false);
			uni.hideLoading();
		})
	}

	function getCertificateDetail(id) {
		https.getCertificateDetail(id).then(res => {
			cerObj.value = res;
			// 获取证书预览图片真实地址
			if (res.certificatePreviewUrl) {
				getCertificateImageUrl(res.certificatePreviewUrl);
			}
			// 获取证书PDF文件真实地址
			if (res.certificateFileUrl) {
				getCertificatePdfUrl(res.certificateFileUrl);
			}
			showModal.value = true;
		}).catch(err => {
			console.error('获取证书详情失败:', err);
		});
	}

	// 获取证书预览图片真实地址
	async function getCertificateImageUrl(key) {
		try {
			if (key) {
				const result = await uploadApi.getFileUrl(key);
				certificateImageUrl.value = result.data || result.url || result;
				// 同时更新全屏预览的图片地址
				currentPreviewImage.value = certificateImageUrl.value;
			}
		} catch (err) {
			console.error('获取证书图片地址失败:', err);
			// 如果获取失败，使用原始地址作为备用
			certificateImageUrl.value = key;
			currentPreviewImage.value = key;
		}
	}

	// 获取证书PDF文件真实地址
	async function getCertificatePdfUrl(key) {
		try {
			if (key) {
				const result = await uploadApi.getFileUrl(key);
				certificatePdfUrl.value = result.data || result.url || result;
			}
		} catch (err) {
			console.error('获取证书PDF地址失败:', err);
			// 如果获取失败，使用原始地址作为备用
			certificatePdfUrl.value = key;
		}
	}

	// 打开PDF预览
	async function openPdfPreview() {
		try {
			if (!certificatePdfUrl.value) {
				uni.showToast({
					title: 'PDF文件地址获取中，请稍后重试',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({
				title: '正在下载PDF文件...'
			});

			// 先下载PDF文件到本地
			uni.downloadFile({
				url: certificatePdfUrl.value,
				success: function(downloadRes) {
					if (downloadRes.statusCode === 200) {
						uni.hideLoading();
						uni.showLoading({
							title: '正在打开PDF...'
						});
						
						// 使用本地文件路径打开PDF
						uni.openDocument({
							filePath: downloadRes.tempFilePath,
							fileType: 'pdf',
							success: function(openRes) {
								uni.hideLoading();
								console.log('PDF打开成功');
							},
							fail: function(openErr) {
								uni.hideLoading();
								console.error('PDF打开失败:', openErr);
								uni.showToast({
									title: 'PDF打开失败，可能不支持此文件格式',
									icon: 'none',
									duration: 3000
								});
							}
						});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: 'PDF文件下载失败',
							icon: 'none'
						});
					}
				},
				fail: function(downloadErr) {
					uni.hideLoading();
					console.error('PDF下载失败:', downloadErr);
					uni.showToast({
						title: 'PDF文件下载失败，请检查网络连接',
						icon: 'none',
						duration: 3000
					});
				}
			});
		} catch (err) {
			console.error('打开PDF时发生错误:', err);
			uni.hideLoading();
			uni.showToast({
				title: '打开PDF时发生错误',
				icon: 'none'
			});
		}
	}

	// 获取证书状态字典
	async function loadCertificateStatusDict() {
		try {
			const dictData = await getDictData('84');
			if (dictData && dictData.length > 0) {
				const statusMap = {};
				dictData.forEach(item => {
					statusMap[item.value] = item.name;
				});
				certificateStatusDict.value = statusMap;
			}
		} catch (err) {
			console.error('获取证书状态字典失败:', err);
		}
	}

	// 获取证书状态显示文本
	function getCertificateStatusText(status) {
		return certificateStatusDict.value[status] || status;
	}

	// 获取证书状态样式类
	function getCertificateStatusClass(status) {
		const statusText = getCertificateStatusText(status);
		switch (statusText) {
			case '有效':
				return 'status-valid';
			case '即将过期':
				return 'status-expiring';
			case '已过期':
				return 'status-expired';
			case '已撤销':
				return 'status-revoked';
			default:
				return 'status-badge';
		}
	}

	// 组件挂载时加载字典
	onMounted(() => {
		loadCertificateStatusDict();
	});
	// 显示全屏（修改原方法）
	const showFullPreviewFun = () => {
		showFullPreview.value = true;
	}

	// 关闭全屏（修改原方法）
	const closeFullPreview = () => {
		showFullPreview.value = false;
	}


	function handleClose() {
		showModal.value = false
	}

	function handleDownload() {
		// 下载证书的逻辑
		console.log('下载证书')
	}

	function handleShare() {
		// 分享证书的逻辑
		console.log('分享证书')
	}
	// 处理标签点击
	const handleTagClick = (index) => {
		activeIndex.value = index
	}

	const certificates = ref([{
			name: '产品经理高级认证',
			issueDate: '2023-06-15',
			expireDate: '2025-12-31'
		},
		{
			name: '用户体验设计师认证',
			issueDate: '2023-03-22',
			expireDate: '2025-03-22'
		},
		{
			name: '前端开发工程师认证',
			issueDate: '2022-11-10',
			expireDate: '2024-11-10'
		},
		{
			name: '数据分析师中级认证',
			issueDate: '2023-01-05',
			expireDate: '2025-01-05'
		},
		{
			name: '人工智能基础认证',
			issueDate: '2022-09-18',
			expireDate: '2024-09-18'
		},
	]);
</script>

<style lang="scss" scoped>
	@import '/static/css/nav.scss';
	@import '/static/css/buttons.scss';

	.certificate-container {
		display: flex;
		flex-direction: column;
		min-height: 100%;
		background-color: #F5F7FA;

		.main-content-list {
			padding: 32rpx;
		}

		.overview-card {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 32rpx;
			margin-top: 32rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		}

		.overview-title {
			font-size: 36rpx;
			color: #1A73E8;
		}

		.button-group {
			display: flex;
			justify-content: space-between;
			margin-top: 32rpx;
		}

		.secondary-button {
			flex: 1;
			margin-right: 16rpx;
			background-color: #EFEFF4;
			color: #333;
			font-size: 28rpx;
			height: 72rpx;
			line-height: 72rpx;
			border-radius: 36rpx;
		}

		.primary-button {
			flex: 1;
			margin-left: 16rpx;
			background-color: #1A73E8;
			color: #fff;
			font-size: 28rpx;
			height: 72rpx;
			line-height: 72rpx;
			border-radius: 36rpx;
		}

		.filter-tabs {
			display: flex;
			background-color: #fff;
			height: 88rpx;
			align-items: center;
			padding: 0 32rpx;
			margin-top: 32rpx;
			overflow-x: auto;
			white-space: nowrap;
		}

		.filter-tabs button {
			font-size: 28rpx;
			font-weight: 500;
			padding: 0 32rpx;
			height: 56rpx;
			line-height: 56rpx;
			border-radius: 28rpx;
			margin-right: 16rpx;
		}

		.tab-inactive {
			background-color: #EFEFF4;
			color: #666;
		}

		.active {
			background-color: #1A73E8;
			color: #fff;
		}

		.certificate-list {
			margin-top: 16rpx;
		}

		.certificate-item {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 32rpx;
			margin-bottom: 16rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		}

		.certificate-info {
			flex: 1;
		}

		.certificate-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			display: block;
		}

		.certificate-date {
			font-size: 24rpx;
			color: #999;
			margin-top: 8rpx;
			display: block;
		}

		.info-card,
		.preview-card,
		.description-card {
			background-color: #ffffff;
			border-radius: 16rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
			padding: 32rpx;
			margin-bottom: 24rpx;
		}

		.info-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 24rpx;
		}

		.info-item:last-child {
			margin-bottom: 0;
		}

		.bullet {
			color: #1A73E8;
			font-size: 28rpx;
			margin-right: 16rpx;
		}

		.info-content {
			flex: 1;
		}

		.info-label,
		.info-value {
			font-size: 28rpx;
			color: #1a1a1a;
		}

		.info-value {
			font-weight: 500;
		}

		.status-badge {
			border-radius: 16rpx;
			padding: 4rpx 16rpx;
			font-size: 28rpx;
			font-weight: 500;
			display: inline-block;
		}

		// 有效状态 - 绿色
		.status-valid {
			background-color: #F6FFED;
			color: #52C41A;
			border: 1rpx solid #B7EB8F;
		}

		// 即将过期状态 - 橙色
		.status-expiring {
			background-color: #FFF7E6;
			color: #FA8C16;
			border: 1rpx solid #FFD591;
		}

		// 已过期状态 - 红色
		.status-expired {
			background-color: #FFF2F0;
			color: #FF4D4F;
			border: 1rpx solid #FFCCC7;
		}

		// 已撤销状态 - 灰色
		.status-revoked {
			background-color: #F5F5F5;
			color: #8C8C8C;
			border: 1rpx solid #D9D9D9;
		}

		// 默认状态 - 蓝色（保持原样式）
		.status-badge:not(.status-valid):not(.status-expiring):not(.status-expired):not(.status-revoked) {
			background-color: #E6F4FF;
			color: #1A73E8;
			border: 1rpx solid #91D5FF;
		}

		.preview-title,
		.description-title {
			font-size: 28rpx;
			font-weight: bold;
			color: #1a1a1a;
			margin-bottom: 24rpx;
			display: block;
		}

		.preview-thumbnail {
			height: 360rpx;
			border-radius: 16rpx;
			overflow: hidden;
			margin-bottom: 16rpx;
		}

		.preview-image {
			width: 100%;
			height: 100%;
		}

		.preview-hint {
			font-size: 28rpx;
			color: #999999;
			text-align: center;
			display: block;
		}

		.pdf-preview {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 200rpx;
			border: 2rpx dashed #E0E0E0;
			border-radius: 16rpx;
			background-color: #FAFAFA;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.pdf-preview:hover {
			border-color: #FF6B6B;
			background-color: #FFF5F5;
		}

		.pdf-icon {
			margin-bottom: 16rpx;
		}

		.pdf-text {
			font-size: 28rpx;
			color: #666666;
			font-weight: 500;
		}

		.description-content {
			font-size: 28rpx;
			color: #666666;
			line-height: 1.6;
		}

		.card-title {
			font-size: 14px;
			font-weight: bold;
			color: #1A73E8;
			margin-bottom: 16rpx;
			display: block;
		}

		.info-text {
			font-size: 14px;
			color: #4a4a4a;
			margin-bottom: 16rpx;
			display: block;
		}

		.info-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
		}

		.fullscreen-wrapper {
			position: fixed;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			background: rgba(0, 0, 0, 0.9);
			z-index: 99999;
			display: flex;
			justify-content: center;
			align-items: center;

			.fullscreen-image {
				width: 100%;
				max-width: 750rpx;
			}

			.close-btn {
				position: absolute;
				top: 40rpx;
				right: 40rpx;
				padding: 16rpx;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 50%;
			}
		}
	}
</style>