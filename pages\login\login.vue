<template>
	<view class="login-container">
		<!-- 顶部装饰区 -->
		<view class="header-bg">
			<view class="logo-container">
				<view class="logo-circle">
					<image style="width: 100%; height: 100%"
						src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png">
					</image>
					<!-- <text class="logo-text">logo</text> -->
				</view>
			</view>
			<view class="welcome-text">
				<text class="welcome-title">猫伯伯智能合规管家</text>
				<!-- <text class="welcome-subtitle">让合规插上科技的翅膀</text> -->

			</view>
		</view>

		<!-- 登录表单区 -->
		<view class="login-form">
			<!-- 企业代码输入 -->
			<!-- <view class="input-group">
				<uni-icons type="pyq" size="20" color="#9CA3AF" class="input-icon"></uni-icons>
				<input v-model="form.companyCode" type="text" placeholder="请输入用户名" class="input-field"
					placeholder-class="placeholder" />
			</view> -->

			<!-- 手机号输入 -->
			<!-- <view class="input-group">
				<uni-icons type="phone" size="20" color="#9CA3AF" class="input-icon"></uni-icons>
				<input v-model="form.phone" type="number" placeholder="请输入手机号" class="input-field"
					placeholder-class="placeholder" />
			</view> -->

			<!-- 账户 -->
			<view class="input-group">
				<uni-icons type="person" size="20" color="#9CA3AF" class="input-icon"></uni-icons>
				<input v-model="form.username" type="text" placeholder="请输入用户名" class="input-field"
					placeholder-class="placeholder" />
			</view>

			<!-- 密码输入 -->
			<view class="input-group">
				<uni-icons type="locked" size="20" color="#9CA3AF" class="input-icon"></uni-icons>
				<input v-model="form.password" :password="showPassword" type="text" placeholder="请输入密码"
					class="input-field" placeholder-class="placeholder" />
				<uni-icons :type="showPassword ? 'eye-slash' : 'eye'" size="20" color="#9CA3AF" class="toggle-icon"
					@click="showPassword=!showPassword"></uni-icons>
			</view>

			<!-- 验证码登录切换 -->
			<!-- <view class="switch-login">
				<text class="switch-text" @click="switchToCodeLogin">验证码登录</text>
			</view> -->
		</view>

		<!-- 登录按钮 -->
		<button class="base-button login-btn" @click="handleLogin">登录</button>

		<!-- 忘记密码 -->
		<view class="forget-password">
			<text class="forget-text" @click="handleForget">忘记密码？</text>
		</view>

		<!-- 第三方登录 -->
		<view class="third-party">
			<view class="divider">
				<view class="divider-line"></view>
				<text class="divider-text">或使用以下方式登录</text>
				<view class="divider-line"></view>
			</view>
			<view class="third-icon">
				<!-- open-type="getPhoneNumber" -->
				<button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber" class="icon-btn">
					<uni-icons type="weixin" size="48" color="#09BB07"></uni-icons>
				</button>
			</view>
		</view>
		<!-- 底部协议 -->
		<view class="footer">
			<view class="agreement-container">
				<view class="checkbox-container" @click="toggleAgreement">
					<view class="checkbox" :class="{ 'checked': isAgreed }">
						<uni-icons v-if="isAgreed" type="checkmarkempty" size="16" color="#FFFFFF"></uni-icons>
					</view>
					<text class="footer-text">我已阅读并同意</text>
				</view>
				<text class="footer-link" @click="handlePrivacyAgreement">《隐私协议》</text>
			</view>
		</view>
		<view class="slogan-text">
			<text>让管理插上合规的翅膀，让合规插上科技的翅膀</text>
		</view>
		
		<!-- 合规承诺书弹窗 -->
		<uni-popup ref="commitmentPopup" type="center" background-color="transparent" :mask-click="false">
			<view class="commitment-popup">
				<view class="commitment-title">合规承诺书</view>
				<scroll-view scroll-y class="commitment-content">
					<rich-text :nodes="commitmentContent" style="width: 100%;"></rich-text>
				</scroll-view>
				<view class="commitment-footer">
					<button class="commitment-btn" :loading="commitmentLoading" @click="confirmCommitment">我已阅读并同意</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import loginApi from '@/api/login/login.js'
	import orgApi from '@/api/org/index.js'
	import {
		useUserStore
	} from '@/store/pinia.js';
	const userStore = useUserStore();
	
	// 合规承诺书弹窗相关
	const commitmentPopup = ref(null);
	const commitmentContent = ref('');
	const commitmentLoading = ref(false);
	
	// 加载承诺书内容
	const loadCommitmentContent = () => {
		commitmentContent.value = `
		<div class="commitment" style="width:100%; max-width:100%; overflow-wrap:break-word;">
		  <h2 style="text-align:center; font-size:32rpx; margin-bottom:20rpx;">猫伯伯合规管家系统使用合规承诺书</h2>
		  <p style="margin-bottom:12rpx;"><strong>尊敬的用户：</strong></p>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">欢迎使用猫伯伯合规管家系统！为确保所有使用者（包括所在企业租户的普通员工、管理人员及其他授权人员）能够合法、合规、安全地使用本系统，特制定本合规承诺书。请您在首次登录系统前认真阅读并确认同意本承诺书全部条款。</p>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">一、承诺范围</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">本人（以下亦称"用户"）明确知晓并承诺严格遵守中华人民共和国现行的法律、行政法规、部门规章及其他规范性文件，以及本人所属企业（租户）的内部规章制度，并严格遵守猫伯伯合规管家系统的各项规则和使用要求。</p>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">二、信息真实性承诺</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">本人保证在猫伯伯合规管家系统内填写、上传或生成的所有信息均真实、准确、完整，不存在虚假、误导或重大遗漏，并将在信息变更时及时更新。若因信息不真实、不完整或未及时更新造成任何损失或法律后果，由本人承担全部责任。</p>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">三、合法合规使用承诺</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">本人承诺在使用猫伯伯合规管家系统过程中，不会从事或协助他人从事以下任何行为：</p>
		  <ul style="padding-left:30rpx; margin-bottom:12rpx;">
			<li style="font-size:28rpx; margin-bottom:8rpx;">违反宪法及法律、行政法规、部门规章等规范性文件的行为；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">侵犯他人知识产权、商业秘密、个人信息或其他合法权益的行为；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">利用系统散布虚假信息、实施商业欺诈、传输违禁内容或扰乱社会秩序的行为；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">破坏、攻击、入侵或以其他方式干扰系统或相关网络、软件、硬件、数据的安全与完整性；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">违反所属企业（租户）规章制度或猫伯伯合规管家系统使用规则的行为；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">其他法律、法规、规章或政策明令禁止的行为。</li>
		  </ul>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">四、数据安全与保密承诺</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">本人承诺妥善保管系统账号、密码及多因素认证信息，不向任何未经授权的第三方透露、出租、出借或转让。如因账号管理不善导致账号被盗用、数据泄露或造成其他损失，本人将承担全部责任。本人同时承诺严格遵守企业及系统的数据分类分级、信息安全及保密制度，不得擅自导出、披露或用于超出授权范围的目的。</p>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">五、责任追究</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">如本人违反本承诺书任一条款，愿意自行承担全部法律责任，并同意猫伯伯合规管家系统及所属企业（租户）视情节轻重采取包括但不限于以下措施：暂停或终止账户使用、删除违规信息、内部行政处分、追究民事赔偿责任、移交司法机关处理等。</p>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">六、确认与生效</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">点击 "我已阅读并同意" 按钮，即视为本人已充分阅读、理解并接受本承诺书全部内容，并将其作为电子签署予以确认。本承诺书自本人点击确认之时起即时生效，对本人具有法律效力。系统将自动记录以下信息作为电子签署凭证：</p>
		  <ul style="padding-left:30rpx; margin-bottom:12rpx;">
			<li style="font-size:28rpx; margin-bottom:8rpx;">用户账号及唯一标识；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">真实姓名或实名信息（如适用）；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">IP 地址及设备信息；</li>
			<li style="font-size:28rpx; margin-bottom:8rpx;">系统服务器记录的时间戳。</li>
		  </ul>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">根据《中华人民共和国民法典》《中华人民共和国电子签名法》等相关法律法规，上述电子确认与手写签名具有同等法律效力。</p>
		  <h3 style="font-size:28rpx; margin:16rpx 0;">七、联系我们</h3>
		  <p style="margin-bottom:12rpx; font-size:28rpx;">如您对本承诺书内容有任何疑问，请联系所属企业管理员或猫伯伯合规管家客服热线：400-166-5291（服务时间：每天 09:00-21:00）。</p>
		  <p style="text-align:right; font-size:28rpx;">感谢您的配合！</p>
		</div>
		`;
	};
	
	// 初始化时加载承诺书内容
	loadCommitmentContent();
	
	// 检查用户是否已签约承诺书
	const checkCommitmentStatus = () => {
		loginApi.checkEmployeeCommitment().then((res) => {
			if (!res || res === false) {
				// 未签约，显示承诺书弹窗
				commitmentPopup.value.open();
			} else {
				// 已签约，跳转到首页
				let pages = getCurrentPages();
				if (pages.length > 1) {
					uni.navigateBack();
				} else {
					uni.reLaunch({
						url: `/pages/home/<USER>
					});
				}
			}
		}).catch((err) => {
			console.error('检查承诺书状态失败:', err);
			// 出错时也显示承诺书弹窗
			commitmentPopup.value.open();
		});
	};
	
	// 确认承诺书
	const confirmCommitment = () => {
		commitmentLoading.value = true;
		
		const commitmentData = {
			type: 1, // 类别：1、合规承诺
			filePath: 'https://dev.static.mbbhg.com/whiskerguard-front/%E7%8C%AB%E4%BC%AF%E4%BC%AF%E5%90%88%E8%A7%84%E7%AE%A1%E5%AE%B6%E7%B3%BB%E7%BB%9F%E4%BD%BF%E7%94%A8%E5%90%88%E8%A7%84%E6%89%BF%E8%AF%BA%E4%B9%A6.doc', // 文件地址，可以为空
			isSigned: true // 已签名
		};
		
		loginApi.createCommitment(commitmentData).then(() => {
			commitmentLoading.value = false;
			commitmentPopup.value.close();
			uni.$uv.toast(`承诺书签署成功`);
			
			// 签署成功后跳转到首页
			let pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack();
			} else {
				uni.reLaunch({
					url: `/pages/home/<USER>
				});
			}
		}).catch((err) => {
			commitmentLoading.value = false;
			console.error('承诺书签署失败:', err);
			uni.$uv.toast(`承诺书签署失败，请重试`);
		});
	};
	
	// 微信登录code
	const wechatCode = ref('');
	
	// 预先获取微信登录code
	const getWechatCode = () => {
		uni.login({
			success: (loginRes) => {
				if (loginRes.code) {
					wechatCode.value = loginRes.code;
					console.log('预先获取微信code成功:', loginRes.code);
				} else {
					console.error('预先获取微信code失败:', loginRes.errMsg);
				}
			},
			fail: (err) => {
				console.error('预先获取微信code失败:', err);
			}
		});
	}
	
	// 微信快捷登录
	const onGetPhoneNumber = (e) => {
		console.log('获取手机号', e)
		if (e.detail.errMsg === 'getPhoneNumber:ok') {
			// 检查用户协议同意状态
			if (!isAgreed.value) {
				uni.$uv.toast(`请先阅读并勾选《隐私协议》，即可继续登录。`);
				return
			}
			
			// 检查是否有可用的微信code
			if (!wechatCode.value) {
				uni.showToast({
					title: '微信登录凭证失效，请重试',
					icon: 'none'
				});
				// 重新获取code
				getWechatCode();
				return;
			}
			
			// 获取手机号成功，开始微信登录流程
			uni.showLoading({
				title: '登录中...',
				mask: true,
			})
			console.log('微信手机号登录参数', uni.getSystemInfoSync())
			
			// 构造微信手机号登录参数
			const wechatLoginParams = {
				loginType: 'WECHAT_PHONE',
				code: wechatCode.value,
				encryptedData: e.detail.encryptedData,
				iv: e.detail.iv,
				deviceId: uni.getSystemInfoSync().deviceId,
			}
			
			// 调用微信手机号登录接口
			loginApi.xcx_appletLogin(wechatLoginParams).then(async (res) => {
				console.log('微信手机号登录结果', res)
				if (res && res.id) {
					uni.$uv.toast(`登录成功`);
					userStore['token'] = res['token']
					userStore['userId'] = res['userId']
					userStore['tenantId'] = res['tenantId']
					userStore['user'] = res

					// 获取用户详细信息
					try {
						const userDetail = await orgApi.getEmpById(res.userId);
						console.log('获取用户详细信息', userDetail)

						userStore['userDetail'] = userDetail;

						// 检查roleList中是否包含律师角色
						if (userDetail && userDetail.roleList) {
							console.log('mmmmmmmmmmmmm')
							const isLawyer = userDetail.roleList.some(role => role
								.name && role.name.includes('律师'));
							userStore['lawyer'] = isLawyer;
						} else {
							userStore['lawyer'] = false;
						}
					} catch (error) {
						console.error('获取用户详细信息失败:', error);
						userStore['lawyer'] = false;
					}

					uni.hideLoading()
					
					// 登录成功后检查用户是否已签约承诺书
					checkCommitmentStatus();
				} else {
					uni.hideLoading()
					uni.$uv.toast(`微信登录失败，请重试`);
					// 清空code，下次重新获取
					wechatCode.value = '';
				}
			}).catch((err) => {
				uni.hideLoading()
				console.error('微信登录错误:', err)
				uni.$uv.toast(`微信登录失败`);
				// 清空code，下次重新获取
				wechatCode.value = '';
			})
		} else {
			uni.showToast({
				title: '获取手机号失败',
				icon: 'none'
			})
		}
	}

	onLoad((options) => {
		console.log('页面加载', options)
		let pages = getCurrentPages(); // 获取当前页面栈
		console.log(pages.length, 666);
		
		// 读取上次保存的账号密码
		loadSavedCredentials();
		// 预先获取微信登录code
		getWechatCode();
		// 加载承诺书内容
		loadCommitmentContent();
	})


	// 表单数据
	const form = ref({
		// companyCode: '',
		// phone: '',
		// password: '',
		username: "",
		password: "",
	})
	
	// 读取保存的账号密码
	const loadSavedCredentials = () => {
		try {
			const savedUsername = uni.getStorageSync('saved_username');
			const savedPassword = uni.getStorageSync('saved_password');
			
			if (savedUsername) {
				form.value.username = savedUsername;
			}
			if (savedPassword) {
				form.value.password = savedPassword;
			}
			
			console.log('已加载保存的账号密码');
		} catch (error) {
			console.error('读取保存的账号密码失败:', error);
		}
	}
	
	// 保存账号密码
	const saveSavedCredentials = () => {
		try {
			uni.setStorageSync('saved_username', form.value.username);
			uni.setStorageSync('saved_password', form.value.password);
			console.log('已保存账号密码');
		} catch (error) {
			console.error('保存账号密码失败:', error);
		}
	}

	// 密码可见性
	const showPassword = ref(true)

	// 用户协议同意状态
	const isAgreed = ref(false)

	// 切换协议同意状态
	const toggleAgreement = () => {
		isAgreed.value = !isAgreed.value
	}

	// 处理隐私协议预览
	const handlePrivacyAgreement = () => {
		uni.navigateTo({
			url: '/pages/webview/privacyWeb?type=privacy'
		})
	}


	// 切换验证码登录
	const switchToCodeLogin = () => {
		uni.$uv.toast(`敬请期待`);
	}

	// 处理登录
	const handleLogin = () => {
		console.log('开始登录流程');
		let f = Object.assign({}, form.value)
		console.log('登录参数:', f);

		if (!f.username) {
			uni.$uv.toast(`请输入账户`);
			return
		}
		if (!f.password) {
			uni.$uv.toast(`请输入密码`);
			return
		}
		if (!isAgreed.value) {
			uni.$uv.toast(`请先阅读并勾选《隐私协议》，即可继续登录。`);
			return
		}

		uni.showLoading({
			title: '登录中...',
			mask: true,
		})
		// f.deviceId = uni.getSystemInfoSync().deviceId;
		f.deviceId = 18;
		f.loginType = 'PASSWORD'
		loginApi.xcx_appletLogin(f).then(async (res) => {
			if (res && res.id) {
				console.log('登录成功，准备跳转');
				uni.$uv.toast(`登录成功`);
				
				// 保存账号密码
				saveSavedCredentials();
				
				userStore['token'] = res['token']
				userStore['userId'] = res['userId']
				userStore['tenantId'] = res['tenantId']
				userStore['user'] = res

				// 获取用户详细信息
				try {
					const userDetail = await orgApi.getEmpById(res.userId);
					userStore['userDetail'] = userDetail;
					console.log('userDetail', userDetail);

					console.log('userDetail.roleList', userDetail && userDetail.roleList);
					// 检查roleList中是否包含律师角色
					if (userDetail && userDetail.roleList) {
						const isLawyer = userDetail.roleList.some(role => role.name && role.name.includes(
							'律师'));
						userStore['lawyer'] = isLawyer;
					} else {
						userStore['lawyer'] = false;
					}
				} catch (error) {
					console.error('获取用户详细信息失败:', error);
					userStore['lawyer'] = false;
				}
				
				uni.hideLoading()
				
				// 登录成功后检查用户是否已签约承诺书
				checkCommitmentStatus();
			} else {
				uni.hideLoading()
				console.log('登录失败，响应数据异常:', res);
				uni.$uv.toast(`登录失败，请重试`);
			}
		}).catch((err) => {
			console.error('登录请求失败:', err);
			uni.hideLoading()
			uni.$uv.toast(`登录失败: ${err.message || '网络错误'}`);
		})
	}

	// 忘记密码
	const handleForget = () => {
		uni.navigateTo({
			url: '/pages/login/changePwd'
		})
	}

	// 微信登录
	const handleWechatLogin = () => {
		uni.$uv.toast(`敬请期待`);
		// uni.showToast({
		// 	title: '微信登录功能开发中',
		// 	icon: 'none'
		// })
	}
</script>

<style lang="scss" scoped>
	@import '/static/css/buttons.scss';
	/* 基础样式 */

	.login-container {
		display: flex;
		flex-direction: column;
		height: auto;
		position: relative;

		/* 顶部装饰区 */
		.header-bg {
			position: relative;
			height: 360rpx;
			width: 100%;
			/*background: linear-gradient(to bottom, #1A73E8, #3AAFFF);*/
		}

		.logo-container {
			position: absolute;
			top: 80rpx;
			left: 50%;
			transform: translateX(-50%);
		}

		.logo-circle {
			width: 140rpx;
			height: 140rpx;
			margin: 0 auto;
			// background: #3e91f3;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			// box-shadow: 0 12rpx 32rpx rgba(74, 144, 226, 0.2);
			transition: all 0.3s ease;
		}

		.logo-text {
			font-family: "Pacifico", serif;
			font-size: 48rpx;
			color: #1A73E8;
		}

		.welcome-text {
			position: absolute;
			top: 240rpx;
			left: 50%;
			transform: translateX(-50%);
			text-align: center;
		}

		.welcome-title {
			color: #FFFFFF;
			font-size: 44rpx;
			font-weight: bold;
			line-height: 56rpx;
			color: #2C3E50;
			white-space: nowrap;
		}

		.welcome-subtitle {
			color: #7F8C8D;
			font-size: 28rpx;
			font-weight: 400;
			margin-top: 15rpx;
			line-height: 56rpx;
			white-space: nowrap;
		}

		/* 登录表单区 */
		.login-form {
			margin: 40rpx 32rpx 0;
			border-radius: 24rpx;

		}

		.input-group {
			position: relative;
			margin-bottom: 24rpx;
		}

		.input-icon {
			position: absolute;
			left: 24rpx;
			top: 50%;
			transform: translateY(-50%);
		}

		.input-field {
			// width: 100%;
			box-sizing: border-box;
			// height: 48px;
			// padding-left: 40px;
			// padding-right: 40px;
			height: 96rpx;
			padding-left: 80rpx;
			padding-right: 80rpx;
			border: 2rpx solid #cfd1d5;
			border-radius: 16rpx;
			font-size: 28rpx;
		}

		.input-field:focus {
			border-color: #1A73E8;
			outline: none;
		}

		.placeholder {
			color: #9CA3AF;
			font-size: 28rpx;
		}

		.toggle-icon {
			position: absolute;
			right: 48rpx;
			top: 50%;
			transform: translateY(-50%);
		}

		.switch-login {
			text-align: right;
			margin-top: 16rpx;
		}

		.switch-text {
			color: #1A73E8;
			font-size: 24rpx;
			text-decoration: none;
		}

		/* 登录按钮 */
		.login-btn {
			margin: 48rpx 32rpx 0;
			height: 85rpx;
			background: linear-gradient(220deg, #1A73E8 0%, #3AAFFF 100%);
			color: #FFFFFF;
			font-weight: bold;
			border-radius: 48rpx;
		}

		.login-btn:active {
			background: linear-gradient(220deg, #1A73E8 0%, #3AAFFF 80%);
			transform: scale(0.98);
		}

		/* 忘记密码 */
		.forget-password {
			margin-top: 24rpx;
			text-align: center;
		}

		.forget-text {
			color: #1A73E8;
			font-size: 24rpx;
			text-decoration: none;
		}

		/* 第三方登录 */
		.third-party {
			margin: 32rpx 32rpx 0;
		}

		.divider {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;
		}

		.divider-line {
			flex: 1;
			height: 2rpx;
			background-color: #E5E7EB;
		}

		.divider-text {
			padding: 0 24rpx;
			color: #9CA3AF;
			font-size: 24rpx;
		}

		.third-icon {
			display: flex;
			justify-content: center;
		}

		.icon-btn {
			// width: 160rpx;
			// height: 160rpx;
			// border-radius: 50%;
			background-color: transparent;
			border-width: none;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
		}

		.icon-btn::after {
			display: none;
		}

		/* 底部协议 */
		.footer {
			margin: 50rpx 0;
			// position: absolute;
			// bottom: 32rpx;
			// left: 0;
			right: 0;
			text-align: center;
		}

		.agreement-container {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-wrap: wrap;
			gap: 8rpx;
		}

		.checkbox-container {
			display: flex;
			align-items: center;
			gap: 12rpx;
		}

		.checkbox {
			width: 32rpx;
			height: 32rpx;
			border: 2rpx solid #D1D5DB;
			border-radius: 6rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s ease;
		}

		.checkbox.checked {
			background-color: #1A73E8;
			border-color: #1A73E8;
		}

		.footer-text {
			color: #6B7280;
			font-size: 24rpx;
		}

		.footer-link {
			color: #1A73E8;
			font-size: 24rpx;
		}

		.slogan-text {
			margin: 10rpx 0;

			right: 0;
			color: #7F8C8D;
			font-size: 24rpx;
			text-align: center;
		}
	}
	
	/* 承诺书弹窗样式 */
	.commitment-popup {
		width: 650rpx;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
	}
	
	.commitment-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 20rpx;
	}
	
	.commitment-content {
		flex: 1;
		max-height: 60vh;
		padding: 20rpx;
		box-sizing: border-box;
		font-size: 28rpx;
		line-height: 1.6;
		color: #333;
		overflow-y: auto;
	}
	
	.commitment-content :deep(rich-text) {
		width: 100%;
		display: block;
	}
	
	.commitment-content :deep(.commitment) {
		width: 100%;
		overflow-wrap: break-word;
	}
	
	.commitment-content :deep(h2) {
		font-size: 32rpx;
		margin-bottom: 20rpx;
	}
	
	.commitment-content :deep(h3) {
		font-size: 28rpx;
		margin: 16rpx 0;
	}
	
	.commitment-content :deep(p) {
		margin-bottom: 30rpx;
	}
	
	.commitment-footer {
		margin-top: 30rpx;
		display: flex;
		justify-content: center;
	}
	
	.commitment-btn {
		width: 400rpx;
		height: 80rpx;
		background: linear-gradient(220deg, #1A73E8 0%, #3AAFFF 100%);
		border-radius: 40rpx;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>