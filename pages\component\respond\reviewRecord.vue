<template>
	<view class="review-record-container">
		<!-- 审查记录内容 -->
		<view class="content" v-if="pageParams.type === 'contract'">
			<view class="scroll-container" scroll-y="true">
				<!-- 有审查记录时显示时间线 -->
				<view v-if="reviewRecords.length > 0" class="workflow-container">
					<view class="timeline">
						<view class="timeline-item" v-for="(record, index) in reviewRecords" :key="index">
							<view class="timeline-dot active"></view>
							<view class="timeline-content">
								<view class="approval-header">
									<!-- <text class="approval-status" :class="record.status">{{ record.statusText }}</text> -->
								</view>
								<text class="approval-description">{{ record.description }}</text>
								<view class="approval-comment">
									<text class="comment-label">审查员：{{ record.reviewer }}</text>
									<text class="comment-text">{{ record.time }}</text>
								</view>
								<view v-if="record.opinion" class="approval-comment">
									<text class="comment-label">审查意见：</text>
									<text class="comment-text">{{ record.opinion }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 无审查记录时显示空状态 -->
				<view v-else class="empty-state">
					<view class="empty-icon">📋</view>
					<text class="empty-title">暂无审查记录</text>
					<text class="empty-description">该项目还没有审查记录</text>
				</view>
			</view>
		</view>
		<view class="content" v-else>
			<view class="scroll-container" scroll-y="true">
				<!-- 有审查记录时显示时间线 -->
				<view v-if="reviewRecords.length > 0" class="workflow-container">
					<view class="timeline">
						<view class="timeline-item" v-for="(record, index) in reviewRecords" :key="index">
							<view class="timeline-dot active"></view>
							<view class="timeline-content">
								<view class="approval-header">
									<text class="approval-step">{{ record.taskName }}</text>
									<!-- <text class="approval-status" :class="record.status">{{ record.statusText
                                                }}</text> -->
								</view>
								<view v-for="(recordChild) in record.contentList" :key="recordChild.id">
									<mp-html :content="recordChild.content" />
								</view>
								<view class="approval-comment">
									<text class="comment-label">创建人：{{ record.createdBy }}</text>
									<text class="comment-text">{{ record.createdAt }}</text>
								</view>
								<view class="approval-comment">
									<text class="comment-label">审查意见：</text>
									<text class="comment-text">{{ record.comment }}</text>
								</view>

							</view>
						</view>
					</view>
				</view>

				<!-- 无审查记录时显示空状态 -->
				<view v-else class="empty-state">
					<view class="empty-icon">📋</view>
					<text class="empty-title">暂无审查记录</text>
					<text class="empty-description">该项目还没有审查记录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import contractApi from '@/api/contract/index.js'

// 页面参数
const pageParams = ref({
	id: null,
	type: null
})

// 审查记录数据
const reviewRecords = ref([])
const arr = ref([])

// 获取页面参数
const getPageParams = () => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	return currentPage.options
}

// 加载审查记录
const loadReviewRecords = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		})

		let result = []
		if (pageParams.value.type === 'contract') {
			result = await contractApi.queryAllReviews(pageParams.value.id)
		} else {
			const params = {
				page: 0,
				size: 20,
				reviewId: pageParams.value.id,
				reviewType: pageParams.value.type.toUpperCase()
			};
			result = await contractApi.queryAllOtherReviews(params)
		}
		if (pageParams.value.type === 'contract') {
			if (result && Array.isArray(result) && result.length > 0) {
				// 格式化审查记录数据以适配页面显示
				reviewRecords.value = result.map((record, index) => {
					// 根据完成时间判断状态
					const isCompleted = record.finishDate && record.finishDate.seconds
					return {
						status: isCompleted ? 'completed' : 'pending',
						// statusText: isCompleted ? '已完成' : '进行中',
						description: record.content || '暂无审查内容',
						reviewer: record.createdBy || '系统',
						time: record.finishDate || record.createdAt,
						opinion: record.opinion || '暂无意见',
						qualityScore: record.qualityScore || 0,
						aiModels: record.aiModels || ''
					}
				})
			} else {
				// 如果没有数据，设置为空数组，显示空状态
				reviewRecords.value = []
			}
		} else {
			reviewRecords.value = result
		}


		uni.hideLoading()
	} catch (error) {
		console.error('加载审查记录失败:', error)
		uni.hideLoading()
		uni.showToast({
			title: '加载审查记录失败',
			icon: 'none'
		})

		// 加载失败时设置为空数组，显示空状态
		reviewRecords.value = []
	}
}

// 页面加载时获取参数并加载数据
onMounted(async () => {
	const params = getPageParams()
	pageParams.value.id = params.id
	pageParams.value.type = params.type

	if (pageParams.value.id) {
		await loadReviewRecords()
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
	}
})
</script>

<style lang="scss" scoped>
.review-record-container {
	background: #F5F5F5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.content {
	flex: 1;
	padding: 32rpx;
}

.scroll-container {
	height: 100vh;
}

.workflow-container {
	background: white;
	border-radius: 12rpx;
	padding: 32rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.workflow-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 32rpx;
	text-align: center;
}

.timeline {
	position: relative;
	padding-left: 40rpx;
}

.timeline::before {
	content: '';
	position: absolute;
	left: 20rpx;
	top: 0;
	bottom: 0;
	width: 2rpx;
	background: #E5E5E5;
}

.timeline-item {
	position: relative;
	margin-bottom: 40rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.timeline-dot {
	position: absolute;
	left: -30rpx;
	top: 8rpx;
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background: #1A73E8;
	border: 4rpx solid white;
	box-shadow: 0 0 0 2rpx #E5E5E5;

	&.active {
		background: #1A73E8;
		box-shadow: 0 0 0 2rpx #1A73E8;
	}
}

.timeline-content {
	background: #F8F9FA;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-left: 20rpx;
}

.approval-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.approval-status {
	font-size: 28rpx;
	font-weight: bold;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;

	&.completed {
		color: #52C41A;
		background: #F6FFED;
	}

	&.pending {
		color: #FA8C16;
		background: #FFF7E6;
	}

	&.error {
		color: #FF4D4F;
		background: #FFF2F0;
	}
}

.approval-description {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	margin: 20rpx 0;
}

.approval-comment {
	display: flex;
	align-items: flex-start;
	margin-bottom: 12rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.comment-label {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-right: 16rpx;
	flex-shrink: 0;
}

.comment-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #555;
	line-height: 1.5;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
	min-height: 400rpx;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 24rpx;
	opacity: 0.6;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.empty-description {
	font-size: 28rpx;
	color: #999;
	line-height: 1.5;
}
</style>