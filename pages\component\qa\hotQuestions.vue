<template>
	<view class="question-detail-container">
		<!-- 问题详情内容 -->
		<view class="content" scroll-y="true">
			<view v-if="questionData" class="detail-content">
				<!-- 问题标题卡片 -->
				<view class="question-card">
					<view class="question-header">
						<uni-icons type="help-filled" size="20" color="#4A90E2"></uni-icons>
						<!-- <text class="question-label">问题</text> -->
					</view>
					<text class="question-text">{{ questionData.questionTitle }}</text>
				</view>
				
				<!-- 答案内容卡片 -->
				<view class="answer-card">
					<view class="answer-header">
						<uni-icons type="checkmarkempty" size="20" color="#52C41A"></uni-icons>
						<!-- <text class="answer-label">答案</text> -->
					</view>
					<text class="answer-text">{{ questionData.answerContent }}</text>
				</view>
				
				<!-- 元信息卡片 -->
				<view class="meta-card">
					<view class="meta-header">
						<uni-icons type="info-filled" size="18" color="#8C8C8C"></uni-icons>
						<text class="meta-title">详细信息</text>
					</view>
					<view class="meta-content">
						<view class="meta-item">
							<view class="meta-icon">
								<uni-icons type="folder" size="16" color="#8C8C8C"></uni-icons>
							</view>
							<text class="meta-label">分类</text>
							<text class="meta-value">{{ getCategoryText(questionData.category) }}</text>
						</view>
						<view class="meta-item">
							<view class="meta-icon">
								<uni-icons type="eye" size="16" color="#8C8C8C"></uni-icons>
							</view>
							<text class="meta-label">浏览次数</text>
							<text class="meta-value">{{ questionData.clickCount }}</text>
						</view>
					</view>
				</view>
				
				<!-- 智能解答引导卡片 -->
				<view class="ai-guide-card" @click="goToAIChat">
					<view class="ai-guide-content">
						<view class="ai-guide-left">
							<view class="ai-guide-icon">
								<uni-icons type="chatbubble-filled" size="24" color="#ffffff"></uni-icons>
							</view>
							<view class="ai-guide-text">
								<text class="ai-guide-title">回答不够详细？</text>
								<text class="ai-guide-subtitle">进入智能解答获取更多帮助</text>
							</view>
						</view>
						<view class="ai-guide-arrow">
							<uni-icons type="arrowright" size="18" color="#1890ff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-else class="empty-state">
				<view class="empty-icon">
					<uni-icons type="help" size="80" color="#D9D9D9"></uni-icons>
				</view>
				<text class="empty-text">暂无问题数据</text>
				<text class="empty-desc">请从问题列表中选择一个问题查看详情</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import qaApi from '@/api/chatAI/qa.js'
import { useUserStore } from '@/store/pinia.js'
const userStore = useUserStore()

const userAgent = uni.getDeviceInfo()
// 响应式数据
const questionData = ref(null)

// 分类映射
const categoryMap = {
	REGULATORY_COMPLIANCE: '合规管理',
	POLICY_REVIEW: '政策审查',
	CONTRACT_REVIEW: '合同审查',
	GENERAL_CONSULTATION: '一般咨询',
	LEGAL_ADVICE: '法律建议',
	COMMON_QUESTIONS: '常见问题'
}

// 获取分类文本
const getCategoryText = (category) => {
	return categoryMap[category] || '其他'
}

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 跳转到智能解答
const goToAIChat = () => {
	if (questionData.value && questionData.value.questionTitle) {
		uni.navigateTo({
			url: `/pages/component/qa/qaDetail?questionTitle=${encodeURIComponent(questionData.value.questionTitle)}`
		})
	} else {
		uni.navigateTo({
			url: '/pages/qa/qa'
		})
	}
}

// 获取问题详情
const getQuestionDetail = async (id) => {
	try {
		// 显示加载状态
		uni.showLoading({
			title: '加载中...'
		})
		
		const params = {
			employeeId: userStore.userId,
			userAgent: userAgent.model
		}
		const response = await qaApi.addViewCount(id, params)
		if (response) {
			questionData.value = response
		}
	} catch (error) {
		console.error('获取问题详情失败:', error)
		uni.showToast({
			title: '获取问题详情失败',
			icon: 'none'
		})
	} finally {
		// 隐藏加载状态
		uni.hideLoading()
	}
}

// 页面加载时接收数据
onMounted(() => {
	// 接收从上一页传递的问题数据
	const instance = getCurrentInstance()
	const eventChannel = instance?.proxy?.getOpenerEventChannel?.()
	if (eventChannel) {
		eventChannel.on('acceptDataFromOpenerPage', function(data) {
			if (data.question && data.question.id) {
				// 使用传递过来的问题 id 调用详情接口
				getQuestionDetail(data.question.id)
			} else if (data.question) {
				// 如果没有 id，直接显示传递过来的问题详情（兼容旧逻辑）
				questionData.value = data.question
			}
		})
	}
})
</script>

<style lang="scss" scoped>
.question-detail-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}


// 内容区域样式
.content {
	flex: 1;
	padding: 24rpx;
	overflow-y: auto;
}

.detail-content {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

// 卡片通用样式
.question-card,
.answer-card,
.meta-card,
.ai-guide-card {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

// 问题卡片样式
.question-card {
	border-left: 6rpx solid #4A90E2;
}

.question-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	gap: 12rpx;
}

.question-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #4A90E2;
	letter-spacing: 1rpx;
}

.question-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.6;
	display: block;
	word-break: break-word;
}

// 答案卡片样式
.answer-card {
	border-left: 6rpx solid #52C41A;
}

.answer-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	gap: 12rpx;
}

.answer-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #52C41A;
	letter-spacing: 1rpx;
}

.answer-text {
	font-size: 32rpx;
	color: #555;
	line-height: 1.8;
	display: block;
	white-space: pre-wrap;
	word-break: break-word;
}

// 元信息卡片样式
.meta-card {
	border-left: 6rpx solid #8C8C8C;
}

.meta-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	gap: 10rpx;
}

.meta-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #8C8C8C;
	letter-spacing: 1rpx;
}

.meta-content {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	background: rgba(248, 249, 250, 0.8);
	border-radius: 12rpx;
	transition: all 0.3s ease;

	&:active {
		background: rgba(248, 249, 250, 1);
		transform: scale(0.98);
	}
}

.meta-icon {
	margin-right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.meta-label {
	font-size: 28rpx;
	color: #666;
	flex: 1;
	font-weight: 500;
}

.meta-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
	line-height: 1.5;
	max-width: 400rpx;
}

// 智能解答引导卡片样式
.ai-guide-card {
	border-left: 6rpx solid #1890ff;
	background: linear-gradient(135deg, rgba(24, 144, 255, 0.03) 0%, rgba(69, 90, 100, 0.03) 100%);
	cursor: pointer;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(24, 144, 255, 0.06) 0%, rgba(69, 90, 100, 0.06) 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
	}

	&:active {
		transform: translateY(1rpx) scale(0.98);
		
		&::before {
			opacity: 1;
		}
	}
}

.ai-guide-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	z-index: 1;
}

.ai-guide-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.ai-guide-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	border-radius: 50%;
	box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.25);
}

.ai-guide-text {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.ai-guide-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	letter-spacing: 0.5rpx;
}

.ai-guide-subtitle {
	font-size: 24rpx;
	color: #666;
	font-weight: 400;
	letter-spacing: 0.5rpx;
}

.ai-guide-arrow {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40rpx;
	height: 40rpx;
	background: rgba(24, 144, 255, 0.08);
	border-radius: 50%;
	transition: all 0.3s ease;
}

.ai-guide-card:active .ai-guide-arrow {
	background: rgba(24, 144, 255, 0.15);
	transform: translateX(4rpx);
}

</style>