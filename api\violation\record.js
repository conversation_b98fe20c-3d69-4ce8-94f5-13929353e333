import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}

const baseUrl = `/services/whiskerguardviolationservice/api/problem/investigate/records`

export default {
    // 创建问题调查记录
    createInvestigateRecord(data) {
        return request(baseUrl, data, 'post')
    },
    // 获取调查记录列表
    getInvestigateRecords(params) {
        return request(baseUrl, params, 'get')
    },
    // 获取调查记录详情
    getInvestigateRecordDetail(id) {
        return request(baseUrl + `/${id}`, {}, 'get')
    },
    // 更新调查记录
    updateInvestigateRecord(id, data) {
        return request(baseUrl + `/${id}`, data, 'put')
    },
    // 删除调查记录
    deleteInvestigateRecord(id) {
        return request(baseUrl + `/${id}`, {}, 'delete')
    }
}