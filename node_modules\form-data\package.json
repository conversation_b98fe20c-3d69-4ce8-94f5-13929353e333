{"_from": "form-data@^4.0.0", "_id": "form-data@4.0.1", "_inBundle": false, "_integrity": "sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==", "_location": "/form-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "form-data@^4.0.0", "name": "form-data", "escapedName": "form-data", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.1.tgz", "_shasum": "ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48", "_spec": "form-data@^4.0.0", "_where": "G:\\项目集合\\家客云_h5\\node_modules\\axios", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "browser": "./lib/browser", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "bundleDependencies": false, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "deprecated": false, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "puppeteer": "^1.19.0", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "engines": {"node": ">= 6"}, "homepage": "https://github.com/form-data/form-data#readme", "license": "MIT", "main": "./lib/form_data", "name": "form-data", "pre-commit": ["lint", "ci-test", "check"], "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "scripts": {"browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "lint": "eslint --ext=js,mjs .", "postpublish": "npm run restore-readme", "posttest": "npx npm@'>=10.2' audit --production", "posttests-only": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "report": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md", "test": "npm run tests-only", "tests-only": "istanbul cover test/run.js", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md"}, "typings": "./index.d.ts", "version": "4.0.1"}