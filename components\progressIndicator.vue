<!-- components/progress-indicator.vue -->
<template>
  <view class="progress-container">
    <view class="steps-wrapper">
      <!-- 进度条容器 -->
      <view class="progress-track">
        <!-- 进度条填充部分 -->
        <view 
          class="progress-fill"
          :style="{
            width: fillWidth,
            // left: trackPadding
          }"
        ></view>
      </view>

      <!-- 步骤节点 -->
      <view class="steps-list">
        <view 
          v-for="(step, index) in steps"
          :key="index"
          class="step-item"
          :style="{ width: stepWidth }"
        >
          <!-- 步骤指示点 -->
          <view 
            class="step-marker"
            :class="{
              'completed': index < currentStep,
              'current': index === currentStep
            }"
          >
            <view class="inner-dot">
              <uni-icons 
                v-if="index < currentStep"
                type="checkmarkfilled" 
                size="14" 
                color="#fff"
              />
            </view>
          </view>
          
          <!-- 步骤文字 -->
          <view class="step-text">
            <text 
              class="step-title"
              :class="{
                'completed': index <= currentStep,
                'current': index === currentStep
              }"
            >{{ step.title }}</text>
            <text 
              v-if="step.description"
              class="step-description"
            >{{ step.description }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  steps: {
    type: Array,
    default: () => [
      { title: '制度建立', description: '基础框架搭建' },
      { title: '清单配置', description: '权限清单管理' },
      { title: '合规审查', description: '风险审查流程' },
      { title: '持续监控', description: '执行情况跟踪' }
    ]
  },
  currentStep: {
    type: Number,
    default: 0
  }
})

// 进度条计算逻辑
const fillWidth = computed(() => {
  if (props.currentStep === 0) return '0%'
  const stepPercent = 100 / (props.steps.length - 1)
  return `${props.currentStep * stepPercent}%`
})

// 步骤间距计算
// const trackPadding = computed(() => `calc(50% / ${props.steps.length})`)
const stepWidth = computed(() => `calc(100% / ${props.steps.length})`)
</script>

<style lang="scss" scoped>
.progress-container {
  padding: 48rpx 32rpx;
  background: #fff;

  .steps-wrapper {
  position: relative;
}

// 进度条轨道
.progress-track {
  height: 8rpx;
  background: #f0f2f5;
  border-radius: 8rpx;
  margin: 0 64rpx;
  top: 76rpx;
  left: 1px;
  position: relative;
  overflow: hidden;
}

// 进度条填充
.progress-fill {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, #1A73E8, #0d47a1);
  border-radius: 8rpx;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

// 步骤列表
.steps-list {
  display: flex;
  justify-content: space-between;
  margin-top: 48rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

// 步骤标记点
.step-marker {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #fff;
  border: 3rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  &.completed {
    border-color: #1A73E8;
    background: #1A73E8;
    box-shadow: 0 4rpx 12rpx rgba(26, 115, 232, 0.2);
  }

  &.current {
    border-color: #1A73E8;
    transform: scale(1.2);
    
    .inner-dot {
      background: #1A73E8;
    }
  }
}

.inner-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 步骤文字
.step-text {
  text-align: center;
  padding: 0 16rpx;
}

.step-title {
  font-size: 28rpx;
  color: #999;
  line-height: 1.4;
  display: block;
  transition: color 0.3s ease;

  &.completed {
    color: #1A73E8;
    font-weight: 500;
  }

  &.current {
    color: #333;
    font-weight: 600;
  }
}

.step-description {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  line-height: 1.2;
}
}
</style>