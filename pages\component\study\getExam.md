---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/考试管理

## GET 获取考试题目

GET /api/exam/management/questions/{examRecordId}

获取考试题目
{@code GET  /exam/management/questions/{examRecordId}} : Get exam questions.
获取考试题目
获取指定考试记录的题目列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|examRecordId|path|integer| 是 |考试记录ID 考试记录ID|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 500 Response

```json
[
  {
    "questionId": 0,
    "questionOrder": 0,
    "questionContent": "",
    "optionA": "",
    "optionB": "",
    "optionC": "",
    "optionD": "",
    "questionType": "",
    "difficulty": "",
    "userAnswer": "",
    "isAnswered": false
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **500**

*状态为 {@code 200 (OK)} 的响应实体，包含考试题目列表
获取题目成功
考试记录不存在
获取题目失败*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListExamQuestionDTO](#schemalistexamquestiondto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含考试题目列表<br />获取题目成功<br />考试记录不存在<br />获取题目失败|
|» questionId|integer(int64)|false|none||题目ID|
|» questionOrder|integer|false|none||题目序号|
|» questionContent|string|false|none||题目内容|
|» optionA|string|false|none||选项A|
|» optionB|string|false|none||选项B|
|» optionC|string|false|none||选项C|
|» optionD|string|false|none||选项D|
|» questionType|string|false|none||题目类型|
|» difficulty|string|false|none||题目难度|
|» userAnswer|string|false|none||用户已选答案（如果有）|
|» isAnswered|boolean|false|none||是否已答题|

# 数据模型

<h2 id="tocS_ListExamQuestionDTO">ListExamQuestionDTO</h2>

<a id="schemalistexamquestiondto"></a>
<a id="schema_ListExamQuestionDTO"></a>
<a id="tocSlistexamquestiondto"></a>
<a id="tocslistexamquestiondto"></a>

```json
{
  "questionId": 0,
  "questionOrder": 0,
  "questionContent": "string",
  "optionA": "string",
  "optionB": "string",
  "optionC": "string",
  "optionD": "string",
  "questionType": "string",
  "difficulty": "string",
  "userAnswer": "string",
  "isAnswered": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|questionId|integer(int64)|false|none||题目ID|
|questionOrder|integer|false|none||题目序号|
|questionContent|string|false|none||题目内容|
|optionA|string|false|none||选项A|
|optionB|string|false|none||选项B|
|optionC|string|false|none||选项C|
|optionD|string|false|none||选项D|
|questionType|string|false|none||题目类型|
|difficulty|string|false|none||题目难度|
|userAnswer|string|false|none||用户已选答案（如果有）|
|isAnswered|boolean|false|none||是否已答题|

