<template>
    <view class="about-us-container">
        <!-- 主要内容区域 -->
        <view class="content">
            <!-- 公司Logo和名称 -->
            <view class="logo-card">
                <view class="logo-container">
                    <image src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
                        class="logo-image" mode="aspectFill"></image>
                </view>
                <text class="company-name">猫伯伯智能合规管家</text>
                <text class="company-subname">WhiskerGuard™</text>
            </view>

            <!-- 公司简介 -->
            <view class="info-card">
                <text class="card-title">企业使命与愿景</text>
                <text class="card-text">　　猫伯伯智能合规管家以"一体三翼"为理论框架，秉承"让管理插上合规的翅膀，让合规插上科技的翅膀"的企业理念，致力于通过前沿的信息技术和创新解决方案，帮助各行各业实现从传统管理向智能合规管理的转型升级。</text>
                <text class="card-text">　　我们相信，合规不应是企业的负担，而是发展的基石。我们始终以科技赋能合规、数据驱动管理为己任，致力于打造安全、高效、透明的数字化智慧合规管理生态体系。</text>
            </view>

            <!-- 核心价值观 -->
           <!-- <view class="info-card">
                <text class="card-title">核心价值观</text>
                <view class="value-grid">
                    <view class="value-item" v-for="(item, index) in coreValues" :key="index">
                        <uni-icons :type="item.icon" size="32" :color="primaryColor"></uni-icons>
                        <text class="value-text">{{ item.text }}</text>
                    </view>
                </view>
            </view> -->

            <!-- 联系我们 -->
            <view class="info-card">
                <text class="card-title">联系我们</text>
                <view class="contact-item" @click="handlePhone">
                    <view class="contact-left">
                        <uni-icons type="phone" size="24" color="#666"></uni-icons>
                        <text class="contact-text">************</text>
                    </view>
                    <uni-icons type="right" size="20" color="#999"></uni-icons>
                </view>
                <view class="divider"></view>
                <view class="contact-item" @click="handleEmail">
                    <view class="contact-left">
                        <uni-icons type="email" size="24" color="#666"></uni-icons>
                        <text class="contact-text"><EMAIL></text>
                    </view>
                    <uni-icons type="right" size="20" color="#999"></uni-icons>
                </view>
            </view>

            <!-- 版本与协议 -->
            <view class="info-card">
                <view class="contact-item">
                    <view class="contact-left">
                        <uni-icons type="reload" size="24" color="#666"></uni-icons>
                        <text class="contact-text">当前版本：v1.0.0</text>
                    </view>
                </view>
                <view class="divider"></view>
                <view class="contact-item" @click="handlePrivacy">
                    <view class="contact-left">
                        <uni-icons type="paperclip" size="24" color="#666"></uni-icons>
                        <text class="contact-text">隐私政策</text>
                    </view>
                    <uni-icons type="right" size="20" color="#999"></uni-icons>
                </view>
                <view class="divider"></view>
                <view class="contact-item" @click="handleAgreement">
                    <view class="contact-left">
                        <uni-icons type="paperclip" size="24" color="#666"></uni-icons>
                        <text class="contact-text">用户协议</text>
                    </view>
                    <uni-icons type="right" size="20" color="#999"></uni-icons>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue';
const userDoc = ref('https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/%E7%94%A8%E6%88%B7%E5%8D%8F%E8%AE%AE.docx');
const agreementDoc = ref('https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/%E9%9A%90%E7%A7%81%E5%A3%B0%E6%98%8E.docx');
const primaryColor = '#1A73E8';
const activeTab = ref('home');

const coreValues = [
    { icon: 'map-filled', text: '智慧' },
    { icon: 'locked', text: '可靠' },
    { icon: 'search', text: '敏锐' },
    { icon: 'heart', text: '温暖' }
];

const goBack = () => {
    uni.navigateBack();
};

const switchTab = (tab) => {
    activeTab.value = tab;
    // 实际应用中这里会跳转到对应页面
    console.log('切换到', tab);
};

const handlePhone = () => {
    uni.showModal({
        title: '提示',
        content: '是否要拨打 ************？',
        success: (res) => {
            if (res.confirm) {
                uni.makePhoneCall({
                    phoneNumber: '4001665291'
                });
            }
        }
    });
};

const handleEmail = () => {
    uni.showModal({
        title: '提示',
        content: '是否要复制邮箱地址 <EMAIL>？',
        success: (res) => {
            if (res.confirm) {
                uni.setClipboardData({
                    data: '<EMAIL>',
                    success: () => {
                        uni.showToast({
                            title: '邮箱地址已复制',
                            icon: 'none'
                        });
                    }
                });
            }
        }
    });
};

const handlePrivacy = () => {
    uni.navigateTo({
        url: '/pages/webview/privacyWeb?type=privacy'
    });
};

const handleAgreement = () => {
    uni.navigateTo({
        url: '/pages/webview/privacyWeb?type=agreement'
    });
};
</script>

<style lang="scss" scoped>
.about-us-container {
    height: 100%;
    background-color: #f8f8f8;

    .content {
        padding-top: 30rpx;
        padding-bottom: 100rpx;
    }

    .logo-card {
        background-color: #fff;
        border-radius: 16rpx;
        margin: 32rpx;
        padding: 48rpx 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    }

    .logo-container {
        width: 128rpx;
        height: 128rpx;
        border-radius: 50%;
        background-color: #e8f0fe;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .logo-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }

    .company-name {
        font-size: 40rpx;
        font-weight: bold;
        color: #1A73E8;
        margin-bottom: 8rpx;
    }

    .company-subname {
        font-size: 28rpx;
        color: #666;
    }

    .info-card {
        background-color: #fff;
        border-radius: 16rpx;
        margin: 0 32rpx 32rpx;
        padding: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    }

    .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 24rpx;
        display: block;
    }

    .card-text {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
        display: block;
    }

    .value-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16rpx;
    }

    .value-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx;
    }

    .value-text {
        font-size: 24rpx;
        font-weight: bold;
        color: #333;
        margin-top: 8rpx;
        text-align: center;
    }

    .contact-item {
        height: 96rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16rpx;
    }

    .contact-left {
        display: flex;
        align-items: center;
    }

    .contact-text {
        font-size: 28rpx;
        color: #333;
        margin-left: 16rpx;
    }

    .divider {
        height: 1px;
        background-color: #f0f0f0;
        margin: 0 16rpx;
    }
}
</style>