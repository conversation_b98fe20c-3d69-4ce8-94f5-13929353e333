
<template>
  <view class="container">
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 左侧分类Tab -->
      <scroll-view class="left-tab" scroll-y>
        <view 
          v-for="(item, index) in categories" 
          :key="item.id"
          class="tab-item"
          :class="{ active: activeTab === index }"
          @click="switchTab(index)"
        >
          <text class="tab-text">{{ item.name }}</text>
        </view>
      </scroll-view>

      <!-- 右侧内容区 -->
      <scroll-view class="right-content" scroll-y>
        <view class="content-header">
          <text class="category-title">{{ categories[activeTab].name }}</text>
        </view>
        
        <view class="question-list">
          <!-- 加载状态 -->
          <view v-if="loading" class="loading-container">
            <text class="loading-text">加载中...</text>
          </view>
          
          <!-- 问题列表 -->
          <view v-else-if="currentQuestions.length > 0">
            <view 
              v-for="question in currentQuestions" 
              :key="question.id"
              class="question-card"
              @click="goToDetail(question)"
            >
              <text class="question-title">{{ question.title }}</text>
              <text class="question-summary">{{ question.summary }}</text>
              <view class="question-meta">
                <text class="click-count">点击量: {{ question.clickCount }}</text>
              </view>
            </view>
            
            <!-- <view class="view-all" @click="viewAll">
              <text>查看全部</text>
              <uni-icons type="right" size="16" color="#999" />
            </view> -->
          </view>
          
          <!-- 空状态 -->
          <view v-else class="empty-container">
            <text class="empty-text">暂无相关问题</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部浮动按钮 -->
    <!-- <view class="float-btn">
      <button type="primary" @click="goToSmartQA" class="smart-qa-btn">去智能问答</button>
    </view> -->
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import qaApi from '@/api/chatAI/qa.js';

const activeTab = ref(0);
const categories = ref([]);
const questions = ref([]);
const loading = ref(false);
const currentQuestions = ref([]);

// 分类映射，将英文分类转换为中文显示
const categoryMap = {
  'REGULATORY_COMPLIANCE': '规章制度',
  'POLICY_REVIEW': '政策审查',
  'CONTRACT_REVIEW': '合同审查',
  'GENERAL_CONSULTATION': '一般咨询',
  'LEGAL_ADVICE': '法律建议',
  'COMMON_QUESTIONS': '常见问题'
};

// 获取所有分类
const fetchCategories = async () => {
  try {
    loading.value = true;
    const response = await qaApi.queryCategories();
      // 处理分类数据，转换为页面需要的格式
      categories.value = response.map((item, index) => ({
        id: index + 1,
        name: categoryMap[item.key] || item.key,
        key: item.key
      }));
      
      // 默认选中第一个分类并加载其问题
      if (categories.value.length > 0) {
        activeTab.value = 0;
        await fetchQuestionsByCategory(categories.value[0].key);
      }
  } catch (error) {
    console.error('获取分类失败:', error);
    uni.showToast({
      title: '获取分类失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 根据分类获取问题列表
const fetchQuestionsByCategory = async (category) => {
  try {
    loading.value = true;
    const response = await qaApi.queryQuestionsByCategory(category);
      // 处理问题数据，只显示启用的问题
      currentQuestions.value = response
        .filter(item => item.isEnabled)
        .map(item => ({
          id: item.id,
          title: item.questionTitle,
          summary: item.questionContent || item.answerContent?.substring(0, 100) + '...',
          content: item.questionContent,
          answer: item.answerContent,
          category: item.category,
          clickCount: item.clickCount
        }))
        .sort((a, b) => b.clickCount - a.clickCount); // 按点击量排序
  } catch (error) {
    console.error('获取问题列表失败:', error);
    currentQuestions.value = [];
    uni.showToast({
      title: '获取问题列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

const switchTab = async (index) => {
  activeTab.value = index;
  if (categories.value[index]) {
    await fetchQuestionsByCategory(categories.value[index].key);
  }
};



const goToDetail = (question) => {
  // 跳转到问题详情页，使用eventChannel传递问题数据
  uni.navigateTo({
    url: '/pages/component/qa/hotQuestions',
    events: {
      // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
      acceptDataFromOpenedPage: function(data) {
        console.log('接收到数据:', data)
      }
    },
    success: function (res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('acceptDataFromOpenerPage', { 
        question: {
          id: question.id,
          questionTitle: question.title,
          questionContent: question.content,
          answerContent: question.answer,
          category: question.category,
          clickCount: question.clickCount
        }
      })
    }
  });
};

const viewAll = () => {
  if (categories.value[activeTab.value]) {
    uni.navigateTo({
      url: `/pages/question/list?categoryId=${categories.value[activeTab.value].id}&category=${categories.value[activeTab.value].key}`
    });
  }
};

const goToSmartQA = () => {
  uni.navigateTo({
    url: '/pages/smart-qa/index'
  });
};

// 页面加载时获取数据
onMounted(() => {
  fetchCategories();
});
</script>

<style>
page {
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}



/* 主体内容区 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧Tab栏 */
.left-tab {
  width: 180rpx;
  background-color: #f9f9f9;
}

.tab-item {
  padding: 28rpx 20rpx;
  border-left: 4px solid transparent;
}

.tab-item.active {
  background-color: #fff;
  border-left-color: #2979ff;
}

.tab-text {
  font-size: 14px;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-item.active .tab-text {
  color: #2979ff;
  font-weight: 500;
}

/* 右侧内容区 */
.right-content {
  flex: 1;
  background-color: #fff;
}

.content-header {
  padding: 24rpx 32rpx;
}

.category-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.question-list {
  padding: 0 32rpx 120rpx;
}

.question-card {
  padding: 24rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.question-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.question-summary {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.view-all {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 32rpx 0;
  color: #999;
  font-size: 14px;
}

.question-meta {
  margin-top: 8rpx;
}

.click-count {
  font-size: 12px;
  color: #999;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部浮动按钮 */
.float-btn {
  position: fixed;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  z-index: 10;
}

.float-btn .smart-qa-btn {
  width: 100%;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(41, 121, 255, 0.2);
  background-color: #2979ff;
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
}

.float-btn .smart-qa-btn:hover {
  background-color: #1976d2;
}
</style>

