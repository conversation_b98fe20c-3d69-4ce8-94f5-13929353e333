
<template>
    <view class="container">
      <!-- Top Navigation Bar -->
      <view class="nav-bar">
        <view class="nav-icon">
          <uni-icons type="arrowleft" size="20" color="#606266"></uni-icons>
        </view>
        <text class="nav-title">学习计划</text>
        <view class="date-picker">
          <uni-icons type="calendar" size="16" color="#1A73E8"></uni-icons>
          <text class="date-text">2025-04</text>
          <uni-icons type="arrowdown" size="12" color="#1A73E8"></uni-icons>
        </view>
      </view>
  
      <!-- Main Content -->
      <scroll-view class="content" scroll-y>
        <!-- Calendar Section -->
        <view class="calendar-section">
          <view class="weekdays">
            <text class="weekday">日</text>
            <text class="weekday">一</text>
            <text class="weekday">二</text>
            <text class="weekday">三</text>
            <text class="weekday">四</text>
            <text class="weekday">五</text>
            <text class="weekday">六</text>
          </view>
          <view class="days-grid">
            <view class="day-item other-month">31</view>
            <view class="day-item">1</view>
            <view class="day-item">2</view>
            <view class="day-item">3</view>
            <view class="day-item current-day">
              <text class="day-number">4</text>
              <view class="day-dot"></view>
            </view>
            <view class="day-item">5</view>
            <view class="day-item">6</view>
  
            <view class="day-item">7</view>
            <view class="day-item">8</view>
            <view class="day-item">9</view>
            <view class="day-item current-day">
              <text class="day-number">10</text>
              <view class="day-dot"></view>
            </view>
            <view class="day-item">11</view>
            <view class="day-item">12</view>
            <view class="day-item">13</view>
  
            <view class="day-item">14</view>
            <view class="day-item">15</view>
            <view class="day-item">16</view>
            <view class="day-item">17</view>
            <view class="day-item">18</view>
            <view class="day-item">19</view>
            <view class="day-item">20</view>
  
            <view class="day-item">21</view>
            <view class="day-item">22</view>
            <view class="day-item">23</view>
            <view class="day-item">24</view>
            <view class="day-item">25</view>
            <view class="day-item">26</view>
            <view class="day-item">27</view>
  
            <view class="day-item">28</view>
            <view class="day-item">29</view>
            <view class="day-item">30</view>
            <view class="day-item selected-day">1</view>
            <view class="day-item">2</view>
            <view class="day-item">3</view>
            <view class="day-item">4</view>
          </view>
        </view>
  
        <!-- Today's Tasks -->
        <view class="tasks-section">
          <text class="section-title">今日计划：3 节</text>
          <view class="task-list">
            <view class="task-item">
              <text class="task-bullet">•</text>
              <text class="task-text">1-1. 课程导入：企业合规管理概述</text>
            </view>
            <view class="task-item">
              <text class="task-bullet">•</text>
              <text class="task-text">2-1. 风险清单介绍与编制方法</text>
            </view>
            <view class="task-item">
              <text class="task-bullet">•</text>
              <text class="task-text">3-2. 合规审查流程实操演练</text>
            </view>
          </view>
          <button  class="start-button" type="primary">开始学习</button>
        </view>
  
        <!-- Future Plans -->
        <view class="future-plans">
          <view class="plan-item">
            <view class="plan-header">
              <text class="plan-date">04-20 周日</text>
              <uni-icons type="arrowright" size="14" color="#C0C4CC"></uni-icons>
            </view>
            <view class="plan-content">
              <text class="task-bullet">•</text>
              <text class="plan-text">合规审查流程（2 课时）</text>
            </view>
          </view>
  
          <view class="plan-item">
            <view class="plan-header">
              <text class="plan-date">04-21 周一</text>
              <uni-icons type="arrowright" size="14" color="#C0C4CC"></uni-icons>
            </view>
            <view class="plan-content">
              <text class="task-bullet">•</text>
              <text class="plan-text">三张清单管理（1 课时）</text>
            </view>
          </view>
  
          <view class="plan-item">
            <view class="plan-header">
              <text class="plan-date">04-22 周二</text>
              <uni-icons type="arrowright" size="14" color="#C0C4CC"></uni-icons>
            </view>
            <view class="plan-content">
              <text class="task-bullet">•</text>
              <text class="plan-text">合规风险评估（1.5 课时）</text>
            </view>
          </view>
  
          <view class="plan-item">
            <view class="plan-header">
              <text class="plan-date">04-23 周三</text>
              <uni-icons type="arrowright" size="14" color="#C0C4CC"></uni-icons>
            </view>
            <view class="plan-content">
              <text class="task-bullet">•</text>
              <text class="plan-text">合规培训与文化建设（1 课时）</text>
            </view>
          </view>
        </view>
      </scroll-view>
  
      <!-- Bottom Navigation -->
      <view class="tab-bar">
        <view class="tab-item">
          <uni-icons type="home" size="20" color="#909399"></uni-icons>
          <text class="tab-text">首页</text>
        </view>
        <view class="tab-item active">
          <uni-icons type="book" size="20" color="#1A73E8"></uni-icons>
          <text class="tab-text">学习</text>
        </view>
        <view class="tab-item">
          <uni-icons type="chat" size="20" color="#909399"></uni-icons>
          <text class="tab-text">问答</text>
        </view>
        <view class="tab-item">
          <uni-icons type="person" size="20" color="#909399"></uni-icons>
          <text class="tab-text">我的</text>
        </view>
      </view>
    </view>
  </template>
  
  <script lang="ts" setup>
  import { ref } from 'vue';
  import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
  
  // 这里可以添加相关的逻辑代码
  const currentDate = ref(new Date());
  </script>
  
  <style>
  page {
    height: 100%;
  }
  
  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #F5F7FA;
  }
  
  /* Navigation Bar */
  .nav-bar {
    height: 88rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    position: relative;
    z-index: 10;
  }
  
  .nav-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #303133;
  }
  
  .date-picker {
    display: flex;
    align-items: center;
  }
  
  .date-text {
    font-size: 28rpx;
    color: #1A73E8;
    margin: 0 8rpx;
  }
  
  /* Main Content */
  .content {
    flex: 1;
    padding: 16rpx 32rpx;
    box-sizing: border-box;
  }
  
  /* Calendar Section */
  .calendar-section {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .weekdays {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }
  
  .weekday {
    width: calc((100% - 64rpx) / 7);
    text-align: center;
    font-size: 24rpx;
    color: #909399;
  }
  
  .days-grid {
    display: flex;
    flex-wrap: wrap;
  }
  
  .day-item {
    width: calc((100% - 64rpx) / 7);
    height: 68rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 8rpx;
    font-size: 28rpx;
    color: #303133;
  }
  
  .other-month {
    color: #C0C4CC;
  }
  
  .current-day {
    position: relative;
  }
  
  .day-number {
    color: #1A73E8;
  }
  
  .day-dot {
    position: absolute;
    bottom: 4rpx;
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background-color: #1A73E8;
  }
  
  .selected-day {
    width: 56rpx;
    height: 56rpx;
    border: 4rpx solid #1A73E8;
    border-radius: 50%;
    background-color: #E6F4FF;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Tasks Section */
  .tasks-section {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #1A73E8;
    margin-bottom: 24rpx;
    display: block;
  }
  
  .task-list {
    margin-bottom: 32rpx;
  }
  
  .task-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
  }
  
  .task-bullet {
    color: #909399;
    margin-right: 16rpx;
    font-size: 28rpx;
  }
  
  .task-text {
    font-size: 28rpx;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
  
  .start-button {
    width: 224rpx;
    height: 72rpx;
    font-size: 28rpx;
    font-weight: 500;
    margin: 0 auto;
    border-radius: 36rpx;
  }
  
  /* Future Plans */
  .future-plans {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }
  
  .plan-item {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }
  
  .plan-date {
    font-size: 28rpx;
    font-weight: 500;
    color: #303133;
  }
  
  .plan-content {
    display: flex;
    align-items: center;
  }
  
  .plan-text {
    font-size: 28rpx;
    color: #303133;
  }
  
  /* Tab Bar */
  .tab-bar {
    height: 112rpx;
    background-color: #FFFFFF;
    border-top: 2rpx solid #EBEEF5;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .tab-text {
    font-size: 24rpx;
    color: #909399;
    margin-top: 8rpx;
  }
  
  .tab-item.active .tab-text {
    color: #1A73E8;
  }
  </style>
  
  