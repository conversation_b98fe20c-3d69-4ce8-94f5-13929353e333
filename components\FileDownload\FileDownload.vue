<template>
  <view class="action-btn" @click="handleDownload">
    <uni-icons type="download" size="14" color="#1A73E8"></uni-icons>
    <text class="action-text">下载</text>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import uploads from '@/api/upload.js'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

// 判断是否为图片类型
const isImageType = (fileType) => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  return imageTypes.includes(fileType?.toLowerCase())
}

const handleDownload = async () => {
  const filePath = props.item.filePath
  const fileUrl = await uploads.getFileUrl(filePath)
  if (!fileUrl) {
    uni.showToast({ title: '文件路径不存在', icon: 'none' })
    return
  }

  const fileName = props.item.fileName || '未命名文件'
  const fileType = props.item.fileType ? '.' + props.item.fileType : ''
  const fullFileName = fileName + fileType

  // #ifdef MP-WEIXIN
  // 在微信小程序环境下，检查文件类型
  if (!isImageType(props.item.fileType)) {
    uni.showToast({ 
      title: '暂不支持下载该文件格式，仅支持图片文件', 
      icon: 'none',
      duration: 3000
    })
    return
  }
  // #endif

  uni.showLoading({ title: '下载中...' })

  // #ifdef MP-WEIXIN
  wx.downloadFile({
    url: fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        // 对于图片文件，使用 wx.saveImageToPhotosAlbum 保存到相册
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.showToast({ 
              title: '图片已保存到相册', 
              icon: 'success',
              duration: 2000
            })
          },
          fail: (err) => {
            console.log('保存到相册失败:', err)
            // 如果保存到相册失败，降级使用 saveFile
            wx.saveFile({
              tempFilePath: res.tempFilePath,
              success: () => {
                uni.showToast({ 
                  title: '文件已保存到小程序缓存', 
                  icon: 'success',
                  duration: 2000
                })
              },
              fail: () => {
                uni.showToast({ title: '保存失败，请重试', icon: 'none' })
              }
            })
          }
        })
      } else {
        uni.showToast({ title: '下载失败，请检查网络连接', icon: 'none' })
      }
    },
    fail: (err) => {
      console.log('下载失败:', err)
      uni.showToast({ title: '下载失败，请检查网络连接', icon: 'none' })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif

  // #ifdef H5
  const a = document.createElement('a')
  a.href = fileUrl
  a.download = fullFileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  uni.hideLoading()
  // #endif

  // #ifndef MP-WEIXIN || H5
  uni.hideLoading()
  uni.showToast({
    title: '当前平台不支持下载，请在浏览器中打开',
    icon: 'none'
  })
  // #endif
}
</script>

<style scoped>
.action-btn {
  display: flex;
  align-items: center;
}
.action-text {
  margin-left: 4rpx;
}
</style>
