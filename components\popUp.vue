<template>
    <view class="action-bar" :style="mergedBarStyle">
      <button  
        v-for="(btn, index) in normalizedButtons"
        :key="index"
        class="action-btn"
        :style="[getBtnStyle(btn), btn.style]"
        @click="handleClick(btn)"
      >
        <slot :name="btn.slotName" :btn="btn">
          {{ btn.text }}
        </slot>
      </button>
    </view>
  </template>
  
  <script setup>
  import { computed } from 'vue'
  
  const props = defineProps({
    buttons: {
      type: Array,
      default: () => []
    },
    barStyle: {
      type: Object,
      default: () => ({})
    },
    btnCommon: {
      type: Object,
      default: () => ({
        height: '80rpx',
        lineHeight: '80rpx',
        fontSize: '28rpx',
        margin: '0 16rpx',
        flex: '1'
      })
    }
  })
  
  const emit = defineEmits(['click'])
  
  // 默认按钮配置
  const defaultButtons = [
    {
      text: '取消',
      type: 'cancel',
      slotName: 'cancel',
      bgColor: '#f5f5f5',
      textColor: '#666'
    },
    {
      text: '草稿',
      type: 'draft',
      slotName: 'draft',
      bgColor: '#fff',
      textColor: '#1a73e8',
      border: '1px solid #1a73e8'
    },
    {
      text: '提交',
      type: 'submit',
      slotName: 'submit',
      bgColor: '#1a73e8',
      textColor: '#fff'
    }
  ]
  
  // 处理后的按钮数组（解决 map undefined 问题）
  const normalizedButtons = computed(() => 
    props.buttons?.length > 0 ? props.buttons : defaultButtons
  )
  
  // 合并后的工具栏样式
  const mergedBarStyle = computed(() => ({
    backgroundColor: '#fff',
    borderTop: '1px solid #f0f0f0',
    padding: '24rpx 32rpx',
    ...props.barStyle
  }))
  
  // 按钮样式生成器
  const getBtnStyle = (btn) => ({
    backgroundColor: btn.bgColor,
    color: btn.textColor,
    border: btn.border || 'none',
    ...props.btnCommon
  })
  
  // 点击事件处理
  const handleClick = (btn) => {
    emit('click', btn)
    emit(btn.type, btn) // 自动触发对应类型事件
  }
  </script>
  
  <style scoped>
  .action-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    z-index: 1;
  }
  
  .action-btn {
    box-sizing: border-box;
    white-space: nowrap;
    text-align: center;
    transition: opacity 0.3s;
  }
  
  .action-btn:active {
    opacity: 0.7;
  }
  </style>