<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
    <view class="transform-table">
        <view class="transform-table__form">
            <uv-form labelPosition="top" label-width="120px" :model="processForm" :rules="rules" ref="form">
                <uv-form-item label="文件导入">
                    <SimpleFileUpload v-model="processForm.processDiagramUrl" 
                        :service-name="'compliancelistservice'" 
                        :category-name="'ai'"
                        placeholder="上传流程图或相关文档"
                        tip-text="支持上传图片、文档等文件，单个文件不超过10MB" 
                        @upload-success="handleUploadSuccess" />
                </uv-form-item>
                <view class="card-section">
                    <view class="section-title">
                        <text class="section-title-text">业务流程区</text>
                    </view>
                    <uv-form-item prop="businessDomainName" required label="业务领域">
                        <uv-input v-model="processForm.businessDomainName" placeholder="请输入业务领域"></uv-input>
                    </uv-form-item>
                    <uv-form-item v-if="showCustomBusinessArea" prop="businessDomainType" label="自定义业务领域">
                        <uv-input v-model="processForm.businessDomainType" placeholder="请输入自定义业务领域"></uv-input>
                    </uv-form-item>
                    <uv-form-item prop="businessProcess" required label="业务流程描述">
                        <view class="textarea-container">
                            <textarea autoHeight class="own-textarea" placeholder-style="color:#dadbde" maxlength="9999"
                                v-model="processForm.businessProcess" placeholder="请描述具体的业务流程"></textarea>
                        </view>
                    </uv-form-item>
                </view>
                <!-- 管控环节区 -->
                <view class="card-section">
                    <view class="section-title">
                        <text class="section-title-text">管控环节区</text>
                        <view class="ai-tag" @click="handleAddControlLink">
                            <uni-icons type="star" size="14" color="#67C23A"></uni-icons>
                            <text class="ai-tag-text">添加环节</text>
                        </view>
                    </view>
                    <view v-for="(link, index) in controlLinks" :key="index" class="control-link-card">
                        <view class="card-header">
                            <text class="card-title">管控环节</text>
                            <view class="remove-btn" @click="removeControlLink(index)">
                                <uni-icons type="close" size="16" color="#999"></uni-icons>
                            </view>
                        </view>
                        <uv-form-item :prop="`controlLinks.${index}.controlLinkName`" required label="环节节点名称">
                            <uv-input v-model="link.controlLinkName" placeholder="请输入环节节点名称"></uv-input>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.linkDescription`" label="节点描述">
                            <uv-textarea v-model="link.linkDescription" autoHeight placeholder="请输入节点描述"></uv-textarea>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.approvalDepartment`" label="审批部门">
                            <uv-input v-model="link.approvalDepartment" placeholder="请输入审批部门"></uv-input>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.approver`" label="审批人">
                            <uv-input v-model="link.approver" placeholder="请输入审批人"></uv-input>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.requiresGeneralManager`" label="是否需要总经理审批">
                            <uv-checkbox v-model="link.requiresGeneralManager" shape="square">需要总经理审批</uv-checkbox>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.requiresChairman`" label="是否需要董事长审批">
                            <uv-checkbox v-model="link.requiresChairman" shape="square">需要董事长审批</uv-checkbox>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.riskPoints`" label="风险点">
                            <uv-textarea autoHeight maxlength="9999" v-model="link.riskPoints"
                                placeholder="请输入风险点"></uv-textarea>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.riskDescriptions`" label="风险描述">
                            <uv-textarea autoHeight maxlength="9999" v-model="link.riskDescriptions"
                                placeholder="请输入风险描述"></uv-textarea>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.complianceRequirements`" label="合规要求">
                            <uv-textarea autoHeight maxlength="9999" v-model="link.complianceRequirements"
                                placeholder="请输入合规要求"></uv-textarea>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.complianceBasis`" label="合规依据">
                            <uv-textarea autoHeight maxlength="9999" v-model="link.complianceBasis"
                                placeholder="请输入合规依据"></uv-textarea>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.controlMeasures`" label="管控措施">
                            <uv-textarea autoHeight maxlength="9999" v-model="link.controlMeasures"
                                placeholder="请输入管控措施"></uv-textarea>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.responsibleDepartments`" label="责任部门">
                            <view class="selector-container" @click="openDepartmentSelector(index)">
                                <view v-if="link.responsibleDepartments && link.responsibleDepartments.length > 0"
                                    class="selected-items">
                                    <view v-for="(dept, deptIndex) in link.responsibleDepartments"
                                        :key="dept.departmentId || deptIndex" class="selected-tag"
                                        :class="{ 'external-tag': dept.isExternal }">
                                        <text class="tag-text">{{ dept.departmentName }}</text>
                                        <text v-if="dept.isExternal" class="external-mark">(外部)</text>
                                        <view class="tag-close" @click.stop="removeDepartment(index, deptIndex)">
                                            <uni-icons type="close" size="12" color="#999"></uni-icons>
                                        </view>
                                    </view>
                                </view>
                                <view v-else class="placeholder-text">请选择责任部门</view>
                                <uni-icons type="right" size="16" color="#999"></uni-icons>
                            </view>
                        </uv-form-item>
                        <uv-form-item :prop="`controlLinks.${index}.responsiblePositions`" label="责任岗位">
                            <view class="selector-container" @click="openPositionSelector(index)">
                                <view v-if="link.responsiblePositions && link.responsiblePositions.length > 0"
                                    class="selected-items">
                                    <view v-for="(pos, posIndex) in link.responsiblePositions"
                                        :key="pos.positionId || posIndex" class="selected-tag"
                                        :class="{ 'external-tag': pos.isExternal }">
                                        <text class="tag-text">{{ pos.positionName }}</text>
                                        <text v-if="pos.isExternal" class="external-mark">(外部)</text>
                                        <view class="tag-close" @click.stop="removePosition(index, posIndex)">
                                            <uni-icons type="close" size="12" color="#999"></uni-icons>
                                        </view>
                                    </view>
                                </view>
                                <view v-else class="placeholder-text">请选择责任岗位</view>
                                <uni-icons type="right" size="16" color="#999"></uni-icons>
                            </view>
                        </uv-form-item>
                    </view>
                </view>
                <!-- 工作文档 -->
                <view class="card-section">
                    <view class="section-title">
                        <text class="section-title-text">工作文档</text>
                    </view>
                    <uv-form-item>
                        <UploadFile v-model="processForm.processFiles" :auto-upload="true"
                            :service-name="'compliancelistservice'" :category-name="'ai'" />
                    </uv-form-item>
                </view>
            </uv-form>
        </view>
        <!-- 底部操作栏 -->
        <!-- 只有在数据加载完成后才显示底部操作栏，避免闪动效果 -->
        <view v-if="isDataLoaded">
            <!-- 律师在PENDING状态下可以操作 -->
            <view v-if="lawyer && processForm.approvalStatus === 'PENDING'">
                <FooterBar @click="handleFooterClick" :buttons="footerButtons" />
            </view>
            <!-- 非律师在草稿状态或无状态下可以操作 -->
            <view v-if="!lawyer && (processForm.approvalStatus === 'DRAFT' || !processForm.approvalStatus)">
                <FooterBar @click="handleFooterClick" :buttons="footerButtons" />
            </view>
        </view>

        <!-- 进度弹窗 -->
        <uni-popup ref="progressDialog" type="dialog">
            <view class="dialog-content">
                <text class="dialog-title">猫伯伯智能识别中</text>
                <view class="progress-body">
                    <image src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
                        style="width: 48px; height: 48px;" class="pulse"></image>
                    <text class="progress-text">{{ currentProgress }}</text>
                </view>
            </view>
        </uni-popup>

        <!-- 部门选择弹窗 -->
        <uni-popup ref="departmentPopup" type="bottom">
            <view class="selector-popup">
                <view class="popup-header">
                    <text class="popup-title">选择责任部门</text>
                    <view class="popup-actions">
                        <text class="action-btn" @click="closeDepartmentSelector">取消</text>
                        <text class="action-btn confirm" @click="confirmDepartmentSelection">确定</text>
                    </view>
                </view>
                <view class="popup-content">
                    <view v-for="dept in departments" :key="dept.id" class="checkbox-item"
                        @click="toggleDepartmentSelection(dept)">
                        <uv-checkbox :checked="isDepartmentSelected(dept)" @change="toggleDepartmentSelection(dept)">
                            {{ dept.name }}
                        </uv-checkbox>
                    </view>
                </view>
            </view>
        </uni-popup>

        <!-- 岗位选择弹窗 -->
        <uni-popup ref="positionPopup" type="bottom">
            <view class="selector-popup">
                <view class="popup-header">
                    <text class="popup-title">选择责任岗位</text>
                    <view class="popup-actions">
                        <text class="action-btn" @click="closePositionSelector">取消</text>
                        <text class="action-btn confirm" @click="confirmPositionSelection">确定</text>
                    </view>
                </view>
                <view class="popup-content">
                    <view v-for="pos in positions" :key="pos.id" class="checkbox-item"
                        @click="togglePositionSelection(pos)">
                        <uv-checkbox :checked="isPositionSelected(pos)" @change="togglePositionSelection(pos)">
                            {{ pos.name }}
                        </uv-checkbox>
                    </view>
                </view>
            </view>
        </uni-popup>

    </view>
</template>
<script setup>
import {
    ref,
    computed,
    onMounted,
    watch
} from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import pickerInput from '@/components/picker-input.vue'
import UploadFile from '@/components/uploadFile.vue'
import SimpleFileUpload from '@/components/SimpleFileUpload.vue'
import FooterBar from '@/components/footerBar.vue'
import threeAI from '@/api/threeAI/threeAI.js'
import addForm from '@/api/duties/addForm.js'
import { useUserStore } from '@/store/pinia.js'
import getDictData from '@/utils/dict.js'

// 使用 uni.showToast 替代自定义 useToast
const userStore = useUserStore()

const lawyer = ref(false);
lawyer.value = userStore.lawyer

// 提交状态控制
const isSubmitting = ref(false)

// AI生成状态控制
const isGenerating = ref(false)

// 数据加载状态控制
const isDataLoaded = ref(false)

// 页面引用
const form = ref()
const progressDialog = ref()
const departmentPopup = ref()
const positionPopup = ref()

// 页面参数
const isAdd = ref('')
const processId = ref('')

// 进度提示
const currentProgress = ref('')

// 会话ID
const conversationId = ref('')

// 部门和岗位数据
const departments = computed(() => userStore.getDepartments() || [])
const positions = computed(() => userStore.getPostList() || [])

// 当前选择状态
const currentControlLinkIndex = ref(-1)
const selectedDepartments = ref([])
const selectedPositions = ref([])

// 解析AI返回的责任部门/岗位数据
const parseResponsibleData = (data) => {
    if (!data) return []

    // 如果已经是数组，直接返回
    if (Array.isArray(data)) {
        return data.map(item => ({
            ...item,
            isExternal: !item.departmentId && !item.positionId // 标记外部数据（无ID）
        }))
    }

    // 如果是字符串，尝试解析JSON
    if (typeof data === 'string') {
        try {
            const parsed = JSON.parse(data)
            if (Array.isArray(parsed)) {
                return parsed.map(item => ({
                    ...item,
                    isExternal: !item.departmentId && !item.positionId // 标记外部数据（无ID）
                }))
            }
        } catch (e) {
            console.warn('解析责任数据失败:', e)
        }
    }

    return []
}

// 删除已选择的部门
const removeDepartment = (linkIndex, deptIndex) => {
    const dept = controlLinks.value[linkIndex].responsibleDepartments[deptIndex]

    // 如果有ID，从弹窗选择状态中移除
    if (dept.departmentId && currentControlLinkIndex.value === linkIndex) {
        const selectedIndex = selectedDepartments.value.findIndex(item => item.departmentId === dept.departmentId)
        if (selectedIndex > -1) {
            selectedDepartments.value.splice(selectedIndex, 1)
        }
    }

    // 从控制环节中移除
    controlLinks.value[linkIndex].responsibleDepartments.splice(deptIndex, 1)
}

// 删除已选择的岗位
const removePosition = (linkIndex, posIndex) => {
    const pos = controlLinks.value[linkIndex].responsiblePositions[posIndex]

    // 如果有ID，从弹窗选择状态中移除
    if (pos.positionId && currentControlLinkIndex.value === linkIndex) {
        const selectedIndex = selectedPositions.value.findIndex(item => item.positionId === pos.positionId)
        if (selectedIndex > -1) {
            selectedPositions.value.splice(selectedIndex, 1)
        }
    }

    // 从控制环节中移除
    controlLinks.value[linkIndex].responsiblePositions.splice(posIndex, 1)
}

// 文件上传
const fileData = ref([])

// 业务领域选项
const businessAreas = ref([])

// 业务领域映射
const businessAreaMap = ref({})

// 详情的时候需要个反映射，通过name获取value
const businessAreaValueMap = ref({})


// 部门选项（已移除，改为直接输入）

// 表单数据
const processForm = ref({
    businessDomainName: '',
    businessDomainType: '',
    businessProcess: '',
    processDiagramUrl: '',
    processFiles: [],
    approvalStatus: null
})

const showCustomBusinessArea = ref(false)

// 管控环节数据
const controlLinks = ref([])



// 表单验证规则
const rules = ref({
    businessDomainName: [{
        required: true,
        message: '请选择业务领域',
        trigger: 'change'
    }],
    businessProcess: [{
        required: true,
        message: '请输入业务流程描述',
        trigger: 'blur'
    }]
})

// 底部按钮配置
const footerButtons = computed(() => [
    {
        text: '保存草稿',
        type: 'draft',
        slotName: 'draft',
        bgColor: '#fff',
        textColor: '#1a73e8',
        border: '1px solid #1a73e8',
        isHidden: lawyer.value
    },
    {
        text: '业务流程智能识别',
        type: 'ai',
        slotName: 'ai',
        bgColor: '#fff',
        textColor: '#1a73e8',
        border: '1px solid #1a73e8'
    },
    {
        text: lawyer.value ? '发布' : '提交审核',
        type: 'submit',
        slotName: 'submit',
        bgColor: '#1a73e8',
        textColor: '#fff'
    }
])
// 处理业务领域选择变化
const handleBusinessAreaChange = (value) => {
    showCustomBusinessArea.value = value === 'other'
    if (value !== 'other') {
        processForm.value.businessDomainType = ''
    }
}

// 删除管控环节
const removeControlLink = (index) => {
    controlLinks.value.splice(index, 1)
}



// 文件上传成功回调
const handleUploadSuccess = (fileInfo) => {
    console.log('文件上传成功:', fileInfo)
}

// 智能生成业务流程
const handleRiskGenerate = async () => {
    console.log('开始业务流程智能识别...')

    // 防重复点击判断
    if (isGenerating.value) {
        uni.showToast({
            title: '正在生成中，请稍候...',
            icon: 'none',
            duration: 2000
        })
        return
    }

    // 检查业务领域是否为空
    if (!processForm.value.businessDomainName || processForm.value.businessDomainName.trim() === '') {
        uni.showToast({
            title: '请先填写业务领域',
            icon: 'none',
            duration: 2000
        })
        return
    }

    // 检查业务流程描述是否为空
    // if (!processForm.value.businessProcess || processForm.value.businessProcess.trim() === '') {
    //     uni.showToast({
    //         title: '请先填写业务流程描述',
    //         icon: 'none',
    //         duration: 2000
    //     })
    //     return
    // }

    // 检查progressDialog是否存在
    // if (!progressDialog.value) {
    //     console.error('progressDialog ref 未找到')
    //     uni.showToast({ title: 'progressDialog未初始化', icon: 'none' })
    //     return
    // }

    try {
        // 设置生成状态
        isGenerating.value = true

        progressDialog.value.open()
        currentProgress.value = '正在进行业务流程智能识别...'
        console.log('弹窗已打开，开始识别')

        // 构建请求参数
        const params = {
            businessDomain: processForm.value.businessDomainName,
            processDiagramUrl: processForm.value.processDiagramUrl || '',
            toolKey: 'deepseek',
            conversationId: conversationId.value
        }

        currentProgress.value = '正在分析业务流程...'

        // 调用AI接口进行业务流程分析
        const result = await threeAI.getProcessAI(params)

        if (result && result.success && result.processData) {
            currentProgress.value = '正在填充表单数据...'

            const processData = result.processData

            // 填充基本信息
            // if (processData.businessDomainName) {
            //     processForm.value.businessDomainName = processData.businessDomainName
            // }
            if (processData.businessDomainType) {
                processForm.value.businessDomainType = processData.businessDomainType
            }
            if (processData.businessProcess) {
                processForm.value.businessProcess = processData.businessProcess
            }
            if (processData.processDescription) {
                processForm.value.processDescription = processData.processDescription
            }

            // 填充管控环节数据
            if (processData.controlLinks && processData.controlLinks.length > 0) {
                controlLinks.value = processData.controlLinks.map(link => ({
                    controlLinkName: link.controlLinkName || '',
                    linkDescription: link.linkDescription || '',
                    approvalDepartment: link.approvalDepartment || '',
                    approver: link.approver || '',
                    requiresGeneralManager: link.requiresGeneralManager || false,
                    requiresChairman: link.requiresChairman || false,
                    riskPoints: link.controlDetail?.riskPoints || '',
                    riskDescriptions: link.controlDetail?.riskDescriptions || '',
                    complianceRequirements: link.controlDetail?.complianceRequirements || '',
                    complianceBasis: link.controlDetail?.complianceBasis || '',
                    controlMeasures: link.controlDetail?.controlMeasures || '',
                    responsibleDepartments: parseResponsibleData(link.controlDetail?.responsibleDepartments) || [],
                    responsiblePositions: parseResponsibleData(link.controlDetail?.responsiblePositions) || []
                }))
            }

            // 填充工作文档（如果有返回）
            if (processData.processFiles && processData.processFiles.length > 0) {
                processForm.value.processFiles = processData.processFiles.map(doc => ({
                    name: doc.fileName,
                    url: doc.fileUrl,
                    type: doc.fileType,
                    size: doc.fileSize
                }))
            }

            currentProgress.value = '识别完成！'

            // 延迟关闭弹窗，让用户看到完成状态
            setTimeout(() => {
                progressDialog.value.close()
                uni.showToast({
                    title: '业务流程智能识别完成',
                    icon: 'success',
                    duration: 2000
                })
            }, 1000)

        } else {
            progressDialog.value.close()
            uni.showToast({
                title: result?.message || '识别失败，请重试',
                icon: 'none',
                duration: 2000
            })
        }

    } catch (error) {
        console.error('业务流程识别过程中出错:', error)
        if (progressDialog.value) {
            progressDialog.value.close()
        }
        uni.showToast({
            title: '识别失败，请重试',
            icon: 'none',
            duration: 2000
        })
    } finally {
        // 重置生成状态
        isGenerating.value = false
    }
}

// 添加环节（直接生成）
const handleAddControlLink = () => {
    console.log('直接添加管控环节')

    // 直接生成并新增一份管控环节
    const newControlLink = {
        controlLinkName: ``,
        linkDescription: '',
        approvalDepartment: '',
        approver: '',
        requiresGeneralManager: false,
        requiresChairman: false,
        riskPoints: '',
        riskDescriptions: '',
        complianceRequirements: '',
        complianceBasis: '',
        controlMeasures: '',
        responsibleDepartments: [],
        responsiblePositions: []
    }

    // 添加到管控环节
    controlLinks.value.push(newControlLink)
    console.log('管控环节已添加，当前环节数量:', controlLinks.value.length)

    uni.showToast({
        title: '环节已添加',
        icon: 'success',
        duration: 1500
    })
}

// 部门选择相关方法
const openDepartmentSelector = (index) => {
    currentControlLinkIndex.value = index
    // 初始化已选择的部门（只包含有ID的部门，外部部门不参与弹窗选择）
    selectedDepartments.value = (controlLinks.value[index].responsibleDepartments || [])
        .filter(dept => dept.departmentId)
        .map(dept => ({ ...dept }))
    departmentPopup.value.open()
}

const closeDepartmentSelector = () => {
    departmentPopup.value.close()
    selectedDepartments.value = []
    currentControlLinkIndex.value = -1
}

const toggleDepartmentSelection = (dept) => {
    const index = selectedDepartments.value.findIndex(item => item.departmentId === dept.id)
    if (index > -1) {
        selectedDepartments.value.splice(index, 1)
    } else {
        selectedDepartments.value.push({
            departmentId: dept.id,
            departmentName: dept.name
        })
    }
}

const isDepartmentSelected = (dept) => {
    return selectedDepartments.value.some(item => item.departmentId === dept.id)
}

const confirmDepartmentSelection = () => {
    if (currentControlLinkIndex.value >= 0) {
        // 保留外部数据（无ID的部门），合并弹窗选择的数据
        const externalDepts = (controlLinks.value[currentControlLinkIndex.value].responsibleDepartments || [])
            .filter(dept => !dept.departmentId)
        controlLinks.value[currentControlLinkIndex.value].responsibleDepartments = [...externalDepts, ...selectedDepartments.value]
    }
    closeDepartmentSelector()
}

// 岗位选择相关方法
const openPositionSelector = (index) => {
    currentControlLinkIndex.value = index
    // 初始化已选择的岗位（只包含有ID的岗位，外部岗位不参与弹窗选择）
    selectedPositions.value = (controlLinks.value[index].responsiblePositions || [])
        .filter(pos => pos.positionId)
        .map(pos => ({ ...pos }))
    positionPopup.value.open()
}

const closePositionSelector = () => {
    positionPopup.value.close()
    selectedPositions.value = []
    currentControlLinkIndex.value = -1
}

const togglePositionSelection = (pos) => {
    const index = selectedPositions.value.findIndex(item => item.positionId === pos.id)
    if (index > -1) {
        selectedPositions.value.splice(index, 1)
    } else {
        selectedPositions.value.push({
            positionId: pos.id,
            positionName: pos.name
        })
    }
}

const isPositionSelected = (pos) => {
    return selectedPositions.value.some(item => item.positionId === pos.id)
}

const confirmPositionSelection = () => {
    if (currentControlLinkIndex.value >= 0) {
        // 保留外部数据（无ID的岗位），合并弹窗选择的数据
        const externalPositions = (controlLinks.value[currentControlLinkIndex.value].responsiblePositions || [])
            .filter(pos => !pos.positionId)
        controlLinks.value[currentControlLinkIndex.value].responsiblePositions = [...externalPositions, ...selectedPositions.value]
    }
    closePositionSelector()
}



// 通用提交函数，减少代码冗余
function submitProcessData(type = 'submit') {
    uni.showLoading()

    // 根据类型设置不同的审批状态和消息
    let approvalStatus
    let successMessage
    let apiMethod

    switch (type) {
        case 'draft':
            approvalStatus = 'DRAFT'
            successMessage = '草稿保存成功'
            // 如果有详情ID，使用更新接口；否则使用创建草稿接口
            apiMethod = processId.value ? addForm.updateProcess : addForm.createDraftProcess
            break
        case 'submit':
            approvalStatus = 'PENDING'
            successMessage = '提交成功'
            apiMethod = addForm.addProcess
            break
        case 'update':
            approvalStatus = 'APPROVED'
            successMessage = '更新成功'
            apiMethod = addForm.updateProcess
            break
        default:
            approvalStatus = 'PENDING'
            successMessage = '操作成功'
            apiMethod = addForm.addProcess
    }
    // 构建提交数据，按照接口文档要求的格式
    const submitData = {
        bizProcessMainV2Id: processId.value ? +processId.value : null,
        orgUnitId: userStore.userInfo?.orgUnitId || null,
        businessDomainName: processForm.value.businessDomainName,
        businessDomainType: processForm.value.businessDomainType,
        businessProcess: processForm.value.businessProcess,
        processDescription: processForm.value.processDescription,
        processDiagramUrl: processForm.value.processDiagramUrl || '',
        processStatus: 'ACTIVE',
        approvalStatus: approvalStatus,
        controlLinks: controlLinks.value.map((link, index) => ({
            controlLinkName: link.controlLinkName || '',
            linkOrder: index + 1,
            linkDescription: link.linkDescription || '',
            approvalDepartment: link.approvalDepartment || '',
            approver: link.approver || '',
            requiresGeneralManager: link.requiresGeneralManager || false,
            requiresChairman: link.requiresChairman || false,
            controlDetail: {
                riskPoints: link.riskPoints || '',
                riskDescriptions: link.riskDescriptions || '',
                complianceRequirements: link.complianceRequirements || '',
                complianceBasis: link.complianceBasis || '',
                controlMeasures: link.controlMeasures || '',
                responsibleDepartments: JSON.stringify(Array.isArray(link.responsibleDepartments)
                    ? link.responsibleDepartments.map(dept => ({
                        departmentName: dept.departmentName || '',
                        departmentId: dept.departmentId || ''
                    }))
                    : []),
                responsiblePositions: JSON.stringify(Array.isArray(link.responsiblePositions)
                    ? link.responsiblePositions.map(pos => ({
                        positionName: pos.positionName || '',
                        positionId: pos.positionId || ''
                    }))
                    : [])
            }
        })),
        processFiles: processForm.value.processFiles.map(doc => ({
            fileName: doc.fileName,
            fileUrl: doc.key,
            fileSize: doc.size,
            fileType: doc.fileType,
        }))
    }

    // 如果是更新操作或草稿保存且有详情ID，需要传递ID参数
    if ((type === 'update' || (type === 'draft' && processId.value)) && processId.value) {
        apiMethod(submitData, processId.value).then(res => {
            handleApiResponse(res, successMessage, type)
        }).catch(err => {
            handleApiError(err, type)
        })
    } else {
        // 新增和草稿操作（无ID）
        apiMethod(submitData).then(res => {
            handleApiResponse(res, successMessage, type)
        }).catch(err => {
            handleApiError(err, type)
        })
    }
}

// 处理API响应
function handleApiResponse(res, successMessage, type) {
    uni.showToast({
        title: successMessage,
        icon: 'success',
        duration: 2000
    })

    // 草稿保存成功后，如果processId不存在，将返回的bizProcessMainV2Id赋值给processId
    if (type === 'draft' && res) {
        processId.value = res.bizProcessMainV2Id
    }

    // 草稿保存不返回上一页
    if (type !== 'draft') {
        setTimeout(() => {
            if (type !== 'draft') {
                uni.navigateBack({
                    delta: 1
                })
            }
        }, 800)
    }

    uni.hideLoading()
    isSubmitting.value = false
}

// 处理API错误
function handleApiError(err, type) {
    console.error('提交失败:', err)
    uni.showToast({
        title: type === 'draft' ? '保存失败，请重试' : '提交失败，请重试',
        icon: 'none',
        duration: 2000
    })

    uni.hideLoading()
    isSubmitting.value = false
}

// 保存草稿
function saveProcessDraft() {
    submitProcessData('draft')
}

// 提交审核
function submitProcess() {
    submitProcessData('submit')
}

// 更新流程
function updateProcess() {
    submitProcessData('update')
}

// 页面加载时获取参数
onLoad(async (options) => {
    // 获取业务领域字典数据
    try {
        const dictData = await getDictData('88', '')
        if (dictData) {
            businessAreas.value = dictData
            businessAreaMap.value = dictData.reduce((acc, item) => {
                acc[item.value] = item.name
                return acc
            }, {})
            businessAreaValueMap.value = dictData.reduce((acc, item) => {
                acc[item.name] = item.value
                return acc
            }, {})
        }
    } catch (error) {
        console.error('获取业务领域字典失败:', error)
        // 如果获取字典失败，使用默认数据
        businessAreas.value = []
    }
    isAdd.value = options.type
    if (options.type === 'detail' && options.id) {
        processId.value = options.id
        loadDetailData()
    } else {
        // 如果不是详情页面，直接设置数据加载完成
        isDataLoaded.value = true
    }

    // 获取会话ID
    try {
        const aiID = await threeAI.getSessionId()
        conversationId.value = aiID.conversationId
    } catch (error) {
        console.error('获取会话ID失败:', error)
    }

})

// 加载详情数据
function loadDetailData() {
    uni.showLoading({
        title: '加载中...'
    })

    addForm.getProcessDetail(processId.value).then(res => {
        uni.hideLoading()
        if (res) {
            console.log('流程详情数据:', res)
            // 回填表单数据
            if (res.businessDomainName) {
                processForm.value.businessDomainName = res.businessDomainName
            }
            if (res.businessDomainType) {
                processForm.value.businessDomainType = res.businessDomainType
            }
            if (res.businessProcess) {
                processForm.value.businessProcess = res.businessProcess
            }
            // 回填审批状态
            if (res.approvalStatus) {
                processForm.value.approvalStatus = res.approvalStatus
            }

            // 回填流程图数据
            if (res.processDiagramUrl) {
                processForm.value.processDiagramUrl = res.processDiagramUrl
            }

            // 回填管控环节数据
            if (res.controlLinks && res.controlLinks.length > 0) {
                controlLinks.value = res.controlLinks.map(link => {
                    const controlDetail = link.controlDetail || {}
                    return {
                        controlLinkName: link.controlLinkName || '',
                        linkDescription: link.linkDescription || '',
                        approvalDepartment: link.approvalDepartment || '',
                        approver: link.approver || '',
                        requiresGeneralManager: link.requiresGeneralManager || false,
                        requiresChairman: link.requiresChairman || false,
                        requiresGeneralManagerArray: link.requiresGeneralManager ? ['requiresGeneralManager'] : [],
                        requiresChairmanArray: link.requiresChairman ? ['requiresChairman'] : [],
                        // 从 controlDetail 中获取风险点到职责岗位的数据
                        riskPoints: controlDetail.riskPoints || '',
                        riskDescriptions: controlDetail.riskDescriptions || '',
                        complianceRequirements: controlDetail.complianceRequirements || '',
                        complianceBasis: controlDetail.complianceBasis || '',
                        controlMeasures: controlDetail.controlMeasures || '',
                        responsibleDepartments: parseResponsibleData(controlDetail.responsibleDepartments),
                        responsiblePositions: parseResponsibleData(controlDetail.responsiblePositions)
                    }
                })
            }

            // 回填文件数据
            if (res.processFiles && Array.isArray(res.processFiles)) {
                // 将详情接口返回的数据格式转换为组件所需的格式
                const convertedWorkDocuments = res.processFiles.map(doc => ({
                    name: doc.fileName || '',
                    fileName: doc.fileName || '',
                    url: doc.fileUrl || '',
                    type: doc.fileType || '',
                    size: doc.fileSize || 0,
                    fileSize: doc.fileSize || 0,
                    key: doc.fileUrl || '', // 使用 fileUrl 作为 key
                    filePath: doc.fileUrl || '',
                    fileType: doc.fileType || '',
                    status: 'success' // 标记为已上传成功
                }))

                processForm.value.processFiles = convertedWorkDocuments
                // 同时更新 fileData 以保持一致性
                fileData.value = convertedWorkDocuments
            }

            // 数据加载完成，可以显示底部操作栏
            isDataLoaded.value = true
        }
    }).catch(err => {
        uni.hideLoading()
        console.error('加载流程详情失败:', err)
        uni.showToast({
            title: '加载详情失败',
            icon: 'none',
            duration: 2000
        })

        // 即使加载失败，也要设置数据加载完成，避免界面卡住
        isDataLoaded.value = true
    })
}

// 防抖定时器
let debounceTimer = null;

// 底部按钮点击处理
const handleFooterClick = (btn) => {
    console.log('FooterBar clicked:', btn.type)

    // 对所有按钮添加2秒防抖
    if (debounceTimer) {
        uni.showToast({
            title: '操作过于频繁，请稍后再试',
            icon: 'none',
            duration: 1500
        });
        return;
    }

    // 设置防抖定时器
    debounceTimer = setTimeout(() => {
        debounceTimer = null;
    }, 2000);

    switch (btn.type) {
        case 'draft':
            // 防重复点击
            if (isSubmitting.value) {
                return
            }
            isSubmitting.value = true
            saveProcessDraft()
            break
        case 'ai':
            handleRiskGenerate()
            break
        case 'submit':
            form.value.validate().then(() => {
                // 防重复点击
                if (isSubmitting.value) {
                    return
                }
                isSubmitting.value = true
                if (processId.value && processForm.value.approvalStatus === 'PENDING' && lawyer.value) {
                    updateProcess()
                } else {
                    submitProcess()
                }
            }).catch(() => {
                uni.showToast({ title: '请完善必填信息', icon: 'none' })
            })
            break
    }
}
</script>

<style scoped lang="scss">
.transform-table {
    min-height: 100vh;
}

.transform-table__form {
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 30rpx;
}

.empty-tip {
    text-align: center;
    color: #999;
    font-size: 28rpx;
    padding: 60rpx 0;
}

.control-link-card {
    background: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    position: relative;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
}

.card-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
}

.remove-btn {
    padding: 8rpx 16rpx;
    font-size: 30rpx;
    // background: #ff4757;
    color: white;
    border: none;
    border-radius: 20rpx;
    cursor: pointer;
}

.textarea-container {
    position: relative;
}

/* 进度弹窗样式 */
.progress-dialog {
    background: white;
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    min-width: 400rpx;
    text-align: center;
}

.progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
}

.progress-icon {
    animation: spin 1s linear infinite;
}

.progress-text {
    font-size: 28rpx;
    color: #333;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 选择器容器样式 */
.selector-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
}

.selector-container:hover {
    border-color: #1a73e8;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    flex: 1;
}

.selected-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    background-color: #e3f2fd;
    color: #1976d2;
    border-radius: 12px;
    font-size: 12px;
    line-height: 1.5;
    margin: 2px 4px 2px 0;
    position: relative;
}

.selected-tag.external-tag {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.tag-text {
    margin-right: 4px;
}

.external-mark {
    font-size: 10px;
    color: #ff9800;
    margin-right: 4px;
}

.tag-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    margin-left: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.tag-close:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.external-tag .tag-close {
    background-color: rgba(245, 124, 0, 0.1);
}

.external-tag .tag-close:hover {
    background-color: rgba(245, 124, 0, 0.2);
}

.placeholder-text {
    color: #999;
    flex: 1;
}

/* 弹窗样式 */
.selector-popup {
    background: #fff;
    border-radius: 12px 12px 0 0;
    max-height: 60vh;
    overflow: hidden;
    min-height: 20vh;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.popup-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.popup-actions {
    display: flex;
    gap: 16px;
}

.action-btn {
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.action-btn.confirm {
    color: #1a73e8;
    font-weight: 500;
}

.popup-content {
    max-height: 40vh;
    overflow-y: auto;
    padding: 8px 0;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #f8f8f8;
    cursor: pointer;
}

.checkbox-item:hover {
    background-color: #f5f5f5;
}

.checkbox-item:last-child {
    border-bottom: none;
}

.dialog-content {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    width: 80vw;
    max-width: 600rpx;
    box-sizing: border-box;
}

.dialog-title {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    text-align: center;
    margin-bottom: 30rpx;
}

.progress-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
}

.progress-text {
    font-size: 28rpx;
    color: #666;
    margin-top: 30rpx;
}

.pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}
</style>
