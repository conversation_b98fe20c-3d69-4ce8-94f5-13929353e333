<template>
    <view class="surveyDetail-container">
        <!-- Tab切换区 -->
        <!-- 任务信息卡片 -->
        <view class="task-info-card">
            <view class="task-header">
                <text class="task-title">{{ taskDetail.title || '暂无标题' }}</text>
                <view class="task-status" :class="taskDetail.status?.toLowerCase()">
                    {{ getStatusName(taskDetail.status) }}
                </view>
            </view>
            <view class="task-details">
                <view class="detail-row">
                    <view class="detail-item">
                        <text class="detail-label">调查类型</text>
                        <text class="detail-value">{{ getTypeName(taskDetail.investigateType) }}</text>
                    </view>
                    <view class="detail-item">
                        <text class="detail-label">来源部门</text>
                        <text class="detail-value">{{ getSourceName(taskDetail.investigateSource) }}</text>
                    </view>
                </view>
                <view class="detail-row">
                    <view class="detail-item">
                        <text class="detail-label">风险等级</text>
                        <view class="risk-level" :data-level="taskDetail.level">
                            {{ getLevelName(taskDetail.level) }}
                        </view>
                    </view>
                    <view class="detail-item" v-if="taskDetail.startDate">
                        <text class="detail-label">开始时间</text>
                        <text class="detail-value">{{ taskDetail.startDate }}</text>
                    </view>
                </view>
                <view class="detail-row" v-if="taskDetail.finishDate">
                    <view class="detail-item">
                        <text class="detail-label">完成时间</text>
                        <text class="detail-value">{{ taskDetail.finishDate }}</text>
                    </view>
                </view>
            </view>
        </view>
        
        <!-- Tab切换区 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'record' }" @click="switchTab('record')">
                <uni-icons type="list" size="16px" color="#333"></uni-icons>
                <text>调查记录</text>
            </view>
            <view class="tab-item" :class="{ active: activeTab === 'report' }" @click="switchTab('report')">
                <uni-icons type="compose" size="16px" color="#333"></uni-icons>
                <text>调查报告</text>
            </view>
            <view class="tab-indicator" :style="indicatorStyle"></view>
        </view>
        <!-- 内容区域 -->
        <scroll-view class="content" scroll-y>
            <!-- 调查记录页 -->
            <view v-if="activeTab === 'record'" class="record-page">
                <!-- 有数据时显示详情 -->
                <template v-if="recordData">
                    <view class="info-card">
                        <view class="info-item">
                            <text class="info-label">调查编号：</text>
                            <text class="info-value">{{ recordData.recordCode }}</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">场所：</text>
                            <text class="info-value">{{ recordData.location }}</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">类型：</text>
                            <text class="info-value">{{ formatRecordType(recordData.recordType) }}</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">内容：</text>
                            <text class="info-value">{{ recordData.content }}</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">发现：</text>
                            <text class="info-value">{{ recordData.discover }}</text>
                        </view>
                    </view>
                    <!-- 附件列表 -->
                    <view v-if="recordData.attachmentList && recordData.attachmentList.length > 0">
                        <view class="section-title">附件列表</view>
                        <view class="attachment-list">
                            <view class="attachment-item" v-for="(item, index) in recordData.attachmentList"
                                :key="index">
                                <uni-icons type="paperclip" size="20px" color="#666"></uni-icons>
                                <text class="attachment-name">{{ item.name }}</text>
                                <FilePreview :item="item" />
                            </view>
                        </view>
                    </view>
                    <!-- 涉及人员列表 -->
                    <view v-if="recordData.personList && recordData.personList.length > 0">
                        <view class="section-title">涉及人员列表</view>
                        <view class="person-list">
                            <view class="person-item" v-for="(person, index) in recordData.personList" :key="index">
                                <text>{{ person.name }}</text>
                                <text>{{ person.department }}</text>
                                <text>{{ person.position }}</text>
                            </view>
                        </view>
                    </view>
                </template>

                <!-- 新增按钮 -->
                <view v-else>
                    <uv-button @click="navigateToReview">新增</uv-button>
                </view>
            </view>
            <!-- 调查报告页 -->
            <view v-if="activeTab === 'report'" class="report-page">
                <!-- 有数据时显示详情 -->
                <template v-if="reportData">
                    <!-- 头部信息区 -->
                    <view class="report-header">
                        <view class="report-meta">
                            <text class="report-code">报告编号：{{ reportData.reportCode }}</text>
                            <view class="status-tag" :class="reportData.status.toLowerCase()">
                                {{ formatStatus(reportData.status) }}
                            </view>
                            <view class="level-tag" :class="reportData.level.toLowerCase()">
                                {{ formatLevel(reportData.level) }}
                            </view>
                        </view>
                        <view class="report-title">{{ reportData.title }}</view>
                        <view class="report-source">来源部门：{{ investigateSources[reportData.investigateSource] }}</view>
                    </view>
                    <!-- 主体内容区 -->
                    <view class="report-body">
                        <view class="section-card" v-if="reportData.investigateBackground">
                            <text class="section-title">背景说明</text>
                            <text class="section-content">{{ reportData.investigateBackground }}</text>
                        </view>
                        <view class="section-card" v-if="reportData.investigateMethod">
                            <text class="section-title">调查方法</text>
                            <text class="section-content">{{ reportData.investigateMethod }}</text>
                        </view>
                        <view class="section-card" v-if="reportData.investigateProcess">
                            <text class="section-title">调查过程</text>
                            <text class="section-content">{{ reportData.investigateProcess }}</text>
                        </view>
                        <view class="section-card" v-if="reportData.investigateFound">
                            <text class="section-title">发现问题</text>
                            <text class="section-content">{{ reportData.investigateFound }}</text>
                        </view>
                        <view class="section-card" v-if="reportData.investigateConclusion">
                            <text class="section-title">调查结论</text>
                            <text class="section-content">{{ reportData.investigateConclusion }}</text>
                        </view>
                        <view class="section-card" v-if="reportData.recommendMeasure">
                            <text class="section-title">改进建议/措施</text>
                            <text class="section-content">{{ reportData.recommendMeasure }}</text>
                        </view>
                    </view>
                    <!-- 附件列表 -->
                    <view v-if="reportData.attachmentList && reportData.attachmentList.length > 0">
                        <view class="section-title">附件列表</view>
                        <view class="attachment-list">
                            <view class="attachment-card" v-for="(item, index) in reportData.attachmentList"
                                :key="index">
                                <uni-icons type="paperclip" size="20px" color="#666"></uni-icons>
                                <view class="attachment-info">
                                    <text class="attachment-name">{{ item.name }}</text>
                                    <text class="attachment-desc">{{ item.desc }}</text>
                                </view>
                                <FilePreview :item="item" />
                            </view>
                        </view>
                    </view>
                </template>

                <!-- 新增按钮 -->
                <view v-else>
                    <uv-button @click="navigateToReport">新增</uv-button>
                </view>
            </view>
        </scroll-view>
    </view>
</template>
<script setup>
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import recordApi from '@/api/violation/record.js'
import reportApi from '@/api/violation/report.js'
import taskApi from '@/api/violation/task.js'
import FilePreview from '@/components/FilePreview/FilePreview.vue'
import getDictData from '@/utils/dict.js'

// 页面参数
const taskId = ref('')

// 任务详情数据
const taskDetail = ref({})

// 当前激活的Tab
const activeTab = ref('record')

// 字典数据
const recordTypes = ref([])

// 状态映射
const statusMap = ref({
    "NO_START": '未开始',
    "PROGRESSING": '进行中',
    "FINISHED": '已完成',
    "PAUSED": '已暂停',
    "CANCELED": '已取消'
});

// 优先级映射
const levelMap = ref({
    "LOW": '低',
    "MIDDLE": '中',
    "HIGH": '高'
});

// 调查类型映射
const typeMap = ref({
    "ADVERTISING_COMPLIANCE": '广告合规',
    "SUPPLIER_MANAGEMENT": '供应商管理',
    "EMPLOYEE_TRAINING": '员工培训',
    "FINANCIAL_AUDITING": '财务审计'
});

// 调查来源映射
const sourceMap = ref({
    "MARKETING": '市场部',
    "PROCUREMENT": '采购部',
    "HR": '人力资源部',
    "FINANCE": '财务部'
});

// Tab指示器位置
const indicatorStyle = computed(() => {
    return {
        left: activeTab.value === 'record' ? '25%' : '75%',
        transform: 'translateX(-50%)'
    }
})

// 调查记录数据
const recordData = ref({
    recordCode: 'INV-2023-001',
    location: '总部大楼3层会议室',
    recordType: '人员访谈',
    content: '针对近期财务异常情况进行访谈调查，主要涉及报销流程和审批环节。',
    discover: '发现部分报销单据存在审批不严的情况，存在违规风险。',
    attachmentList: [
        { name: '访谈记录.pdf', url: 'https://example.com/file1.pdf' },
        { name: '现场照片.jpg', url: 'https://example.com/file2.jpg' }
    ],
    personList: [
        { name: '张三', department: '财务部', position: '经理' },
        { name: '李四', department: '行政部', position: '主管' }
    ]
})

// 调查报告数据
const reportData = ref({
    reportCode: 'REP-2023-001',
    title: '关于财务报销违规问题的调查报告',
    investigateSource: '审计部',
    status: 'FINALIZED',
    level: 'HIGH',
    investigateBackground: '近期发现公司财务报销存在异常情况，部分员工报销金额与实际支出不符，存在违规风险。审计部对此展开专项调查。',
    investigateMethod: '采用文件审查、人员访谈和数据分析相结合的方式。',
    investigateProcess: '1. 收集2023年1-6月所有报销单据\n2. 对异常单据进行抽样核查\n3. 访谈相关审批人员和报销申请人\n4. 分析数据异常点',
    investigateFound: '1. 部分报销单据缺少必要凭证\n2. 审批流程存在漏洞，部分审批人未严格审核\n3. 存在虚假报销情况',
    investigateConclusion: '公司财务报销流程存在管理漏洞，部分员工利用漏洞进行违规操作，需立即整改。',
    recommendMeasure: '1. 完善报销审批流程，增加复核环节\n2. 对相关责任人进行问责\n3. 开展财务合规培训\n4. 建立定期审计机制',
    attachmentList: [
        { name: '调查报告.pdf', desc: '完整调查报告', url: 'https://example.com/report.pdf' },
        { name: '数据分析.xlsx', desc: '异常数据统计', url: 'https://example.com/data.xlsx' },
        { name: '访谈记录.pdf', desc: '相关人员访谈记录', url: 'https://example.com/interview.pdf' }
    ]
})

// 切换Tab
const switchTab = (tab) => {
    activeTab.value = tab
}

// 返回上一页
const handleBack = () => {
    uni.navigateBack()
}

// 格式化状态显示
const formatStatus = (status) => {
    const map = {
        MODIFY: '修改中',
        FINALIZED: '已归档',
        DRAFT: '草稿'
    }
    return map[status] || status
}

// 格式化类型显示
const formatRecordType = (typeValue) => {
    const typeItem = recordTypes.value.find(item => item.value === typeValue)
    return typeItem ? typeItem.label : typeValue
}

// 格式化风险等级显示
const formatLevel = (level) => {
    const map = {
        LOW: '低风险',
        MIDDLE: '中风险',
        HIGH: '高风险'
    }
    return map[level] || level
}

// 数据映射函数
function getStatusName(status) {
    return statusMap.value[status] || status;
}

function getLevelName(level) {
    return levelMap.value[level] || level;
}

function getTypeName(type) {
    return typeMap.value[type] || type;
}

function getSourceName(source) {
    return sourceMap.value[source] || source;
}

// 调查来源选项
const investigateSources = {
    'MARKETING': '市场部',
    'PROCUREMENT': '采购部',
    'HR': '人力资源部',
    'FINANCE': '财务部'
}
// 跳转到调查记录页面
const navigateToReview = () => {
    uni.navigateTo({
        url: `/pages/component/continuousOpt/surveyTask/surveyReview?id=${taskId.value}`
    })
}

// 跳转到调查报告页面
const navigateToReport = () => {
    uni.navigateTo({
        url: `/pages/component/continuousOpt/surveyTask/surveyReport?id=${taskId.value}`
    })
}
async function getDetail(id) {
    try {
        const detail = await taskApi.getInvestigateTaskDetail(id)
        taskDetail.value = detail || {}
        console.log(detail, 'detaildetaildetaildetail')
    } catch (error) {
        console.error('获取任务详情失败:', error)
        taskDetail.value = {}
    }
}
async function getReport(id, params) {
    const report = await reportApi.getInvestigateReportList(id, params)
    if (report.content && report.content.length > 0) {
        reportData.value = report.content[0]
    } else {
        reportData.value = null
    }
    console.log(report, 'reportsreportsreportsreports')
}
async function getRecord(params) {
    const record = await recordApi.getInvestigateRecords(params)
    if (record.content && record.content.length > 0) {
        recordData.value = record.content[0]
    } else {
        recordData.value = null
    }
}
onLoad(async (options) => {
    // 获取页面参数
    if (options && options.id) {
        taskId.value = options.id
        // getDetail(taskId.value)
        // getReport(taskId.value, {page:0, size:1 })
        // getRecord({investigateId:taskId.value,page:0, size:1 })
    }
    
    // 获取记录类型字典数据
    try {
        const dictData = await getDictData('39', null)
        recordTypes.value = dictData.map(item => ({
            label: item.name,
            value: item.value
        }))
    } catch (error) {
        console.error('获取字典数据失败:', error)
    }
})
onShow(() => {
    getDetail(taskId.value)
    getReport(taskId.value, { page: 0, size: 1 })
    getRecord({ investigateId: taskId.value, page: 0, size: 1 })
})
</script>
<style lang="scss" scoped>
.surveyDetail-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f8f9fa;

    /* 任务信息卡片样式 */
    .task-info-card {
        background-color: #fff;
        margin: 20rpx 30rpx;
        border-radius: 16rpx;
        padding: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
        border: 1px solid #f0f0f0;
    }

    .task-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24rpx;
    }

    .task-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.4;
        flex: 1;
        margin-right: 20rpx;
    }

    .task-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
        white-space: nowrap;
    }

    .task-status.no_start {
        background-color: #f0f0f0;
        color: #666;
    }

    .task-status.progressing {
        background-color: #e6f7ff;
        color: #1890ff;
    }

    .task-status.finished {
        background-color: #f6ffed;
        color: #52c41a;
    }

    .task-status.paused {
        background-color: #fff7e6;
        color: #fa8c16;
    }

    .task-status.canceled {
        background-color: #fff1f0;
        color: #f5222d;
    }

    .task-details {
        .detail-row {
            display: flex;
            margin-bottom: 20rpx;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-right: 40rpx;
        }

        .detail-item:last-child {
            margin-right: 0;
        }

        .detail-label {
            font-size: 26rpx;
            color: #999;
            margin-bottom: 8rpx;
        }

        .detail-value {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
        }

        .risk-level {
            display: inline-block;
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 26rpx;
            font-weight: 500;
            text-align: center;
        }

        .risk-level[data-level="HIGH"] {
            background-color: #fff1f0;
            color: #f5222d;
        }

        .risk-level[data-level="MIDDLE"] {
            background-color: #fff7e6;
            color: #fa8c16;
        }

        .risk-level[data-level="LOW"] {
            background-color: #f6ffed;
            color: #52c41a;
        }
    }

    /* Tab切换区样式 */
    .tab-container {
        position: relative;
        display: flex;
        height: 96rpx;
        background-color: #fff;
        margin: 0 30rpx 24rpx;
        border-radius: 16rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
    }

    .tab-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        color: #666;
        transition: all 0.2s ease;
    }

    .tab-item.active {
        color: #1890ff;
        font-weight: 500;
    }

    .tab-item text {
        margin-left: 10rpx;
    }

    .tab-indicator {
        position: absolute;
        bottom: 0;
        width: 120rpx;
        height: 4rpx;
        background-color: #1890ff;
        transition: all 0.3s ease;
    }

    /* 内容区域样式 */
    .content {
        flex: 1;
        padding: 20rpx 30rpx;
        overflow: auto;
        box-sizing: border-box;
    }

    /* 调查记录页样式 */
    .info-card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
        border: 1px solid #f0f0f0;
    }

    .info-item {
        margin-bottom: 20rpx;
    }

    .info-label {
        font-weight: 500;
        color: #333;
        margin-right: 10rpx;
    }

    .info-value {
        color: #666;
    }

    .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1a1a1a;
        margin: 36rpx 0 24rpx;
        padding-left: 16rpx;
        border-left: 6rpx solid #1890ff;
    }

    .attachment-list {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
    }

    .attachment-item {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        transition: background-color 0.2s ease;
    }

    .attachment-item:active {
        background-color: #f5f5f5;
    }

    .attachment-item:last-child {
        border-bottom: none;
    }

    .attachment-name {
        flex: 1;
        margin: 0 20rpx;
        color: #333;
    }

    .person-list {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
    }

    .person-item {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #eee;
    }

    .person-item:last-child {
        border-bottom: none;
    }

    /* 调查报告页样式 */
    .report-header {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
        border: 1px solid #f0f0f0;
    }

    .report-meta {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }

    .report-code {
        font-size: 24rpx;
        color: #999;
        margin-right: 20rpx;
    }

    .status-tag,
    .level-tag {
        flex-shrink: 0;
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        margin-right: 10rpx;
    }

    .status-tag.modify {
        background-color: #fff7e6;
        color: #fa8c16;
    }

    .status-tag.finalized {
        background-color: #f6ffed;
        color: #52c41a;
    }

    .status-tag.draft {
        background-color: #f0f0f0;
        color: #666;
    }

    .level-tag.low {
        background-color: #f6ffed;
        color: #52c41a;
    }

    .level-tag.middle {
        background-color: #fff7e6;
        color: #fa8c16;
    }

    .level-tag.high {
        background-color: #fff1f0;
        color: #f5222d;
    }

    .report-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 10rpx;
    }

    .report-source {
        font-size: 26rpx;
        color: #999;
    }

    .section-card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
        border: 1px solid #f0f0f0;
        transition: transform 0.2s ease;
    }

    .section-card:active {
        transform: scale(0.99);
    }

    .section-content {
        display: block;
        margin-top: 20rpx;
        color: #666;
        line-height: 1.6;
        white-space: pre-line;
    }

    .attachment-card {
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.2s ease;
    }

    .attachment-card:active {
        background-color: #f5f5f5;
    }

    .attachment-info {
        flex: 1;
        margin: 0 20rpx;
    }

    .attachment-desc {
        display: block;
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
    }

    .preview-btn {
        background-color: #1890ff;
        color: white;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .preview-btn:hover {
        background-color: #40a9ff;
    }
}
</style>
