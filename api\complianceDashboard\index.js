import http from '@/utils/request'
function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params,
        timeout: 10000 // 设置超时时间为100秒
    })
}
const baseUrl = `/services/whiskerguarddashboardservice/api/`

export default { 
    //  获取仪表板统计汇总信息
    queryComplianceDashboard() {
        return request(
            `${baseUrl}task/statistics/dashboard`,
            { }, 'get')
    },
    //获取风险分布统计
    queryRiskDistribution() {
        return request(
            `${baseUrl}task/statistics/risk/distribution`,
            { }, 'get')
    },
    // 获取任务状态趋势
    queryTaskStatusTrend() {
        return request(
            `${baseUrl}task/statistics/trend`,
            { }, 'get')
    },
}