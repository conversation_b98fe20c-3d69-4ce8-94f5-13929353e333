
<template>
    <view class="study-detail-container">
      <!-- TopBar -->
      <!-- <view class="top-bar">
        <button  class="back-btn" @click="goBack">
          <uni-icons type="arrowleft" size="20" color="#333333"></uni-icons>
        </button>
        <text class="title">课程详情</text>
        <view class="right-actions">
          <button  class="favorite-btn" @click="toggleFavorite">
            <uni-icons :type="isFavorite ? 'star-filled' : 'star'" size="20" color="#1A73E8"></uni-icons>
            <text class="favorite-text">收藏</text>
          </button>
          <button  class="more-btn">
            <uni-icons type="more" size="20" color="#333333"></uni-icons>
          </button>
        </view>
      </view> -->
  
      <!-- 主要内容区域 -->
      <scroll-view class="main-content" scroll-y>
        <!-- 头图区 -->
        <view class="cover-section">
          <view class="cover-card">
            <image class="cover-image" src="https://ai-public.mastergo.com/ai/img_res/e4dcc9dc72461b1b7a8e1047cdce46ab.jpg" mode="aspectFill"></image>
            <view class="cover-info">
              <text class="course-title">企业合规管理制度详解与实务操作</text>
              <view class="course-meta">
                <text class="meta-text">张明远 · 评分</text>
                <view class="rating">
                  <uni-icons type="star-filled" size="14" color="#FFB800"></uni-icons>
                  <uni-icons type="star-filled" size="14" color="#FFB800"></uni-icons>
                  <uni-icons type="star-filled" size="14" color="#FFB800"></uni-icons>
                  <uni-icons type="star-filled" size="14" color="#FFB800"></uni-icons>
                  <uni-icons type="star" size="14" color="#FFB800"></uni-icons>
                </view>
                <text class="meta-text">· 12K 学员</text>
              </view>
            </view>
          </view>
        </view>
  
        <!-- 操作区 -->
        <view class="action-buttons">
          <button  class="primary-btn" @click="continueLearning">继续学习</button>
          <button  class="secondary-btn">
            <uni-icons type="redo" size="16" color="#333333"></uni-icons>
            <text>分享</text>
          </button>
          <button  class="outline-btn">
            <uni-icons type="bookmark" size="16" color="#1A73E8"></uni-icons>
            <text>加入书签</text>
          </button>
        </view>
  
        <!-- 概览卡 -->
        <view class="overview-card">
          <text class="section-title">课程简介</text>
          <text class="course-description">本课程全面讲解企业合规管理体系的构建与实施，涵盖合规风险识别、制度设计、流程优化等核心内容，结合典型案例分析，帮助学员掌握合规管理的实务操作技能。</text>
          <view class="course-meta-info">
            <text class="meta-info">时长：3h 45m</text>
            <text class="meta-info">·</text>
            <text class="meta-info">难度：中级</text>
          </view>
          <view class="tags-container">
            <text class="tag">制度管理</text>
            <text class="tag">操作指南</text>
            <text class="tag">风险控制</text>
            <text class="tag">案例分析</text>
          </view>
        </view>
  
        <!-- 章节列表 -->
        <view class="chapter-list">
          <!-- 模块1 -->
          <view class="chapter-item">
            <view class="chapter-header" @click="toggleChapter(1)">
              <view class="header-left">
                <uni-icons :type="expandedChapter === 1 ? 'down' : 'right'" size="16" color="#808080"></uni-icons>
                <text class="chapter-title">模块 1：合规制度概述</text>
              </view>
              <text class="lesson-count">3课时</text>
            </view>
            
            <view v-if="expandedChapter === 1" class="lessons-container">
              <view class="lesson-item" @click="openLesson(1)">
                <view class="lesson-left">
                  <uni-icons type="videocam" size="20" color="#1A73E8"></uni-icons>
                  <view class="lesson-info">
                    <text class="lesson-name">1-1. 课程导入</text>
                    <view class="lesson-meta">
                      <text class="lesson-type">视频</text>
                      <text class="lesson-duration">5m</text>
                    </view>
                  </view>
                </view>
                <uni-icons type="arrowright" size="16" color="#C0C0C0"></uni-icons>
              </view>
              
              <view class="lesson-item" @click="openLesson(2)">
                <view class="lesson-left">
                  <uni-icons type="document" size="20" color="#34A853"></uni-icons>
                  <view class="lesson-info">
                    <text class="lesson-name">1-2. 制度要素解读</text>
                    <view class="lesson-meta">
                      <text class="lesson-type">文章</text>
                      <text class="lesson-duration">10m</text>
                    </view>
                  </view>
                </view>
                <uni-icons type="arrowright" size="16" color="#C0C0C0"></uni-icons>
              </view>
              
              <view class="lesson-item" @click="openLesson(3)">
                <view class="lesson-left">
                  <uni-icons type="help" size="20" color="#FBBC05"></uni-icons>
                  <view class="lesson-info">
                    <text class="lesson-name">1-3. 小测验</text>
                    <view class="lesson-meta">
                      <text class="lesson-type">互动</text>
                      <text class="lesson-duration">3m</text>
                    </view>
                  </view>
                </view>
                <uni-icons type="arrowright" size="16" color="#C0C0C0"></uni-icons>
              </view>
            </view>
          </view>
          
          <!-- 模块2 -->
          <view class="chapter-item">
            <view class="chapter-header" @click="toggleChapter(2)">
              <view class="header-left">
                <uni-icons :type="expandedChapter === 2 ? 'down' : 'right'" size="16" color="#808080"></uni-icons>
                <text class="chapter-title">模块 2：三张清单管理</text>
              </view>
              <text class="lesson-count">4课时</text>
            </view>
          </view>
          
          <!-- 模块3 -->
          <view class="chapter-item">
            <view class="chapter-header" @click="toggleChapter(3)">
              <view class="header-left">
                <uni-icons :type="expandedChapter === 3 ? 'down' : 'right'" size="16" color="#808080"></uni-icons>
                <text class="chapter-title">模块 3：合规流程优化</text>
              </view>
              <text class="lesson-count">5课时</text>
            </view>
          </view>
          
          <!-- 模块4 -->
          <view class="chapter-item">
            <view class="chapter-header" @click="toggleChapter(4)">
              <view class="header-left">
                <uni-icons :type="expandedChapter === 4 ? 'down' : 'right'" size="16" color="#808080"></uni-icons>
                <text class="chapter-title">模块 4：典型案例分析</text>
              </view>
              <text class="lesson-count">6课时</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <goNavitor url="/pages/component/study/courseDetail" />
    </view>
  </template>
  
  <script setup>
  import goNavitor from '../../../components/goNavitor.vue';
  import { ref } from 'vue';
  
  const isFavorite = ref(false);
  const expandedChapter = ref(1);
  
  const toggleFavorite = () => {
    isFavorite.value = !isFavorite.value;
  };
  
  const toggleChapter = (chapterId) => {
    expandedChapter.value = expandedChapter.value === chapterId ? 0 : chapterId;
  };
  
  const continueLearning = () => {
    uni.showToast({
      title: '继续学习',
      icon: 'none'
    });
  };
  
  const openLesson = (lessonId) => {
    uni.navigateTo({
      url: `/pages/lesson/lesson?id=${lessonId}`
    });
  };
  
  const switchTab = (tabName) => {
    uni.switchTab({
      url: `/pages/${tabName}/${tabName}`
    });
  };
  
  const goBack = () => {
    uni.navigateBack();
  };
  </script>
  
  <style lang="scss" scoped>
  .study-detail-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #F5F5F5;


      /* TopBar 样式 */
  
  /* 主要内容区域 */
  .main-content {
    flex: 1;
    margin-top: 88rpx;
    margin-bottom: 100rpx;
  }
  
  /* 头图区 */
  .cover-section {
    padding: 16rpx 32rpx;
  }
  
  .cover-card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .cover-image {
    width: 100%;
    height: 360rpx;
  }
  
  .cover-info {
    padding: 24rpx;
  }
  
  .course-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16rpx;
    display: block;
  }
  
  .course-meta {
    display: flex;
    align-items: center;
  }
  
  .meta-text {
    font-size: 24rpx;
    color: #666666;
  }
  
  .rating {
    display: flex;
    align-items: center;
    margin-left: 8rpx;
  }
  
  /* 操作按钮 */
  .action-buttons {
    display: flex;
    padding: 0 32rpx;
    margin-bottom: 16rpx;
  }
  
  .primary-btn {
    flex: 1;
    height: 80rpx;
    background-color: #1A73E8;
    color: #FFFFFF;
    font-size: 28rpx;
    font-weight: 500;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
  }
  
  .secondary-btn {
    flex: 1;
    height: 80rpx;
    background-color: #F1F3F4;
    color: #333333;
    font-size: 28rpx;
    font-weight: 500;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
  }
  
  .outline-btn {
    flex: 1;
    height: 80rpx;
    background-color: transparent;
    color: #1A73E8;
    font-size: 28rpx;
    font-weight: 500;
    border: 1px solid #1A73E8;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 概览卡 */
  .overview-card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    margin: 0 32rpx 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16rpx;
    display: block;
  }
  
  .course-description {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 24rpx;
    display: block;
    line-height: 1.5;
  }
  
  .course-meta-info {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
  }
  
  .meta-info {
    font-size: 24rpx;
    color: #666666;
    margin-right: 16rpx;
  }
  
  .tags-container {
    display: flex;
    flex-wrap: wrap;
  }
  
  .tag {
    font-size: 24rpx;
    color: #666666;
    background-color: #F1F3F4;
    border-radius: 32rpx;
    padding: 8rpx 24rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
  }
  
  /* 章节列表 */
  .chapter-list {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin: 0 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .chapter-item {
    border-bottom: 1px solid #F1F3F4;
  }
  
  .chapter-item:last-child {
    border-bottom: none;
  }
  
  .chapter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
  }
  
  .header-left {
    display: flex;
    align-items: center;
  }
  
  .chapter-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-left: 16rpx;
  }
  
  .lesson-count {
    font-size: 24rpx;
    color: #999999;
  }
  
  .lessons-container {
    padding-left: 80rpx;
    padding-right: 32rpx;
  }
  
  .lesson-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
  }
  
  .lesson-left {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  .lesson-info {
    margin-left: 16rpx;
  }
  
  .lesson-name {
    font-size: 28rpx;
    color: #333333;
    display: block;
  }
  
  .lesson-meta {
    display: flex;
    align-items: center;
    margin-top: 8rpx;
  }
  
  .lesson-type {
    font-size: 24rpx;
    color: #999999;
  }
  
  .lesson-duration {
    font-size: 24rpx;
    color: #CCCCCC;
    margin-left: 16rpx;
  }
  
  /* TabBar */
  .tab-bar {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100rpx;
    background-color: #FFFFFF;
    border-top: 1px solid #F1F3F4;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }
  
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
  }
  
  .tab-text {
    font-size: 20rpx;
    color: #999999;
    margin-top: 8rpx;
  }
  
  .tab-text.active {
    color: #1A73E8;
  }
  }
  

  </style>
  
  