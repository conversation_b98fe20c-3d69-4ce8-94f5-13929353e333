<template>
	<view class="liu-swiper-goods">
		<swiper class="swiper-item-select" :autoplay="autoplay" :interval="interval" circular="true" duration="150">
			<swiper-item class="swiper-item-select-card" v-for="(item,index) in newList" :key="index"
				@touchstart="touchstart" @touchend="touchend($event,item)">
				<view class="select-card">
					<image class="select-card-img" :src="item.img"></image>
					<view class="select-card-info">
						<view class="select-card-title">{{item.desc}}</view>
						<view class="select-card-money">
							¥<span>{{item.price}}</span>秒杀价
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
		<swiper class="swiper-item-main" :autoplay="autoplay" :interval="interval" circular="true" :current="current"
			previous-margin="8rpx" @change="handleChange">
			<swiper-item class="swiper-item-card" v-for="(item, index) in dataList" :key="index"
				@click.stop="clickItem(item)">
				<view class="defaut-card">
					<view class="card-state">秒杀价</view>
					<image class="card-img" :src="item.img"></image>
					<view class="card-name">¥<span>{{item.price}}</span></view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		props: {
			//数据源
			dataList: {
				type: Array,
				default: () => []
			},
			//是否自动切换
			autoplay: {
				type: Boolean,
				default: true
			},
			//自动切换时间间隔
			interval: {
				type: Number,
				default: 3000
			},
		},
		watch: {
			dataList: {
				deep: true,
				immediate: true,
				handler(newArr) {
					if (newArr.length) {
						this.$nextTick(() => {
							this.current = 1
						})
					}
				},
			}
		},
		data() {
			return {
				current: 0,
				startTime: 0,
				newList: []
			}
		},
		methods: {
			handleChange(e) {
				this.current = e.detail.current
				this.newList.shift()
				if (this.current == this.dataList.length - 1) {
					this.newList = this.newList.concat(this.dataList[this.dataList.length - 2])
				} else {
					if (this.current == 0) this.newList = this.newList.concat(this.dataList[this.dataList.length - 1])
					else this.newList = this.newList.concat(this.dataList[this.current - 1])
				}
			},
			touchstart(e) {
				this.startTime = new Date().getTime()
			},
			touchend(e, item) {
				const endTime = new Date().getTime()
				const timeDiff = endTime - this.startTime
				if (timeDiff > 100) {
					let pageX = e.changedTouches[0].pageX
					if (pageX < 150) this.current = this.current < this.dataList.length - 1 ? this.current + 1 : 0
					else this.current = this.current > 0 ? this.current - 1 : this.dataList.length - 1
				} else {
					this.$emit("clickItem", item)
				}
			},
			clickItem(item) {
				this.$emit("clickItem", item)
			}
		}
	};
</script>

<style scoped lang="scss">
	.liu-swiper-goods {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		position: relative;

		.swiper-item-select {
			width: 420rpx !important;
			height: 180rpx;
			overflow: hidden;
			box-sizing: border-box;
			display: flex;
			align-items: flex-start;
			justify-content: center;

			.swiper-item-select-card {
				width: 100%;
				height: 100%;

				.select-card {
					width: 100%;
					height: 100%;
					overflow: hidden;
					background-color: #FFFFFF;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					box-sizing: border-box;
					padding: 0 10rpx;
					box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(88, 102, 123, 0.1);
					border-radius: 8rpx;

					.select-card-img {
						width: 160rpx;
						height: 158rpx;
						min-width: 160rpx;
						border: solid #f0f0f0 1rpx;
						border-radius: 6rpx;
					}

					.select-card-info {
						width: calc(100% - 180rpx);
						margin-left: 20rpx;

						.select-card-title {
							text-align: justify;
							font-size: 30rpx;
							line-height: 40rpx;
							color: #333333;
							height: 80rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}

						.select-card-money {
							height: 54rpx;
							background: linear-gradient(180deg, #FF8329 0%, #FD2F27 100%);
							border-radius: 27rpx;
							font-size: 22rpx;
							color: #FFFFFF;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-top: 16rpx;

							span {
								font-size: 32rpx;
								font-weight: bold;
								color: #FFFFFF;
								margin: 0 6rpx 2rpx 4rpx;
							}
						}
					}
				}
			}
		}

		.swiper-item-main {
			width: calc(100% - 420rpx);
			height: 180rpx;

			.swiper-item-card {
				width: 180rpx !important;
				height: 100%;
				overflow: hidden;
				box-sizing: border-box;
				display: flex;
				align-items: flex-start;
				justify-content: center;
				box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(88, 102, 123, 0.1);

				.defaut-card {
					width: 100%;
					height: 100%;
					overflow: hidden;
					background-color: #FFFFFF;
					margin: 0 8rpx;
					position: relative;
					border-radius: 8rpx;
					text-align: center;

					.card-state {
						padding: 0rpx 12rpx;
						background: linear-gradient(180deg, #FF8329 0%, #FD2F27 100%);
						font-size: 22rpx;
						color: #FFFFFF;
						display: flex;
						align-items: center;
						justify-content: center;
						position: absolute;
						top: 6rpx;
						left: 6rpx;
						border-top-left-radius: 8rpx;
						border-bottom-right-radius: 8rpx;
						z-index: 99999 !important;
					}

					.card-img {
						width: 140rpx;
						height: 110rpx;
						border: solid #f0f0f0 1rpx;
						border-radius: 6rpx;
						margin-top: 15rpx;
					}

					.card-name {
						color: #FD2F27;
						font-size: 26rpx;
						line-height: 32rpx;

						span {
							font-size: 32rpx;
							margin-left: 2rpx;
						}
					}
				}
			}
		}
	}
</style>