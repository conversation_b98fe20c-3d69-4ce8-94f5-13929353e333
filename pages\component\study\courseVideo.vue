<template>
  <view class="container">
    <!-- 视频播放区域 -->
    <view class="video-container" v-if="isVideoContent">
      <x-video ref="videoPlayer" :playbackRates="[1]" :src="currentVideoSrc"
                :height="'400rpx'"
               @timeupdate="onVideoTimeUpdate" @ended="onVideoEnded" @error="onVideoError"
               @loadedmetadata="onVideoLoadedMetadata" @canplay="onVideoCanPlay" />
    </view>
    
    <!-- 非视频内容提示 -->
    <view class="non-video-container" v-else>
      <view class="content-placeholder">
        <uni-icons :type="currentLesson.chapterTitle ? getLessonIcon(currentLesson) : 'list'" size="48" color="#9CA3AF" />
        <text class="placeholder-text">{{ currentLesson.chapterTitle ? getContentTypeText(currentLesson.chapterType) + '内容' : '请选择课时' }}</text>
        
        <!-- 文档类型显示下载按钮 -->
        <view v-if="currentLesson.chapterTitle && isDocumentLesson(currentLesson)" class="download-section">
          <text class="download-desc">该内容为文档类型，请点击下载查看</text>
          <FileDownload :item="getDownloadItem(currentLesson)" />
        </view>
        
        <!-- 其他类型显示默认提示 -->
        <text v-else class="placeholder-desc">点击下方课时列表中的内容进行学习</text>
      </view>
    </view>

    <!-- 课程信息 -->
    <view class="course-info">
      <view class="course-header">
        <text class="course-title">{{ currentLesson.chapterTitle || (currentChapter.chapterTitle ? `${currentChapter.chapterTitle} - 请选择课时` : '请选择课时') }}</text>
        <view class="course-meta" v-if="currentLesson.chapterTitle">
          <text class="duration">{{ currentLesson.durationMinutes || 0 }}分钟</text>
          <text class="lesson-type">{{ getContentTypeText(currentLesson.chapterType) }}</text>
        </view>
      </view>
      <view class="progress-info" v-if="currentLesson.playbackPosition && isVideoContent">
        <text class="progress-text">上次观看到 {{ formatPlaybackPosition(currentLesson.playbackPosition) }}</text>
      </view>
    </view>

    <!-- 章节导航 - 横向滚动 -->
    <view class="chapters-section">
      <scroll-view class="chapters-nav" scroll-x show-scrollbar="false">
        <view class="chapters-wrapper">
          <view v-for="(chapter, index) in courseList" :key="chapter.id"
            :class="['chapter-tab', currentChapterIndex === index ? 'active' : '']"
            @click="switchToChapter(index)">
            <view class="chapter-content">
              <text class="chapter-number">第{{ index + 1 }}章</text>
              <text class="chapter-name">{{ chapter.chapterTitle }}</text>
              <view class="chapter-progress" v-if="getChapterProgress(chapter) > 0">
                <view class="progress-bar">
                  <view class="progress-fill" :style="{ width: getChapterProgress(chapter) + '%' }"></view>
                </view>
                <text class="progress-text">{{ getChapterProgress(chapter) }}%</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 课时列表 - 竖向滚动 -->
    <view class="lessons-section">
      <view class="section-header">
        <view class="section-title">
          <uni-icons type="videocam" size="16" color="#374151" />
          <text>{{ currentChapter.chapterTitle || '课时列表' }}</text>
        </view>
        <text class="lesson-count">共{{ currentChapterLessons.length }}个课时</text>
      </view>
      <scroll-view class="lessons-list" scroll-y>
        <view v-for="(lesson, index) in currentChapterLessons" :key="lesson.id"
          :class="['lesson-item', { active: currentLessonIndex === index }]"
          @click="handleLessonClick(lesson, index)">
          <view class="lesson-icon">
            <uni-icons :type="getLessonIcon(lesson)" :color="getLessonIconColor(lesson, index)" size="16" />
          </view>
          <view class="lesson-content">
            <text class="lesson-title">{{ lesson.chapterTitle }}</text>
            <view class="lesson-meta">
              <text class="lesson-duration">{{ lesson.durationMinutes || 0 }}分钟</text>
              <text class="lesson-type-tag">{{ getContentTypeText(lesson.chapterType) }}</text>
            </view>
            <view class="lesson-progress" v-if="lesson.playbackPosition > 0 && isVideoLesson(lesson)">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: getLessonProgress(lesson) + '%' }"></view>
              </view>
              <text class="progress-text">{{ formatPlaybackPosition(lesson.playbackPosition) }}</text>
            </view>
          </view>
          <view class="lesson-action">
            <!-- 视频类型显示播放状态 -->
            <view v-if="isVideoLesson(lesson)" class="lesson-status">
              <text :class="['status-text', getLessonStatusClass(lesson)]">{{ getLessonStatusText(lesson) }}</text>
              <uni-icons v-if="currentLessonIndex === index" type="sound" size="12" color="#10B981" />
            </view>
            <!-- 文档类型显示预览按钮 -->
            <view v-else class="lesson-preview">
              <FilePreviewUrl :url="lesson.contentUrl" :fileName="lesson.chapterTitle" />
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="currentChapterLessons.length === 0" class="empty-state">
          <uni-icons type="info" size="32" color="#9CA3AF" />
          <text class="empty-text">该章节暂无课时内容</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 加载提示 -->
    <uni-popup ref="loadingPopup" type="center">
      <view class="loading-container">
        <uni-icons type="spinner-cycle" size="24" color="#3B82F6" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import xVideo from '@/components/x-video-player/x-video-player.vue'
import FileDownload from '@/components/FileDownload/FileDownload.vue'
import FilePreviewUrl from '@/components/FilePreview/FilePreviewUrl.vue'
import uploads from '@/api/upload.js'
import course from '@/api/study/index.js'
import { useUserStore } from '@/store/pinia.js';

export default {
  components: {
    xVideo,
    FileDownload,
    FilePreviewUrl
  },
  data() {
    return {
      currentVideoSrc: '',
      currentChapterIndex: 0,
      currentLessonIndex: 0,
      currentChapter: {},
      currentLesson: {},
      courseList: [], // 章节列表
      currentChapterLessons: [], // 当前章节的课时列表
      courseObj: null,
      progressTimer: null,
      isVideoContent: false, // 当前是否为视频内容
      loadingText: '加载中...', // 加载提示文本
      lastProgressSaveTime: 0, // 上次保存进度的时间
      chapterSelectedLessons: {}, // 保存每个章节的选中课时状态 {chapterIndex: {lessonIndex: number, lesson: object}}
      positionSetAttempts: 0, // 设置播放位置的尝试次数
      maxPositionSetAttempts: 3 // 最大尝试次数
    }
  },
  onLoad(options) {
    console.log('onLoad', options)
    if (options.courseObj) {
      try {
        this.courseObj = JSON.parse(decodeURIComponent(options.courseObj))
        
        // 如果有传递的章节和课时索引，设置当前位置
        if (options.chapterIndex !== undefined) {
          this.currentChapterIndex = parseInt(options.chapterIndex)
        }
        if (options.lessonIndex !== undefined) {
          this.currentLessonIndex = parseInt(options.lessonIndex)
        }
        
        this.initCourseData()
      } catch (e) {
        console.error('解析课程对象失败:', e)
        this.showError('参数解析失败')
      }
    } else {
      this.showError('缺少必要参数')
    }
  },
  
  onUnload() {
    // 页面卸载时保存进度
    this.saveCurrentProgress()
    // 清除定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },
  methods: {
    // 获取课程章节和课时数据
    async getCourseContentByCourseId() {
      try {
        const userStore = useUserStore()
        const userId = userStore.userId
        const contentRes = await course.getCourseContentByCourseId(this.courseObj.courseId, { userId });
        this.courseList = contentRes || [];
        console.log('课程数据:', this.courseList);
        return true;
      } catch (error) {
        console.error('获取课程数据异常:', error);
        this.showError('网络异常，请检查网络连接');
        return false;
      }
    },

    // 初始化课程数据
    async initCourseData() {
      this.showLoading('加载课程数据...');
      
      const success = await this.getCourseContentByCourseId();
      console.log('1111111:', this.courseList);
      
      if (success && this.courseList.length > 0) {
        // 如果有传递的章节索引，使用传递的值，否则使用第一章
        const chapterIndex = Math.max(0, Math.min(this.currentChapterIndex, this.courseList.length - 1));
        this.currentChapter = this.courseList[chapterIndex];
        this.currentChapterIndex = chapterIndex;
        
        // 更新当前章节的课时列表
        this.updateCurrentChapterLessons();
        
        // 如果有传递的课时索引，使用传递的值
        if (this.currentLessonIndex >= 0 && this.currentChapterLessons[this.currentLessonIndex]) {
          this.currentLesson = this.currentChapterLessons[this.currentLessonIndex];
          await this.loadCurrentContent();
        } else {
          // 查找第一个课时
          const firstLesson = this.findFirstLesson();
          if (firstLesson) {
            this.currentLesson = firstLesson.lesson;
            this.currentLessonIndex = firstLesson.index;
            await this.loadCurrentContent();
          }
        }
        
        // 启动进度保存定时器
        this.startProgressTimer();
      } else if (success) {
        this.showError('课程暂无内容');
      }
      
      this.hideLoading();
    },

    // 查找第一个课时（优先视频）
    findFirstLesson() {
      if (this.currentChapter.lessons && this.currentChapter.lessons.length > 0) {
        // 优先查找视频课时
        for (let i = 0; i < this.currentChapter.lessons.length; i++) {
          const lesson = this.currentChapter.lessons[i];
          if (this.isVideoLesson(lesson)) {
            return { lesson, index: i };
          }
        }
        // 如果没有视频课时，返回第一个课时
        return { lesson: this.currentChapter.lessons[0], index: 0 };
      }
      return null;
    },
    
    // 判断是否为视频课时
    isVideoLesson(lesson) {
      return lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video' || true;
    },

    // 判断是否为文档课时
    isDocumentLesson(lesson) {
      return lesson.chapterType === 'DOCUMENT' || lesson.chapterType === 'document' || lesson.chapterType === 'FILE' || lesson.chapterType === 'file';
    },

    // 获取下载组件需要的数据格式
    getDownloadItem(lesson) {
      return {
        filePath: lesson.contentUrl,
        fileName: lesson.chapterTitle || '未命名文件',
        fileType: this.getFileExtension(lesson.chapterType)
      };
    },

    // 根据课时类型获取文件扩展名
    getFileExtension(chapterType) {
      const typeMap = {
        'DOCUMENT': 'pdf',
        'document': 'pdf',
        'FILE': '',
        'file': ''
      };
      return typeMap[chapterType] || '';
    },

    // 更新当前章节的课时列表
    updateCurrentChapterLessons() {
      this.currentChapterLessons = this.currentChapter.lessons || [];
    },

    // 加载当前内容
    async loadCurrentContent() {
      // 暂时去掉加载视频内容， 因为this.currentLesson
      // if (!this.currentLesson || !this.currentLesson.contentUrl) {
      //   this.isVideoContent = false;
      //   return;
      // }
      
      this.showLoading('加载内容...');
      
      try {
        if (this.isVideoLesson(this.currentLesson) || true) {
          // 加载视频
          console.log('加载视频...');
          await this.loadVideo();
        } else {
          // 非视频内容
          this.isVideoContent = false;
        }
      } catch (error) {
        console.error('加载内容失败:', error);
        this.showError('加载内容失败');
      } finally {
        this.hideLoading();
      }
    },
    
    // 加载视频
    async loadVideo() {
      try {
        const fileRes = await uploads.getFileUrl(this.currentLesson.contentUrl);
        console.log('视频链接:', fileRes);
        if (fileRes) {
          this.currentVideoSrc = fileRes;
          this.isVideoContent = true;

          // 等待视频组件加载完成后设置播放位置
          this.$nextTick(() => {
            this.setVideoPosition();
          });
        } else {
          throw new Error('获取视频链接失败');
        }
      } catch (error) {
        console.error('加载视频失败:', error);
        this.showError('视频加载失败');
        this.isVideoContent = false;
      }
    },

    // 设置视频播放位置
    setVideoPosition() {
      if (!this.$refs.videoPlayer || !this.currentLesson.playbackPosition) {
        return;
      }

      // 转换播放位置为秒数
      const positionInSeconds = this.parsePlaybackPosition(this.currentLesson.playbackPosition);

      if (positionInSeconds > 0) {
        console.log('设置播放位置:', positionInSeconds, '秒');
        this.attemptSetPosition(positionInSeconds, 0);
      }
    },

    // 尝试设置播放位置（带重试机制）
    attemptSetPosition(positionInSeconds, attemptCount) {
      if (attemptCount >= this.maxPositionSetAttempts) {
        console.log('设置播放位置达到最大尝试次数，停止尝试');
        return;
      }

      const delay = attemptCount === 0 ? 500 : 1000 * attemptCount;

      setTimeout(() => {
        if (this.$refs.videoPlayer) {
          try {
            this.$refs.videoPlayer.seek(positionInSeconds);
            console.log(`播放位置设置成功 (尝试${attemptCount + 1}次):`, positionInSeconds);

            // 验证设置是否成功
            setTimeout(() => {
              if (this.$refs.videoPlayer) {
                const currentTime = this.$refs.videoPlayer.getCurrentTime() || 0;
                const timeDiff = Math.abs(currentTime - positionInSeconds);

                if (timeDiff > 5) { // 如果时间差超过5秒，认为设置失败
                  console.log('播放位置验证失败，重新尝试设置');
                  this.attemptSetPosition(positionInSeconds, attemptCount + 1);
                } else {
                  console.log('播放位置验证成功:', currentTime);
                }
              }
            }, 1000);

          } catch (error) {
            console.error(`设置播放位置失败 (尝试${attemptCount + 1}次):`, error);
            this.attemptSetPosition(positionInSeconds, attemptCount + 1);
          }
        } else {
          console.log('视频播放器不存在，重新尝试');
          this.attemptSetPosition(positionInSeconds, attemptCount + 1);
        }
      }, delay);
    },

    // 解析播放位置（支持多种格式）
    parsePlaybackPosition(position) {
      if (!position) return 0;

      // 如果已经是数字，直接返回
      if (typeof position === 'number') {
        return position;
      }

      // 如果是字符串，尝试解析
      if (typeof position === 'string') {
        // 尝试直接转换为数字
        const numValue = parseFloat(position);
        if (!isNaN(numValue)) {
          return numValue;
        }

        // 尝试解析时间格式 "mm:ss" 或 "hh:mm:ss"
        const timeMatch = position.match(/^(\d{1,2}):(\d{2})(?::(\d{2}))?$/);
        if (timeMatch) {
          const hours = timeMatch[3] ? parseInt(timeMatch[1]) : 0;
          const minutes = timeMatch[3] ? parseInt(timeMatch[2]) : parseInt(timeMatch[1]);
          const seconds = timeMatch[3] ? parseInt(timeMatch[3]) : parseInt(timeMatch[2]);

          return hours * 3600 + minutes * 60 + seconds;
        }
      }

      return 0;
    },

    // 切换到指定章节
    async switchToChapter(chapterIndex) {
      console.log('切换章节:', chapterIndex);
      if (chapterIndex >= 0 && chapterIndex < this.courseList.length && chapterIndex !== this.currentChapterIndex) {
        // 保存当前进度
        await this.saveCurrentProgress();
        
        // 保存当前章节的选中状态（不清除其他章节的状态）
        if (this.currentLessonIndex >= 0 && this.currentLesson.id) {
          this.chapterSelectedLessons[this.currentChapterIndex] = {
            lessonIndex: this.currentLessonIndex,
            lesson: { ...this.currentLesson }
          };
        }
        
        const chapter = this.courseList[chapterIndex];
        this.currentChapter = chapter;
        this.currentChapterIndex = chapterIndex;
        
        // 更新当前章节的课时列表
        this.updateCurrentChapterLessons();
        
        // 恢复该章节之前的选中状态
        const savedSelection = this.chapterSelectedLessons[chapterIndex];
        if (savedSelection && savedSelection.lessonIndex >= 0 && this.currentChapterLessons[savedSelection.lessonIndex]) {
          // 恢复之前选中的课时
          this.currentLessonIndex = savedSelection.lessonIndex;
          this.currentLesson = this.currentChapterLessons[savedSelection.lessonIndex];
          await this.loadCurrentContent();
        } else {
          // 该章节没有之前的选中状态，重置
          this.currentLesson = {};
          this.currentLessonIndex = -1;
          this.isVideoContent = false;
          this.currentVideoSrc = '';
        }
      }
    },

    // 处理课时点击事件
    handleLessonClick(lesson, index) {
      if (this.isVideoLesson(lesson)) {
        // 视频类型：切换到该课时进行播放
        this.switchToLesson(index)
      } else {
        // 文档类型：不做任何操作，预览功能由FilePreviewUrl组件处理
        console.log('文档类型课时，使用预览功能')
      }
    },

    // 切换到指定课时
    async switchToLesson(lessonIndex) {
      console.log('切换课时:', lessonIndex);
      if (lessonIndex >= 0 && lessonIndex < this.currentChapterLessons.length && lessonIndex !== this.currentLessonIndex) {
        // 保存当前进度
        await this.saveCurrentProgress();
        
        const lesson = this.currentChapterLessons[lessonIndex];
        this.currentLesson = lesson;
        this.currentLessonIndex = lessonIndex;
        
        // 只保存当前章节的选中状态（不清除其他章节的状态）
        this.chapterSelectedLessons[this.currentChapterIndex] = {
          lessonIndex: this.currentLessonIndex,
          lesson: { ...this.currentLesson }
        };
        
        if (this.isVideoLesson(lesson)) {
          await this.loadCurrentContent();
        } else {
          // 如果是文档或文件，设置为非视频内容，在界面上显示下载选项
          this.isVideoContent = false;
          this.currentVideoSrc = '';
        }
      }
    },

    // 获取当前章节
    getCurrentChapter() {
      if (!this.courseList || this.courseList.length === 0) {
        return null;
      }
      return this.courseList[this.currentChapterIndex] || null;
    },

    // 获取当前章节的课时列表
    getCurrentLessons() {
      const currentChapter = this.getCurrentChapter();
      return currentChapter ? (currentChapter.lessons || []) : [];
    },

    // 获取章节学习进度
    getChapterProgress(chapter) {
      if (!chapter || !chapter.lessons || chapter.lessons.length === 0) {
        return { completed: 0, total: 0, percentage: 0 };
      }
      
      const total = chapter.lessons.length;
      const completed = chapter.lessons.filter(lesson => lesson.isCompleted).length;
      const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
      
      return { completed, total, percentage };
    },

    // 格式化时长
    formatDuration(duration) {
      if (!duration) return '00:00';
      const minutes = Math.floor(duration / 60);
      const seconds = duration % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },

    // 格式化播放位置显示
    formatPlaybackPosition(position) {
      const seconds = this.parsePlaybackPosition(position);
      if (seconds <= 0) return '00:00';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 获取课时状态文本
    getLessonStatusText(lesson) {
      if (lesson.isCompleted) {
        return '已完成';
      } else if (lesson.progress > 0) {
        return '学习中';
      } else {
        return '未开始';
      }
    },

    // 获取课时状态样式类
    getLessonStatusClass(lesson) {
      if (lesson.isCompleted) {
        return 'status-completed';
      } else if (lesson.progress > 0) {
        return 'status-progress';
      } else {
        return 'status-pending';
      }
    },

    // 获取课时学习进度
    getLessonProgress(lesson) {
      if (!lesson.playbackPosition || !lesson.durationMinutes) return 0;

      const playbackSeconds = this.parsePlaybackPosition(lesson.playbackPosition);
      const durationSeconds = lesson.durationMinutes * 60;

      if (playbackSeconds <= 0 || durationSeconds <= 0) return 0;

      return Math.min(Math.round((playbackSeconds / durationSeconds) * 100), 100);
    },

    // 获取课时图标
    getLessonIcon(lesson) {
      if (lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video') {
        return 'play-filled';
      } else if (lesson.chapterType === 'DOCUMENT' || lesson.chapterType === 'document') {
        return 'paperplane-filled';
      } else if (lesson.chapterType === 'FILE' || lesson.chapterType === 'file') {
        return 'download-filled';
      }
      return 'play-filled';
    },

    // 获取课时图标颜色
    getLessonIconColor(lesson, index) {
      if (this.currentLessonIndex === index) {
        return '#FFFFFF';
      } else if (lesson.completionStatus === 'COMPLETED') {
        return '#10B981';
      } else if (lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video') {
        return '#3B82F6';
      }
      return '#6B7280';
    },

    // 获取课时状态文本
    getLessonStatusText(lesson) {
      if (lesson.completionStatus === 'COMPLETED') {
        return '已完成';
      } else if (lesson.playbackPosition > 0) {
        return '进行中';
      }
      return '未开始';
    },

    // 获取课时状态样式类
    getLessonStatusClass(lesson) {
      if (lesson.completionStatus === 'COMPLETED') {
        return 'status-completed';
      } else if (lesson.playbackPosition > 0) {
        return 'status-progress';
      }
      return 'status-pending';
    },

    // 下载文件
    async downloadFile(lesson) {
      if (!lesson.contentUrl) {
        this.showError('文件不存在');
        return;
      }
      
      this.showLoading('准备下载...');
      
      try {
        const fileRes = await uploads.getFileUrl(lesson.contentUrl);
        if (fileRes.code === 200 && fileRes.data) {
          this.hideLoading();
          
          // 在小程序中使用下载API
          uni.downloadFile({
            url: fileRes.data,
            success: (res) => {
              if (res.statusCode === 200) {
                uni.showToast({
                  title: '下载成功',
                  icon: 'success'
                });
                
                // 尝试打开文件
                uni.openDocument({
                  filePath: res.tempFilePath,
                  success: () => {
                    console.log('打开文档成功');
                  },
                  fail: (err) => {
                    console.log('打开文档失败', err);
                    uni.showToast({
                      title: '文件已下载，请在文件管理器中查看',
                      icon: 'none',
                      duration: 3000
                    });
                  }
                });
              } else {
                this.showError('下载失败');
              }
            },
            fail: (err) => {
              console.error('下载失败:', err);
              this.showError('下载失败');
            }
          });
        } else {
          this.hideLoading();
          this.showError('获取文件链接失败');
        }
      } catch (error) {
        console.error('下载文件失败:', error);
        this.hideLoading();
        this.showError('下载失败');
      }
    },

    // 保存当前播放进度
    async saveCurrentProgress() {
      if (this.currentLesson && this.isVideoContent && this.$refs.videoPlayer && this.isVideoLesson(this.currentLesson)) {
        try {
          const currentTime = this.$refs.videoPlayer.getCurrentTime() || 0;
          console.log('当前播放时间:', currentTime, '课时ID:', this.currentLesson.id);

          // 确保currentTime是有效的数字
          if (currentTime > 0 && !isNaN(currentTime)) {
             const userStore = useUserStore()
             const userId = userStore.userId

             // 确保传递的是数字格式的秒数
             const playbackPositionSeconds = Math.floor(currentTime);

             const params = {
              userId: userId,
              courseId: +this.courseObj.courseId,
              chapterId: this.currentLesson.id,
              playbackPosition: playbackPositionSeconds
             }

            console.log('保存进度参数:', params);
            await course.updateProgress(params);

            // 更新本地进度（保存为秒数）
            this.currentLesson.playbackPosition = playbackPositionSeconds;
            console.log('进度保存成功:', playbackPositionSeconds, '秒');
          } else {
            console.log('当前播放时间无效，跳过保存:', currentTime);
          }
        } catch (error) {
          console.error('保存进度失败:', error);
        }
      } else {
        console.log('保存进度条件不满足:', {
          hasCurrentLesson: !!this.currentLesson,
          isVideoContent: this.isVideoContent,
          hasVideoPlayer: !!this.$refs.videoPlayer,
          isVideoLesson: this.currentLesson ? this.isVideoLesson(this.currentLesson) : false
        });
      }
    },
    
    // 启动进度保存定时器
    startProgressTimer() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
      }
      
      // 每30秒保存一次进度
      this.progressTimer = setInterval(() => {
        this.saveCurrentProgress();
      }, 30000);
    },
    
    // 视频时间更新事件
    onVideoTimeUpdate(e) {
      const currentTime = e.detail.currentTime;
      const now = Date.now();
      
      console.log('视频时间更新:', currentTime);
      
      // 每10秒保存一次进度，避免频繁请求
      if (now - this.lastProgressSaveTime > 10000) {
        this.lastProgressSaveTime = now;
        console.log('触发定时保存进度');
        this.saveCurrentProgress();
      }
    },
    
    // 视频播放结束事件
    onVideoEnded() {
      // 标记课时为已完成
      this.currentLesson.completionStatus = 'COMPLETED';
      this.saveCurrentProgress();

      // 显示完成提示和操作选择
      this.showVideoCompletionDialog();
    },

    // 显示视频完成后的操作选择对话框
    showVideoCompletionDialog() {
      const hasNextLesson = this.hasNextLesson();

      if (hasNextLesson) {
        // 有下一课时，提供选择
        uni.showModal({
          title: '课时学习完成',
          content: '恭喜您完成了本课时的学习！您可以选择重新观看或继续下一课时。',
          confirmText: '下一课时',
          cancelText: '重新观看',
          success: (res) => {
            if (res.confirm) {
              // 用户选择下一课时
              this.playNextLesson();
            } else {
              // 用户选择重新观看
              this.replayCurrentVideo();
            }
          }
        });
      } else {
        // 没有下一课时，只提供重新观看选项
        uni.showModal({
          title: '课程学习完成',
          content: '恭喜您完成了整个课程的学习！您可以选择重新观看本课时。',
          confirmText: '重新观看',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重新观看
              this.replayCurrentVideo();
            }
            // 如果用户选择返回，不做任何操作
          }
        });
      }
    },

    // 重新播放当前视频
    replayCurrentVideo() {
      if (this.$refs.videoPlayer && this.isVideoContent) {
        try {
          // 重置播放位置到开始
          this.$refs.videoPlayer.seek(0);

          // 重置播放进度
          this.currentLesson.playbackPosition = 0;

          // 可选：自动开始播放
          // this.$refs.videoPlayer.play();

          uni.showToast({
            title: '已重置到开始位置',
            icon: 'success'
          });
        } catch (error) {
          console.error('重新播放失败:', error);
          uni.showToast({
            title: '重新播放失败，请手动操作',
            icon: 'none'
          });
        }
      }
    },

    // 检查是否有下一课时
    hasNextLesson() {
      const currentChapterLessons = this.currentChapterLessons;
      const currentIndex = this.currentLessonIndex;

      // 检查当前章节是否还有下一课时
      if (currentIndex < currentChapterLessons.length - 1) {
        return true;
      }

      // 检查是否还有下一章节
      const currentChapterIndex = this.currentChapterIndex;
      if (currentChapterIndex < this.courseData.chapters.length - 1) {
        return true;
      }

      return false;
    },
    
    // 视频错误事件
    onVideoError(e) {
      console.error('视频播放错误:', e);
      this.showError('视频播放失败，请检查网络连接');
    },

    // 视频元数据加载完成事件
    onVideoLoadedMetadata(e) {
      console.log('视频元数据加载完成:', e);
      // 在元数据加载完成后设置播放位置
      this.setVideoPosition();
    },

    // 视频可以播放事件
    onVideoCanPlay(e) {
      console.log('视频可以播放:', e);
      // 确保在视频可以播放时设置播放位置
      this.setVideoPosition();
    },
    
    // 播放下一个课时
    playNextLesson() {
      const nextIndex = this.currentLessonIndex + 1;
      if (nextIndex < this.currentChapterLessons.length) {
        this.switchToLesson(nextIndex);
      } else {
        // 当前章节已完成，切换到下一章节
        const nextChapterIndex = this.currentChapterIndex + 1;
        if (nextChapterIndex < this.courseList.length) {
          this.switchToChapter(nextChapterIndex);
        } else {
          uni.showToast({
            title: '课程学习完成',
            icon: 'success'
          });
        }
      }
    },
    
    // 显示加载提示
    showLoading(text = '加载中...') {
      this.loadingText = text;
      if (this.$refs.loadingPopup) {
        this.$refs.loadingPopup.open();
      }
    },
    
    // 隐藏加载提示
    hideLoading() {
      if (this.$refs.loadingPopup) {
        this.$refs.loadingPopup.close();
      }
    },
    
    // 显示错误提示
    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
    },

    // 获取内容类型文本
    getContentTypeText(type) {
      const typeMap = {
        'VIDEO': '视频',
        'video': '视频',
        'DOCUMENT': '文档',
        'document': '文档',
        'FILE': '附件',
        'file': '附件'
      };
      return typeMap[type] || '内容';
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
}

.video-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  background-color: #000;
}

.non-video-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.placeholder-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #6b7280;
}

.placeholder-desc {
  font-size: 28rpx;
  color: #9ca3af;
  text-align: center;
}

.download-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  margin-top: 24rpx;
}

.download-desc {
  font-size: 28rpx;
  color: #6b7280;
  text-align: center;
}

.course-info {
  padding: 32rpx;
  background-color: #fff;
  border-bottom: 2rpx solid #e5e7eb;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.course-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  flex: 1;
}

.course-meta {
  display: flex;
  gap: 24rpx;
  align-items: center;
}

.duration {
  font-size: 28rpx;
  color: #6b7280;
}

.lesson-type {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  background-color: #dbeafe;
  color: #1d4ed8;
  border-radius: 24rpx;
}

.progress-info {
  margin-top: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #059669;
}

/* 章节导航 - 横向滚动 */
.chapters-section {
  background-color: #fff;
  border-bottom: 2rpx solid #e5e7eb;
  padding: 24rpx 0;
}

.chapters-nav {
  width: 100%;
  white-space: nowrap;
}

.chapters-wrapper {
  display: flex;
  padding: 0 32rpx;
  gap: 40rpx;
}

.chapter-tab {
  flex-shrink: 0;
  min-width: 200rpx;
  padding: 24rpx 32rpx;
  background-color: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.chapter-tab.active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
}

.chapter-tab.active .chapter-number,
.chapter-tab.active .chapter-name,
.chapter-tab.active .progress-text {
  color: #fff;
}

.chapter-tab:hover:not(.active) {
  border-color: #3b82f6;
  transform: translateY(-2rpx);
}

.chapter-content {
  text-align: center;
}

.chapter-number {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.chapter-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chapter-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.chapter-tab:not(.active) .progress-bar {
  background-color: #e5e7eb;
}

.progress-fill {
  height: 100%;
  background-color: #10b981;
  transition: width 0.3s;
}

.chapter-tab.active .progress-fill {
  background-color: #fff;
}

.chapter-progress .progress-text {
  font-size: 20rpx;
  color: #6b7280;
  font-weight: 500;
}

/* 课时列表 - 竖向滚动 */
.lessons-section {
  flex: 1;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
}

.lesson-count {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: normal;
}

.lessons-list {
  flex: 1;
  overflow-y: auto;
}

.lesson-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.lesson-item:hover {
  background-color: #f9fafb;
}

.lesson-item.active {
  background-color: #eff6ff;
  border-left: 8rpx solid #3b82f6;
}

.lesson-icon {
  margin-right: 24rpx;
}

.lesson-content {
  flex: 1;
  min-width: 0;
}

.lesson-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lesson-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
  font-size: 28rpx;
  color: #6b7280;
}

.lesson-duration {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.lesson-type-tag {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  background-color: #f3f4f6;
  color: #6b7280;
  border-radius: 16rpx;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

.lesson-progress .progress-bar {
  width: 120rpx;
  height: 8rpx;
  background-color: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.lesson-progress .progress-fill {
  height: 100%;
  background-color: #10b981;
  transition: width 0.3s;
}

.lesson-progress .progress-text {
  font-size: 24rpx;
  color: #6b7280;
}

.lesson-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.lesson-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.lesson-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-text {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-progress {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-pending {
  background-color: #f3f4f6;
  color: #6b7280;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  gap: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  padding: 48rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
}
</style>