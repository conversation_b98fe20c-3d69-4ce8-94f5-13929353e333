import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
const baseUrl = '/services/whiskerguardgeneralservice/api/'
export default { 
    // 构建静默授权
    getAuthorize() {
        return request(`${baseUrl}wechat/binding/oauth/build/userinfo?state=1`, {}, 'GET')
    },
    
    // 处理授权回调，用code换取openId
    handleAuthCallback(code, state) {
        return request(`${baseUrl}wechat/binding/oauth/callback`, {
            code: code,
            state: state
        }, 'POST')
    },
}