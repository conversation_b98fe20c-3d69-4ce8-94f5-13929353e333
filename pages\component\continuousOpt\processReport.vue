
<template>
    <view class="page-container">
      <!-- 主要内容区域 -->
      <view class="main-content">
        <!-- 风险等级选择区块 -->
        <view class="section">
          <text class="section-title">风险等级选择</text>
          <view class="risk-levels">
            <view 
              v-for="(level, index) in riskLevels" 
              :key="index"
              @click="selectRiskLevel(index)"
              :class="[
                'risk-level-item',
                selectedRiskLevel === index ? 'selected' : '',
                `risk-level-${index}`
              ]"
            >
              <uni-icons 
                :type="level.icon" 
                :color="selectedRiskLevel === index ? level.color : '#999'" 
                size="20"
              />
              <text class="risk-level-label">{{ level.label }}</text>
            </view>
          </view>
        </view>
  
        <!-- 处理动作卡片组 -->
        <view class="section">
          <view 
            v-for="(action, index) in actions" 
            :key="index"
            class="action-card"
          >
            <view class="action-card-content">
              <view class="action-card-header">
                <uni-icons 
                  :type="action.icon" 
                  :color="action.color" 
                  size="24"
                />
                <view class="action-card-text">
                  <text class="action-title">{{ action.title }}</text>
                  <text class="action-description">{{ action.description }}</text>
                </view>
              </view>
              <button 
                @click="showConfirmDialog(index)"
                :class="`action-button action-button-${action.color}`"
                :disabled="!action.show"
              >
                {{ action.buttonText }}
              </button>
            </view>
          </view>
        </view>
  

      </view>
  
      <!-- 底部操作栏 -->
      <view class="footer">
        <button 
          @click="submitReport"
          :disabled="selectedRiskLevel === null"
          :class="['submit-button', selectedRiskLevel === null ? 'disabled' : '']"
        >
          确认处理
        </button>
      </view>
  
      <!-- 确认弹窗 -->
      <uni-popup ref="popup" type="dialog">
        <uni-popup-dialog 
          :title="`确认${actions[currentActionIndex]?.title}`"
          :content="'是否新增此任务？'"
          :confirmText="'确认'"
          :cancelText="'取消'"
          @confirm="confirmAction"
          @close="dialogVisible = false"
        />
      </uni-popup>
    </view>
  </template>
  
  <script setup>
import { ref } from 'vue';
import { onLoad, onReady, onShow } from '@dcloudio/uni-app';
import https from '@/api/violation/report.js';
import taskApi from '@/api/violation/task.js';

// 页面参数
const reportId = ref('');
const violationDealDTO = ref(null);

const problemInvestigateId = ref(null); //问题调查id
const responsibilityInvestigateId = ref(null); // 责任追究处理id
const continuousImprovementId = ref(null); //持续改进优化id

// 风险等级数据
const riskLevels = [
  { label: '低', icon: 'checkmarkempty', color: '#07c160' },
  { label: '中', icon: 'info', color: '#f0ad4e' },
  { label: '高', icon: 'closeempty', color: '#dd524d' }
];

const selectedRiskLevel = ref(null);

// 处理动作数据
const actions = ref([
  {
    title: '违规问题调查任务',
    description: '用于深入核实举报内容，发起调查处理流程',
    buttonText: '新增调查任务',
    icon: 'search',
    color: 'blue',
    show: true
  },
  // {
  //   title: '责任追究处理任务',
  //   description: '若确定违规属实，可启动责任追究流程',
  //   buttonText: '新增责任处理',
  //   icon: 'paperclip',
  //   color: 'orange',
  //   show: true
  // },
  // {
  //   title: '持续改进优化任务',
  //   description: '对流程或制度存在问题的，发起持续优化建议任务',
  //   buttonText: '新增改进任务',
  //   icon: 'gear',
  //   color: 'green',
  //   show: true
  // }
]);



// 弹窗相关
const dialogVisible = ref(false);
const currentActionIndex = ref(0);
const popup = ref(null);

// 页面加载时接收参数
onLoad((options) => {
  if (options.id) {
    reportId.value = options.id;
  }
});

// 页面显示时获取数据
onShow(async () => {
  if (reportId.value) {
    await loadReportDetail();
  }
  // 重新初始化页面数据以更新按钮状态
  initializePageData();
});

// 获取举报详情数据
const loadReportDetail = async () => {
  if (!reportId.value) return;
  
  uni.showLoading({ title: '加载中...' });
  
  try {
    const res = await https.getDetail(reportId.value);
    if (res) {
      violationDealDTO.value = res.violationDealDTO;
      // 根据violationDealDTO数据进行回填
      initializePageData();
    }
  } catch (error) {
    console.error('加载举报详情失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    });
  } finally {
    uni.hideLoading();
  }
}
// 根据violationDealDTO初始化页面数据
const initializePageData = () => {
  const dealData = violationDealDTO.value || {};
  
  // 根据风险等级设置选中状态
  if (dealData.level) {
    switch (dealData.level) {
      case 'LOW':
        selectedRiskLevel.value = 0;
        break;
      case 'MIDDLE':
        selectedRiskLevel.value = 1;
        break;
      case 'HIGH':
        selectedRiskLevel.value = 2;
        break;
    }
  }
  
  // 根据已有的任务ID控制按钮显示
  // 如果problemInvestigateId存在，隐藏新增调查任务按钮
  if (dealData.problemInvestigateId || problemInvestigateId.value) {
    actions.value[0].show = false;
    actions.value[0].buttonText = '已创建调查任务';
  } else {
    actions.value[0].show = true;
    actions.value[0].buttonText = '新增调查任务';
  }
  
  // 如果responsibilityInvestigateId存在，隐藏新增责任处理按钮
  // if (dealData.responsibilityInvestigateId || responsibilityInvestigateId.value) {
  //   actions.value[1].show = false;
  //   actions.value[1].buttonText = '已创建责任处理';
  // } else {
  //   actions.value[1].show = true;
  //   actions.value[1].buttonText = '新增责任处理';
  // }
  
  // // 如果continuousImprovementId存在，隐藏新增改进任务按钮
  // if (dealData.continuousImprovementId || continuousImprovementId.value) {
  //   actions.value[2].show = false;
  //   actions.value[2].buttonText = '已创建改进任务';
  // } else {
  //   actions.value[2].show = true;
  //   actions.value[2].buttonText = '新增改进任务';
  // }
};

// 选择风险等级
const selectRiskLevel = (index) => {
  selectedRiskLevel.value = index;
};

// 显示确认弹窗
const showConfirmDialog = (index) => {
  currentActionIndex.value = index;
  popup.value?.open();
};

// 确认处理动作
const confirmAction = () => {
  dialogVisible.value = false;
  popup.value?.close();
  
  // 根据当前选择的动作类型跳转到对应页面
  const actionIndex = currentActionIndex.value;
  
  if (actionIndex === 0) {
    // 新增调查任务
    uni.navigateTo({
      url: `/pages/component/continuousOpt/surveyTask/addSurveyTask?id=${reportId}`
    });
  } else if (actionIndex === 1) {
    // 新增责任处理
    uni.navigateTo({
      url: `/pages/component/continuousOpt/dutyTask/addDuty?id=${reportId}`
    });
  } else if (actionIndex === 2) {
    // 新增改进任务
    uni.navigateTo({
      url: `/pages/component/continuousOpt/improvementTask/addImprovement?id=${reportId}`
    });
  }
};



// 提交处理
const submitReport = async () => {
  if(violationDealDTO.value){
       uni.showToast({
      title: '已创建调查任务，不能重复创建',
      icon: 'none',
    });
    return
  }
  if (selectedRiskLevel.value === null) return;
  
  // 构建提交参数
  const levelMap = ['LOW', 'MIDDLE', 'HIGH'];
  const params = {
    detailId: parseInt(reportId.value),
    level: levelMap[selectedRiskLevel.value],
    problemInvestigateId: problemInvestigateId.value || ''
  };
  
  uni.showLoading({ title: '提交中...' });
  
  try {
    const result = await taskApi.createViolation(params);
    uni.hideLoading();
    
    uni.showToast({
      title: '提交成功',
      icon: 'success'
    });
    
    // 返回上一级页面
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    
  } catch (error) {
    uni.hideLoading();
    console.error('提交失败:', error);
    uni.showToast({
      title: '提交失败',
      icon: 'error'
    });
  }
};

// 暴露变量供其他页面访问
defineExpose({
  problemInvestigateId,
  responsibilityInvestigateId,
  continuousImprovementId
});
</script>
  
  <style>
  page {
    height: 100%;
  }
  
  .page-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f7f7f7;
  }
  
  .main-content {
    flex: 1;
    padding: 20px 16px 80px;
    overflow-y: auto;
  }
  
  .section {
    margin-bottom: 24px;
  }
  
  .section-title {
    display: block;
    font-size: 14px;
    color: #999;
    margin-bottom: 12px;
  }
  
  .risk-levels {
    display: flex;
    justify-content: space-between;
    gap: 12px;
  }
  
  .risk-level-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
  }
  
  .risk-level-item.selected {
    border-width: 1.5px;
  }
  
  .risk-level-0.selected {
    border-color: #07c160;
    background-color: rgba(7, 193, 96, 0.05);
  }
  
  .risk-level-1.selected {
    border-color: #f0ad4e;
    background-color: rgba(240, 173, 78, 0.05);
  }
  
  .risk-level-2.selected {
    border-color: #dd524d;
    background-color: rgba(221, 82, 77, 0.05);
  }
  
  .risk-level-label {
    font-size: 14px;
    margin-top: 6px;
  }
  
  .action-card {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  }
  
  .action-card-content {
    padding: 16px;
  }
  
  .action-card-header {
    display: flex;
    margin-bottom: 16px;
  }
  
  .action-card-text {
    flex: 1;
    margin-left: 12px;
  }
  
  .action-title {
    display: block;
    font-size: 16px;
    color: #333;
    font-weight: 500;
  }
  
  .action-description {
    display: block;
    font-size: 13px;
    color: #999;
    margin-top: 4px;
  }
  
  .action-button {
    width: 100%;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: #fff;
    border-radius: 18px;
    background-color: #007aff;
    margin: 0;
  }
  
  .action-button:disabled {
    background-color: #ccc !important;
    color: #999 !important;
  }
  
  .action-button-orange {
    background-color: #ff9500;
  }
  
  .action-button-green {
    background-color: #07c160;
  }
  

  
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background-color: #fff;
    box-shadow: 0 -1px 6px rgba(0, 0, 0, 0.05);
  }
  
  .submit-button {
    width: 100%;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    color: #fff;
    border-radius: 22px;
    background-color: #007aff;
    margin: 0;
  }
  
  .submit-button.disabled {
    background-color: #ccc;
  }
  </style>
  
  