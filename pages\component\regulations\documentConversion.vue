<template>
    <view class="document-container">
        <header-bar v-if="false" title="制度详情">
            <template #right>
                <!-- 自定义操作按钮 -->
                <view class="nav-right">
                    <view class="nav-btn" @click="handleEdit">
                        <uni-icons type="compose" size="20" />
                        <text class="btn-text">编辑</text>
                    </view>
                </view>
            </template>
        </header-bar>
        <!-- 主内容区 -->
        <scroll-view class="main-content" scroll-y>
            <!-- 文档概览区 -->
            <view class="card">
                <view class="flex-row">
                    <image class="doc-preview"
                        src="https://ai-public.mastergo.com/ai/img_res/f8f58c30fe17881d21af0643ffeb558d.jpg"
                        mode="aspectFill">
                    </image>
                    <view class="doc-info">
                        <text class="doc-name">外部文档示例.pdf</text>
                        <text class="doc-meta">上传日期: 2025-01-15</text>
                        <text class="doc-meta">文件大小: 2.3 MB</text>
                        <text class="doc-meta">上传人: 张三</text>
                    </view>
                </view>
            </view>

            <!-- 转换目标设置 -->
            <view class="card">
                <text class="section-title">转换目标设置</text>

                <view class="form-item">
                    <text class="form-label">2. 文档类别</text>
                    <uv-input suffixIcon="arrow-down" placeholder="请输入内容" border="surround"></uv-input>
                    <uv-picker ref="picker"></uv-picker>
                    <picker-input v-model="selectedCity" :columns="cityData" placeholder="请选择城市" />
                    <!-- <view>
                        <uv-input v-model="selectedCity" @tap="handleClick"
                            :suffix-icon="isPickerVisible ? 'arrow-up' : 'arrow-down'" placeholder="测试" :border="border"
                            readonly>
                        </uv-input>

                        <uv-picker ref="pickerRef" :columns="cityData" keyName="label"
                            @confirm="handleConfirm" @close="handleClose"></uv-picker>
                    </view> -->
                </view>
                <!-- 
                <view class="form-item">
                    <text class="form-label">适用范围</text>
                    <filter-tags @change="handleTagChange" :top="0" :tags="['全组织', '部门', '岗位']" :multiple="true" :fixed="false" />
                </view> -->

                <!-- <view class="section-block">
                    <text class="sub-title">责任人</text>
                    <view class="search-input">
                        <input type="text" placeholder="按姓名或工号搜索" />
                        <uni-icons type="search" size="20" color="#999"></uni-icons>
                    </view>
                    <view class="selected-person" v-for="(item, index) in selectedPersonList" :key="item.id">
                        <text>李四 (工号: 10086)</text>
                        <uni-icons type="close" size="20" color="#999" @click="removePerson(item.id)"></uni-icons>
                    </view>
                </view> -->
            </view>

            <!-- 内部文档元数据 -->
            <view class="card">
                <text class="section-title">内部文档元数据</text>

                <view class="form-item">
                    <text class="form-label">内部标题 <text class="required">*</text></text>
                    <input type="text" placeholder="请输入内部标题" />
                    <text class="error-tip hidden">此项为必填</text>
                </view>

                <view class="form-item">
                    <text class="form-label">简要描述</text>
                    <textarea placeholder="请输入简要描述" auto-height />
                    <text class="hint-tip">最多 200 字</text>
                </view>

                <view class="form-item">
                    <text class="form-label">版本号</text>
                    <input type="text" value="v1.0" />
                </view>
            </view>

            <!-- 内容编辑区 -->
            <view class="card">
                <text class="section-title">内容编辑</text>

                <view class="toolbar">
                    <button  class="tool-btn">
                        <uni-icons type="bold" size="20" color="#555"></uni-icons>
                    </button>
                    <button  class="tool-btn">
                        <uni-icons type="italic" size="20" color="#555"></uni-icons>
                    </button>
                    <button  class="tool-btn">
                        <uni-icons type="underline" size="20" color="#555"></uni-icons>
                    </button>
                    <view class="divider"></view>
                    <button  class="tool-btn">
                        <uni-icons type="list-ol" size="20" color="#555"></uni-icons>
                    </button>
                    <button  class="tool-btn">
                        <uni-icons type="list-ul" size="20" color="#555"></uni-icons>
                    </button>
                    <view class="divider"></view>
                    <button  class="tool-btn">
                        <uni-icons type="image" size="20" color="#555"></uni-icons>
                    </button>
                    <button  class="tool-btn">
                        <uni-icons type="table" size="20" color="#555"></uni-icons>
                    </button>
                    <button  class="tool-btn">
                        <uni-icons type="link" size="20" color="#555"></uni-icons>
                    </button>
                    <view class="divider"></view>
                    <button  class="tool-btn">
                        <uni-icons type="undo" size="20" color="#555"></uni-icons>
                    </button>
                    <button  class="tool-btn">
                        <uni-icons type="redo" size="20" color="#555"></uni-icons>
                    </button>
                </view>

                <view class="editor">
                    <text>这里是文档内容编辑区域...</text>
                    <text>您可以在此处编辑转换后的文档内容。</text>
                    <text>支持富文本格式，包括加粗、斜体、下划线、列表、图片、表格等。</text>
                </view>

                <text class="word-count">已输入 45 / 5000 字</text>
            </view>

            <!-- 附件管理区 -->
            <view class="card">
                <text class="section-title">附件管理</text>

                <button  class="add-attachment">
                    <uni-icons type="plus" size="16" color="#666"></uni-icons>
                    <text>添加附件</text>
                </button>

                <view class="attachment-list">
                    <view class="attachment-item">
                        <uni-icons type="file-pdf" size="20" color="#FF5252"></uni-icons>
                        <text class="attachment-name">参考文档.pdf</text>
                        <uni-icons type="close" size="20" color="#999"></uni-icons>
                    </view>
                    <view class="attachment-item">
                        <uni-icons type="image" size="20" color="#4285F4"></uni-icons>
                        <text class="attachment-name">流程图.png</text>
                        <uni-icons type="close" size="20" color="#999"></uni-icons>
                    </view>
                </view>
            </view>

            <!-- 转换指南与校验提示 -->
            <view class="card card-bottom">
                <view class="guideline-header" @tap="toggleGuidelines">
                    <text class="section-title">查看转换指南</text>
                    <uni-icons :type="showGuidelines ? 'up' : 'down'" size="20" color="#666"></uni-icons>
                </view>

                <view class="guideline-content" v-if="showGuidelines">
                    <text class="guideline-item">• 确保内部标题与原文档主题一致</text>
                    <text class="guideline-item">• 适用范围请根据实际工作需求选择</text>
                    <text class="guideline-item">• 转换后请校对格式与链接是否正确</text>
                    <text class="guideline-item">• 重要内容请使用加粗或高亮标记</text>
                    <text class="guideline-item">• 版本号需按规范递增更新</text>
                </view>

                <view class="validation-errors" v-if="hasError">
                    <text>内部标题不能为空</text>
                </view>
            </view>
        </scroll-view>

        <!-- 底部操作栏 -->
        <view class="action-bar">
            <button  class="cancel-btn">取消</button>
            <button  class="draft-btn">保存草稿</button>
            <button  class="submit-btn">提交审核</button>
        </view>

        <!-- 预览模态框 -->
        <view class="preview-modal" v-if="false">
            <view class="preview-content">
                <view class="preview-header">
                    <text class="preview-title">预览效果</text>
                    <uni-icons type="close" size="24" color="#666" @tap="closePreview"></uni-icons>
                </view>

                <scroll-view class="preview-body" scroll-y>
                    <text class="preview-heading">内部文档示例</text>
                    <text class="preview-text">这里是预览内容...</text>
                    <text class="preview-text">您可以在此查看转换后的文档效果。</text>
                    <text class="preview-text">所有格式和内容都将以最终呈现的效果显示。</text>
                </scroll-view>

                <button  class="preview-back-btn" @tap="closePreview">返回编辑</button>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import HeaderBar from '@/components/headerBar.vue'
import PickerInput from '@/components/picker-input.vue';
import filterTags from '@/components/filterTags.vue';
const cityData = ref([
    [{
        label: '雪月夜',
        // 其他属性值
        id: 2021
        // ...
    }, {
        label: '冷夜雨',
        id: 804
    }]
])
const pickerRef = ref(null);
const isPickerVisible = ref(false)
function handleClose(){
    isPickerVisible.value = !isPickerVisible.value
}
function handleConfirm(item) {
    pickerRef.value.close()
    isPickerVisible.value = !isPickerVisible.value
    selectedCity.value = item.value[0].label
}
function handleClick() {
    pickerRef.value.open()
    isPickerVisible.value = !isPickerVisible.value
}

const selectedCity = ref('')

const selectedPersonList = ref([
    {
        name: '李四 (工号: 10086)',
        id: '1',
    },
    {
        name: '张三 (工号: 10010)',
        id: '2',
    },
]);
const showGuidelines = ref(false);
const showPreview = ref(false);
const hasError = ref(false);
const docTypes = ref(['操作手册', '规章制度', '工作流程', '技术文档']);

function handleTagChange(item) {
    console.log(item, '111111111111111');
}

const removePerson = (id) => {
    selectedPersonList.value = selectedPersonList.value.filter((item) => item.id !== id)
}

const onDocTypeChange = (e) => {
    docType.value = docTypes.value[e.detail.value];
}

const toggleGuidelines = () => {
    showGuidelines.value = !showGuidelines.value;
};

const openPreview = () => {
    showPreview.value = true;
};

const closePreview = () => {
    showPreview.value = false;
};

// 模拟自动保存提示
setTimeout(() => {
    uni.showToast({
        title: '已自动保存草稿',
        icon: 'none',
        duration: 3000
    });
}, 30000);
</script>

<style lang="scss" scoped>
.scope-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.scope-tag {
    padding: 10rpx 30rpx;
    border-radius: 40rpx;
    font-size: 26rpx;
    background-color: #EFEFF4;
    color: #333333;
}

.scope-tag.active {
    background-color: #1A73E8;
    color: #ffffff;
}

.form-search {
    position: relative;
}

.search-input {
    flex: 1;
    padding-left: 60rpx;
}

.document-container {
    display: flex;
    flex-direction: column;
    height: auto;
    background-color: #f8f9fa;

    .main-content {
        flex: 1;
        margin-top: 88rpx;
        padding: 24rpx 32rpx;
        box-sizing: border-box;
    }

    .card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 32rpx;
        margin-bottom: 32rpx;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .card-bottom {
        margin-bottom: 125rpx;
    }

    .flex-row {
        display: flex;
    }

    .doc-preview {
        width: 192rpx;
        height: 256rpx;
        border-radius: 8rpx;
        background-color: #f5f5f5;
    }

    .doc-info {
        flex: 1;
        margin-left: 32rpx;
        display: flex;
        flex-direction: column;
    }

    .doc-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 16rpx;
    }

    .doc-meta {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;
    }

    .section-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 32rpx;
    }

    .section-block {
        margin-bottom: 48rpx;
    }

    .sub-title {
        font-size: 26rpx;
        font-weight: 500;
        color: #666;
        margin-bottom: 16rpx;
    }

    .radio-item,
    .checkbox-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        font-size: 26rpx;
        color: #333;
    }

    .search-input {
        position: relative;
        margin-bottom: 16rpx;
    }

    .search-input input {
        width: 100%;
        height: 72rpx;
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
        padding: 0 72rpx 0 24rpx;
        font-size: 26rpx;
    }

    .search-input .uni-icons {
        position: absolute;
        right: 24rpx;
        top: 50%;
        transform: translateY(-50%);
    }

    .selected-person {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 24rpx;
        background-color: #f5f5f5;
        border-radius: 8rpx;
        font-size: 26rpx;
        margin-bottom: 32rpx;
    }

    .form-item {
        margin-bottom: 32rpx;
    }

    .form-label {
        font-size: 26rpx;
        font-weight: 500;
        color: #666;
        margin-bottom: 16rpx;
        display: block;
    }

    .required {
        color: #ff4d4f;
    }

    input,
    textarea {
        width: 100%;
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
        padding: 16rpx 24rpx;
        font-size: 26rpx;
    }

    textarea {
        min-height: 160rpx;
    }

    .error-tip {
        font-size: 24rpx;
        color: #ff4d4f;
        margin-top: 8rpx;
    }

    .hint-tip {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
    }

    .toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;
        padding-bottom: 16rpx;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 16rpx;
    }

    .tool-btn {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: none;
        border: none;
        padding: 0;
    }

    .divider {
        width: 1px;
        height: 32rpx;
        background-color: #e0e0e0;
        margin: 0 8rpx;
    }

    .editor {
        min-height: 400rpx;
        padding: 16rpx;
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
    }

    .word-count {
        font-size: 24rpx;
        color: #999;
        text-align: right;
        margin-top: 16rpx;
    }

    .add-attachment {
        display: inline-flex;
        align-items: center;
        padding: 16rpx 24rpx;
        border: 1px dashed #e0e0e0;
        border-radius: 8rpx;
        background: none;
        font-size: 26rpx;
        color: #666;
    }

    .add-attachment .uni-icons {
        margin-right: 8rpx;
    }

    .attachment-list {
        margin-top: 24rpx;
    }

    .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx;
        border-radius: 8rpx;
        margin-bottom: 16rpx;
        background-color: #fafafa;
    }

    .attachment-name {
        flex: 1;
        margin: 0 16rpx;
        font-size: 26rpx;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .guideline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
    }

    .guideline-content {
        padding: 16rpx 0;
    }

    .guideline-item {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
    }

    .validation-errors {
        padding: 16rpx;
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #ff4d4f;
        margin-top: 16rpx;
    }

    .action-bar {
        left: 0;
        right: 0;
        bottom: 0;
        position: fixed;
        display: flex;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        background-color: #fff;
        border-top: 1px solid #f0f0f0;
        z-index: 10;
    }

    .action-bar button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        // border-radius: 40rpx;
        margin: 0 16rpx;
    }

    .cancel-btn {
        background-color: #f5f5f5;
        color: #666;
    }

    .draft-btn {
        background-color: #fff;
        color: #1a73e8;
        border: 1px solid #1a73e8;
    }

    .submit-btn {
        background-color: #1a73e8;
        color: #fff;
    }

    .preview-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 100;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .preview-content {
        width: 90%;
        height: 80%;
        background-color: #fff;
        border-radius: 16rpx;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        border-bottom: 1px solid #f0f0f0;
    }

    .preview-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
    }

    .preview-body {
        flex: 1;
        padding: 32rpx;
    }

    .preview-heading {
        font-size: 40rpx;
        font-weight: bold;
        margin-bottom: 32rpx;
        display: block;
    }

    .preview-text {
        font-size: 28rpx;
        margin-bottom: 24rpx;
        display: block;
    }

    .preview-back-btn {
        margin: 32rpx;
        background-color: #1a73e8;
        color: #fff;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        border-radius: 40rpx;
    }

    .hidden {
        display: none;
    }

    .nav-right {
        display: flex;
        align-items: center;

        .nav-btn {
            display: flex;
            align-items: center;
            margin-left: 60rpx;
        }

        .btn-text {
            font-size: 26rpx;
            color: #1A73E8;
            margin-left: 8rpx;
        }
    }
}
</style>