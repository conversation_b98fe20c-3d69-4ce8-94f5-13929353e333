<template>
    <view class="help-feedback-detail-container">
        <!-- 主要内容区 -->
        <view class="main-content">
            <!-- 功能入口卡片区 -->
            <view class="card-grid">
                <!-- 常见问题卡片 -->
                <view class="card" @touchstart="touchStart" @touchend="touchEnd">
                    <view class="icon-container">
                        <uni-icons type="pyq" size="20" color="#4F46E5"></uni-icons>
                    </view>
                    <text class="card-title">常见问题</text>
                    <text class="card-desc">查看FAQ</text>
                </view>

                <!-- 在线客服卡片 -->
                <view class="card" @touchstart="touchStart" @touchend="touchEnd">
                    <view class="icon-container">
                        <uni-icons type="chat" size="20" color="#4F46E5"></uni-icons>
                    </view>
                    <text class="card-title">在线客服</text>
                    <text class="card-desc">立即沟通</text>
                </view>

                <!-- 意见反馈卡片 -->
                <view class="card" @touchstart="touchStart" @touchend="touchEnd">
                    <view class="icon-container">
                        <uni-icons type="compose" size="20" color="#4F46E5"></uni-icons>
                    </view>
                    <text class="card-title">意见反馈</text>
                    <text class="card-desc">提交反馈</text>
                </view>

                <!-- 联系电话卡片 -->
                <view class="card" @touchstart="touchStart" @touchend="touchEnd">
                    <view class="icon-container">
                        <uni-icons type="phone" size="20" color="#4F46E5"></uni-icons>
                    </view>
                    <text class="card-title">联系电话</text>
                    <text class="card-desc">一键拨打</text>
                </view>
            </view>

            <!-- 热门FAQ折叠面板 -->
            <view class="faq-container">
                <view class="faq-group" v-for="(group, index) in faqList" :key="index">
                    <view class="faq-header" @tap="toggleFaq(index)">
                        <view class="header-left">
                            <text class="group-title">{{ group.title }}</text>
                            <text class="count">{{ group.items.length }} 条</text>
                        </view>
                        <uni-icons :type="activeIndexes.includes(index) ? 'up' : 'down'" size="16"
                            color="#666" />
                    </view>
                    <view class="faq-content" v-if="activeIndexes.includes(index)">
                        <view class="faq-item" v-for="(item, itemIndex) in group.items" :key="itemIndex">
                            {{ item }}
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue';

const activeIndex = ref(0);

const activeIndexes = ref([]) // 存储所有展开的面板索引
const faqList = ref([
  {
    title: '入职流程',
    items: [
      '如何办理入职手续？',
      '试用期政策有哪些？',
      '入职体检要求是什么？',
      '需要准备哪些材料？',
      '入职培训安排如何？'
    ]
  },
  {
    title: '报销流程',
    items: [
      '差旅报销需要哪些单据？',
      '报销审批流程是怎样的？',
      '报销到账时间是多久？'
    ]
  },
  {
    title: '合规审查',
    items: [
      '合同审查申请如何提交？',
      '合规培训资料在哪里查看？'
    ]
  },
  {
    title: 'IT支持',
    items: [
      '如何重置密码？',
      'VPN连接问题如何解决？',
      '办公软件安装指南',
      '打印机连接问题'
    ]
  }
])

// 切换折叠状态
const toggleFaq = (index) => {
  const currentIndex = activeIndexes.value.indexOf(index)
  if (currentIndex > -1) {
    activeIndexes.value.splice(currentIndex, 1)
  } else {
    activeIndexes.value.push(index)
  }
}

const goBack = () => {
    uni.navigateBack();
};

const touchStart = (e) => {
    const target = e.currentTarget;
    target.style.transform = 'scale(0.98)';
    target.style.boxShadow = '0 4rpx 12rpx rgba(0,0,0,0.1)';
};

const touchEnd = (e) => {
    const target = e.currentTarget;
    target.style.transform = '';
    target.style.boxShadow = '';
};
</script>

<style lang="scss" scoped>
.help-feedback-detail-container {
    height: 100vh;
    background-color: #f8f9fa;

    .main-content {
        padding-top: 30rpx;
        padding-bottom: 100rpx;
    }

    .card-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 32rpx;
        padding: 32rpx;
    }

    .card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32rpx;
        background-color: #fff;
        border-radius: 16rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        transition: all 0.5s ease;
    }

    .icon-container {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16rpx;
    }

    .card-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
    }

    .card-desc {
        font-size: 24rpx;
        color: #999;
    }

    .faq-container {
        padding: 0 32rpx 32rpx;
    }

    .faq-group {
        background-color: #fff;
        border-radius: 16rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        margin-bottom: 24rpx;
        overflow: hidden;
    }

    .faq-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .group-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-right: 16rpx;
    }

    .count {
        font-size: 24rpx;
        color: #999;
    }

    .faq-content {
        padding: 0 32rpx 24rpx;
    }

    .faq-item {
        position: relative;
        padding-left: 40rpx;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
        line-height: 1.5;
    }

    .faq-item::before {
        content: "•";
        position: absolute;
        left: 16rpx;
        color: #4F46E5;
    }
}
</style>