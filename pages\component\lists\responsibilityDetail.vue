<template>
    <view class="page-container">
        <!-- 内容区域 -->
        <view class="content" scroll-y>
                <!-- 状态标识区 -->
                <view class="card-section status-header">
                    <view class="create-info-container">
                        <view class="create-info-item">创建人：{{ responsibilityDetail.createdBy }}</view>
                        <view class="create-info-item">创建时间：{{ responsibilityDetail.createdAt }}</view>
                    </view>
                    <view :class="['status-tag', statusClass]">{{ statusText }}</view>
                </view>
                <!-- 职责信息区 -->
                <view class="card">
                    <view class="section-title">职责信息区</view>
                    <view class="info-row">
                        <text class="info-label">部门：</text>
                        <text class="info-value">{{ responsibilityDetail.orgUnitName }}</text>
                    </view>
                    <view class="info-row">
                        <text class="info-label">岗位：</text>
                        <text class="info-value">{{ responsibilityDetail.postName }}</text>
                    </view>
                    <view class="info-content">
                        <text class="content-title">基本职责：</text>
                        <view class="content-text">{{ responsibilityDetail.basicDuty }}</view>
                    </view>
                </view>
                <!-- 合规信息区 -->
                <view class="card">
                    <view class="section-title">合规信息区</view>
                    <view class="compliance-section positive">
                        <text class="compliance-title">正面合规要求：</text>
                        <view class="compliance-text">{{ responsibilityDetail.complianceInfo?.positiveComplianceRequirement }}</view>
                    </view>
                    <view class="compliance-section negative">
                        <text class="compliance-title">负面合规要求：</text>
                        <view class="compliance-text">{{ responsibilityDetail.complianceInfo?.negativeComplianceRequirement }}</view>
                    </view>
                    <view class="compliance-basis">
                        <text class="basis-title">合规依据：</text>
                        <view class="basis-text">{{ responsibilityDetail.complianceInfo?.complianceBasis }}</view>
                    </view>
                </view>
                <!-- 风险防控区 -->
                <view class="card">
                    <view class="section-title">风险防控区</view>
                    <view class="risk-source">
                        <text class="subsection-title">风险来源（八项权利识别）：</text>
                        <view class="power-grid">
                            <view v-for="(riskType, index) in riskTypesList" :key="index"
                                class="power-item" :class="{'identified': riskType.identified}">
                                <text>{{ riskType.name }}</text>
                                <uni-icons v-if="riskType.identified" type="checkmarkempty" size="16" color="#4CAF50" />
                            </view>
                        </view>
                    </view>
                    <view class="risk-section">
                        <text class="subsection-title">岗位风险等级：</text>
                        <view class="risk-level">
                            <view class="risk-circle" :style="{backgroundColor: riskColor}"></view>
                            <text class="risk-text">{{ getRiskLevelText }}</text>
                        </view>
                    </view>
                    <view class="prevention-measures">
                        <text class="subsection-title">防控措施：</text>
                        <view class="measures-text">{{ responsibilityDetail.riskControlInfo?.controlMeasures }}</view>
                    </view>
                </view>
        </view>
        <!-- 只有在数据加载完成后才显示底部操作栏，避免闪动效果 -->
        <view v-if="isDataLoaded">
            <FooterBar @submit="handleFooterClick" :buttons="footerButtons" />
        </view>
    </view>
</template>
<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/pinia.js';
import duties from '@/api/duties/duties.js';
import FooterBar from '@/components/footerBar.vue'
import getDictData from '@/utils/dict.js'
const responsibilityId = ref('');
const responsibilityDetail = ref({});
const riskSourceDict = ref([]);
const userStore = useUserStore();

// 数据加载状态
const isDataLoaded = ref(false);

// 律师角色判断
const lawyer = ref(false);
lawyer.value = userStore.lawyer;

const footerButtons = computed(() => {
	// 状态为APPROVED时，律师和非律师都不能操作
	if (responsibilityDetail.value.approvalStatus === 'APPROVED') {
		return [];
	}
	
	// 律师角色：只有在PENDING状态下才能操作
	if (lawyer.value && responsibilityDetail.value.approvalStatus === 'PENDING') {
		return [
			{
				text: '编辑',
				type: 'submit',
				slotName: 'submit',
				bgColor: '#1a73e8',
				textColor: '#fff'
			}
		];
	}
	
	// 非律师角色：只有在DRAFT状态下才能操作
	if (!lawyer.value && responsibilityDetail.value.approvalStatus === 'DRAFT') {
		return [
			{
				text: '编辑',
				type: 'submit',
				slotName: 'submit',
				bgColor: '#1a73e8',
				textColor: '#fff'
			}
		];
	}
	
	// 其他情况不显示按钮
	return [];
});
const handleFooterClick = () => { 
	uni.navigateTo({
			url: `/pages/component/lists/addResponsibility?type=detail&id=${responsibilityId.value}`
		})
};
// 八项权力列表
const riskTypesList = computed(() => {
    if (!riskSourceDict.value || riskSourceDict.value.length === 0) {
        return [];
    }
    
    const identifiedTypes = responsibilityDetail.value.riskControlInfo?.riskTypes || [];
    return riskSourceDict.value.map(type => ({
        name: type.name,
        value: type.value,
        identified: identifiedTypes.includes(type.value) || identifiedTypes.includes(type.name)
    }));
});

// 获取风险来源字典数据
const getRiskSourceDict = async () => {
    try {
        const result = await getDictData(80);
        if (result && result.length > 0) {
            riskSourceDict.value = result.map(item => ({
                name: item.name,
                value: item.value
            }));
        }
        console.log('风险等级字典数据:', riskSourceDict.value);
    } catch (error) {
        console.error('获取风险来源字典数据失败:', error);
    }
};

// 风险等级映射
const riskLevelMap = {
    'HIGH': '高危',
    'MEDIUM': '中危',
    'LOW': '低危',
    '高危': '高危',
    '中危': '中危',
    '低危': '低危'
};

// 风险等级颜色
const riskColor = computed(() => {
    const level = responsibilityDetail.value.riskControlInfo?.riskLevel || responsibilityDetail.value.riskLevel;
    const mappedLevel = riskLevelMap[level] || level;
    switch (mappedLevel) {
        case '高危':
            return '#F44336'; // 红色
        case '中危':
            return '#FF9800'; // 橙色
        case '低危':
            return '#4CAF50'; // 绿色
        default:
            return '#F44336';
    }
});

// 获取中文风险等级
const getRiskLevelText = computed(() => {
    const level = responsibilityDetail.value.riskControlInfo?.riskLevel || responsibilityDetail.value.riskLevel;
    return riskLevelMap[level] || level || '未知';
});

const statusText = computed(() => {
    switch (responsibilityDetail.value.approvalStatus) {
        case 'APPROVED': return '已生效';
        case 'PENDING': return '待审核';
        case 'DRAFT': return '草稿';
        case 'REJECTED': return '已驳回';
        default: return '';
    }
});

const statusClass = computed(() => {
    switch (responsibilityDetail.value.approvalStatus) {
        case 'APPROVED': return 'active';
        case 'PENDING': return 'pending';
        case 'DRAFT': return 'draft';
        case 'REJECTED': return 'rejected';
        default: return 'active';
    }
});

// 返回按钮
const handleBack = () => {
    uni.navigateBack();
};

// 获取合规义务详情数据
const getResponsibilityDetail = async (id) => {
    try {
        uni.showLoading({ title: '加载中...' });
        const response = await duties.dutyList({}, 'info', {
            id: id
        });
        console.log('合规义务详情数据:', response);
        if (response) {
            responsibilityDetail.value = response;
        }
        isDataLoaded.value = true; // 数据加载完成
        uni.hideLoading();
    } catch (error) {
        console.error('获取合规义务详情失败:', error);
        isDataLoaded.value = true; // 即使失败也设置为已加载，避免界面卡住
        uni.hideLoading();
        uni.showToast({
            title: '获取数据失败',
            icon: 'none'
        });
    }
};

onLoad((options) => {
    console.log('页面参数:', options);
    if (options.id) {
        responsibilityId.value = options.id;
        getResponsibilityDetail(options.id);
    } else {
        uni.showToast({
            title: '缺少必要参数',
            icon: 'none'
        });
    }
    // 获取风险来源字典数据
    getRiskSourceDict();
});
</script>
<style>
.page-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #F5F5F5;
}

.content {
    flex: 1;
    overflow: auto;
    padding: 24rpx;
}

.card {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.status-card {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 24rpx;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.create-info-container {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.create-info-item {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 4rpx;
}

.create-info-item:last-child {
    margin-bottom: 0;
}

.info-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
}

.info-label {
    font-size: 14px;
    color: #666666;
    width: 120rpx;
    flex-shrink: 0;
    margin-right: 20rpx;
}

.info-value {
    font-size: 14px;
    color: #333333;
    flex: 1;
    word-break: break-all;
}

.info-content {
    font-size: 14px;
    color: #333333;
    line-height: 1.6;
    margin-bottom: 16rpx;
}

.content-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 12rpx;
}

.content-text {
    font-size: 14px;
    color: #666666;
    line-height: 1.6;
    margin: 16rpx 0;
}

.compliance-section {
    padding: 16rpx;
    border-radius: 8rpx;
    margin-bottom: 16rpx;
}

.positive {
    background-color: #E8F5E9;
}

.negative {
    background-color: #FFEBEE;
}

.compliance-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 12rpx;
}

.compliance-text {
    font-size: 14px;
    color: #666666;
    line-height: 1.6;
    margin: 16rpx 0;
}

.highlight {
    color: #1890FF;
    font-weight: 500;
}

.highlight-warning {
    color: #FF4D4F;
    font-weight: 500;
}

.compliance-basis {
    margin-top: 24rpx;
    padding: 16rpx;
}

.basis-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 12rpx;
}

.basis-text {
    font-size: 14px;
    color: #666666;
    line-height: 1.6;
    margin: 16rpx 0;
}

.power-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
    margin: 24rpx 0;
}

.power-item {
    display: flex;
    align-items: center;
    padding: 16rpx;
    background-color: #F8F9FA;
    border-radius: 8rpx;
    font-size: 14px;
}

.power-item.identified {
    background-color: #F0F9F0;
}

.risk-source {
    margin-bottom: 24rpx;
}

.subsection-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    display: block;
    margin-bottom: 16rpx;
}

.risk-section {
    margin-bottom: 32rpx;
}

.risk-level {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.risk-circle {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    margin-right: 12rpx;
}

.risk-text {
    font-size: 14px;
    color: #333333;
}

.prevention-measures {
    margin-top: 24rpx;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #FFFFFF;
    padding: 20rpx 40rpx;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20rpx;
    z-index: 1000;
}

.edit-btn {
    flex: 1;
    background-color: #409EFF;
    color: #FFFFFF;
    border: none;
    border-radius: 8rpx;
    padding: 24rpx 0;
    font-size: 32rpx;
    font-weight: bold;
}

.export-btn {
    flex: 1;
    background-color: #67C23A;
    color: #FFFFFF;
    border: none;
    border-radius: 8rpx;
    padding: 24rpx 0;
    font-size: 32rpx;
    font-weight: bold;
}


</style>