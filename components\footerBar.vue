<template>
	<view v-if="normalizedButtons.length > 0">
		<view id="action-bar" class="action-bar" :style="mergedBarStyle">
			<button v-for="(btn, index) in normalizedButtons" :key="index" class="action-btn"
				:style="[getBtnStyle(btn), btn.style]" 
				:disabled="btn.disabled" 
				@click="handleClick(btn)">
				<slot :name="btn.slotName" :btn="btn">
					{{ btn.text }}
				</slot>
			</button>
		</view>
		<!-- 生成等高元素 -->
		<uv-gap :height="footerHeight" />
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		nextTick,
		getCurrentInstance,
	} from 'vue'
	const footerHeight = ref(0); // 等高元素
	const instance = getCurrentInstance()
	onMounted(() => {
		nextTick(() => {
			const query = uni.createSelectorQuery().in(instance.proxy)
			query.select('#action-bar').boundingClientRect(rect => {
				if (rect) {
					footerHeight.value = rect.height
				}
			}).exec()
		});
	})

	const props = defineProps({
		buttons: {
			type: Array,
			default: () => []
		},
		barStyle: {
			type: Object,
			default: () => ({})
		},
		btnCommon: {
			type: Object,
			default: () => ({
				height: '80rpx',
				lineHeight: '80rpx',
				fontSize: '28rpx',
				margin: '0 16rpx',
				// flex: '1'
			})
		}
	})

	const emit = defineEmits(['click', 'cancel', 'draft', 'submit', 'confirm', 'preview', 'ai','del', 'save', 'review'])

	// 合并默认按钮配置
	const defaultButtons = [{
			text: '取消',
			type: 'cancel',
			slotName: 'cancel',
			bgColor: '#f5f5f5',
			textColor: '#666'
		},
		{
			text: '草稿',
			type: 'draft',
			slotName: 'draft',
			bgColor: '#fff',
			textColor: '#1a73e8',
			border: '1px solid #1a73e8'
		},
		{
			text: '提交',
			type: 'submit',
			slotName: 'submit',
			bgColor: '#1a73e8',
			textColor: '#fff'
		}
	]

	const normalizedButtons = computed(() => {
		// 如果父组件明确传入空数组，则不显示任何按钮
		if (Array.isArray(props.buttons) && props.buttons.length === 0) {
			return []
		}
		// 如果没有传入buttons或传入undefined/null，则使用默认按钮
		const buttons = props.buttons && props.buttons.length > 0 ? props.buttons : defaultButtons
		return buttons.filter(btn => !btn.isHidden)
	})

	// 合并工具栏样式
	const mergedBarStyle = computed(() => {
		const buttonCount = normalizedButtons.value.length
		let justifyContent = 'space-between'
		
		if (buttonCount === 1) {
			justifyContent = 'center'
		} else if (buttonCount === 2) {
			justifyContent = 'space-between'
		} else if (buttonCount >= 3) {
			justifyContent = 'space-around'
		}
		
		return {
			backgroundColor: '#fff',
			borderTop: '1px solid #f0f0f0',
			padding: '24rpx 32rpx 34rpx 32rpx',
			justifyContent,
			...props.barStyle
		}
	})

	// 按钮样式计算
	const getBtnStyle = (btn) => {
		const buttonCount = normalizedButtons.value.length
		let buttonStyle = {
			backgroundColor: btn.bgColor,
			color: btn.textColor,
			border: btn.border || 'none',
			...props.btnCommon
		}
		
		// 根据按钮数量和文字长度调整宽度和边距
		if (buttonCount === 1) {
			buttonStyle.width = '100%'
			buttonStyle.margin = '0'
		} else if (buttonCount === 2) {
			// 两个按钮时大小相等，左右分布
			buttonStyle.width = 'calc(50% - 8rpx)'
			buttonStyle.margin = '0 4rpx'
		} else {
			// 三个或更多按钮时根据文字长度 + 统一内边距计算宽度
			const buttons = normalizedButtons.value
			const paddingWidth = 32 // 左右各16rpx的内边距
			
			// 计算每个按钮的内容宽度（基于文字长度）
			const textWidths = buttons.map(button => {
				// 估算文字宽度：中文字符约28rpx，英文字符约14rpx
				const chineseChars = (button.text.match(/[\u4e00-\u9fa5]/g) || []).length
				const otherChars = button.text.length - chineseChars
				return chineseChars * 28 + otherChars * 14 + paddingWidth
			})
			
			const totalWidth = textWidths.reduce((sum, width) => sum + width, 0)
			const currentIndex = buttons.findIndex(button => button.text === btn.text && button.type === btn.type)
			const currentTextWidth = textWidths[currentIndex]
			
			// 按比例分配宽度
			const widthPercentage = (currentTextWidth / totalWidth) * 100
			
			buttonStyle.width = `calc(${widthPercentage.toFixed(1)}% - 16rpx)`
			buttonStyle.margin = '0 8rpx'
			buttonStyle.padding = '0 16rpx' // 统一内边距
			buttonStyle.minWidth = 'auto' // 允许自适应最小宽度
		}
		
		return buttonStyle
	}

	// 事件处理
	const handleClick = (btn) => {
		// 如果按钮被禁用，不触发事件
		if (btn.disabled) {
			return;
		}
		emit('click', btn)
		emit(btn.type, btn) // 自动触发对应类型事件
	}
</script>

<style scoped lang="scss">
	.action-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		z-index: 10;
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;
	}

	.action-btn {
		box-sizing: border-box;
		white-space: nowrap;
		text-align: center;
		/* 其他基础样式可通过 btnCommon prop 配置 */
		
		&:disabled {
			opacity: 0.6;
			cursor: not-allowed;
			pointer-events: none;
		}
	}
</style>