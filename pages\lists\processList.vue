<template>
	<view class="list-responsibility-container">
		<z-paging :auto="false" ref="paging" v-model="dataList" @query="queryList" :loading="loading">
			<template #top>
				<header-bar title="风险识别清单" shape="circle" prefixIcon="search" clearable :fixed="false"
					v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click.stop="searchClick">
								<!-- <uni-icons type="search" size="20" /> -->
								<text class="btn-sou">搜索</text>
							</view>
							<view class="nav-btn" v-if="!userStore.lawyer"
								@click="$tools.routeJump(`/pages/component/lists/addProcess?type=add`)">
								<text class="btn-text">新增</text>
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags v-model="tagValue" valueKey="value" :fixed="false" :tags="tags"
					@change="handleTagChange" />
			</template>
			<view>
				<view class="content-container-list">
					<view v-for="(item, index) in dataList" :key="index" class="list-item"
						@click="handleItemClick(item)">
						<view class="item-content">
							<view class="item-t-box">
							<text class="item-title">{{ item.businessDomainName }}</text>
							<text class="info-status" :data-status="item.approvalStatus">{{ getStatusName(item.approvalStatus) }}</text>
						</view>
							<view class="item-info">
								<!-- <text class="info-text" :data-risk="item.riskLevel">
									{{ item.riskLevel === 'HIGH' ? '高危' : item.riskLevel === 'MEDIUM' ? '中危' : '低危' }}
								</text> -->
								<!-- <text class="info-dot">·</text>
								<text>{{ item.orgUnitId }}</text> -->
								<text class="info-tig" style="margin-right: 20rpx;">创建人： {{ item.createdBy }}</text>
								<!-- <text class="info-dot">·</text> -->
								<text class="info-tig">{{ item.createdAt }}</text>
							</view>
						</view>
						<uni-icons type="right" size="16" color="#999" />
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
	import headerBar from '@/components/headerBar.vue'
	import filterTags from '@/components/filterTags.vue'
	import {
		ref,
		nextTick,
		onMounted
	} from 'vue';
	import homeApi from '@/api/home/<USER>'
	import {
		useUserStore
	} from '@/store/pinia.js';
	const userStore = useUserStore();

	const tagValue = ref(null)
	const form = ref({})
	const approvalStatus = ref({
		"DRAFT": '草稿',
		"APPROVED": '审核通过',
		"REJECTED": '审核未通过',
		"PENDING": '审批中'
	});

	function getStatusName(status) {
		return approvalStatus.value[status];
	}
	const tags = ref([{
			name: '全部',
			value: null
		}, {
			name: '草稿',
			value: 'DRAFT'
		},
		{
			name: '审批中',
			value: 'PENDING'
		},
		{
			name: '审核通过',
			value: 'APPROVED'
		},
		{
			name: '审核未通过',
			value: 'REJECTED'
		}
	]);

	onMounted(() => {
		reload();
	});

	const handleTagChange = () => {
		paging.value.reload();
	}

	function getDetail(id) {
		uni.showLoading({
			title: '加载中...'
		});
		homeApi.complianceRiskProcess({
			id: id
		}, 'info').then(res => {
			uni.hideLoading();
			form.value = res
		}).catch(err => {
			uni.hideLoading();
		})
	}
	const handleAdd = () => {
		// 新增职责逻辑
		console.log('新增职责');
	};

	const handleItemClick = (item) => {
		// 点击职责项逻辑 - 跳转到详情页面
		console.log('点击流程项:', item);
		// 统一传入detail
		// uni.navigateTo({
		// 	url: `/pages/component/lists/addProcess?type=detail&id=${item.id}`
		// })
		uni.navigateTo({
			url: `/pages/component/lists/processDetail?type=detail&id=${item.id}`
		})
	};

	const searchValue = ref("")
	const paging = ref(null)
	const dataList = ref([])
	const loading = ref(false)

	function reload() {
		nextTick(() => {
			paging?.value.reload();
		})
	}

	function searchClick() {
		reload()
	}
	// 搜索方法
	const search = () => {
		reload()
	}

	// 列表查询
	const queryList = (pageNo, pageSize) => {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
		loading.value = true;
		var params = {
			page: pageNo - 1,
			size: pageSize,
			approvalStatus: tagValue.value,
			// tenantId: userStore.tenantId,
			businessDomainName: searchValue.value
		}

		homeApi.complianceRiskProcess(params).then((res) => {
			// let arr = res.content
			paging.value.complete(res.content);
			loading.value = false;
			uni.hideLoading();
		}).catch((err) => {
			paging.value.complete(false);
			loading.value = false;
			uni.hideLoading();
		})
	}

	// 暴露方法供父组件调用
	defineExpose({
		reload
	});
</script>

<style lang="scss" scoped>
	@import '/static/css/nav.scss';

	.list-responsibility-container {
		display: flex;
		flex-direction: column;
		// height: 100%;
		min-height: 100%;
		background-color: #F5F7FA;

		.content-container {
			flex: 1;
			// margin-bottom: 88rpx;
		}

		.content-container-list {
			padding: 20rpx 32rpx;
		}

		.list-item {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			margin-bottom: 16rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		}

		.item-content {
			flex: 1;
		}

		.item-title {
			font-size: 32rpx;
			font-weight: normal;
			color: #1a1a1a;
			margin-bottom: 8rpx;
		}

		.item-margin {
			margin-left: 20px;
		}

		.item-info {
			display: flex;
			margin-top: 10rpx;
			align-items: center;
		}

		.info-text {
			display: inline-block;
			/* 确保内边距和背景生效 */
			padding: 10rpx 16rpx;
			/* 上下5px 左右8px */
			color: white;
			/* 文字白色 */
			font-weight: normal;
			/* 加粗文字 */
			border-radius: 6rpx;
			/* 可选：轻微圆角使更美观 */
			text-align: center;
			/* 文字居中 */
			font-size: 24rpx;
			border-radius: 20rpx;
			/* 统一字体大小 */
		}

		/* 高危样式 - 红色背景 */
		.info-text.high-risk,
		.info-text[data-risk="HIGH"] {
			background-color: rgb(255, 234, 234);
			color: #ff7875;
			/* 标准警告红 */
		}

		/* 中危样式 - 橙色背景 */
		.info-text.medium-risk,
		.info-text[data-risk="MEDIUM"] {
			background-color: rgb(255, 244, 229);
			color: #ffc069;
			/* 标准警告橙 */
		}

		/* 低危样式 - 绿色背景 */
		.info-text.low-risk,
		.info-text[data-risk="LOW"] {
			background-color: rgb(230, 255, 250);
			color: rgb(49, 151, 149);
			/* 标准成功绿 */
		}

		.info-dot {
			font-size: 24rpx;
			color: #666;
			margin: 0 8rpx;
		}

		.info-tig {
			font-size: 24rpx;
			color: $uni-text-color-grey;
		}
	}

	.item-t-box {
		display: flex;
		align-items: center;
	}

	.info-status {
		margin-left: 20rpx;
		white-space: nowrap;
		padding: 2rpx 8rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
	}
	
	/* 状态颜色样式 */
	.info-status[data-status="DRAFT"] {
		background-color: #f3f7fd;
		color: #777777;
	}
	
	.info-status[data-status="PENDING"] {
		background-color: #fff7e6;
		color: #fa8c16;
	}
	
	.info-status[data-status="APPROVED"] {
		background-color: #f6ffed;
		color: #52c41a;
	}
	
	.info-status[data-status="REJECTED"] {
		background-color: #fff2f0;
		color: #ff4d4f;
	}
</style>