<template>
    <view class="compliance-regulations-container">

        <!-- Main Content -->
        <view class="main-content">
            <!-- Report Info Card -->
            <view class="card title-section">
                <text class="title">{{ detailInfo?.name || '' }}</text>
                <view class="status-row">
                    <view class="status-badge">{{ detailInfo?.status || '' }}</view>
                    <text class="version">审查类型：{{ detailInfo?.contractType || '' }}</text>
                </view>
                <text class="publisher">
                    发起：{{ detailInfo?.createdBy || '' }} ·
                    审查员：{{ detailInfo?.auditBy || '' }} ·
                    日期：{{ detailInfo?.createdAt }}
                </text>
            </view>

            <!-- Tab Navigation -->
            <view class="tab-bar">
                <view @click="active = index" class="tab-item" :class="{ active: active === index }"
                    v-for="(tab, index) in tabs" :key="index">
                    <text class="tab-text">{{ tab }}</text>
                </view>
            </view>

            <!-- Content Section -->
            <view class="card content-section">
                <!-- 审查结果 -->
                <view v-if="active === 0" class="tab-content">
                    <scroll-view class="scroll-container" scroll-y="true">
                        <view class="content-container">
                            <view class="content-item">
                                <!-- <text class="chapter-title">第一章 总则</text> -->
                                <text class="content-text">{{ result }}</text>
                                <!-- <text class="content-text">第二条 本细则适用于公司总部及所有分支机构、子公司。</text> -->
                            </view>
                        </view>
                    </scroll-view>
                </view>

                <!-- 审查记录 -->
                <view v-if="active === 1" class="tab-content">
                    <scroll-view class="scroll-container" scroll-y="true">
                        <view class="workflow-container">
                            <view class="workflow-title">审查记录</view>
                            <view class="timeline">
                                <view class="timeline-item" v-for="(record, index) in reviewRecords" :key="index">
                                    <view class="timeline-dot active"></view>
                                    <view class="timeline-content">
                                        <view class="approval-header">
                                            <text class="approval-step">{{ record.taskName }}</text>
                                            <!-- <text class="approval-status" :class="record.status">{{ record.statusText
                                                }}</text> -->
                                        </view>
                                        <view v-for="(recordChild) in record.contentList" :key="recordChild.id">
                                           <mp-html :content="recordChild.content" />
                                        </view>
                                        <view class="approval-comment">
                                            <text class="comment-label">创建人：{{ record.createdBy }}</text>
                                            <text class="comment-text">{{ record.createdAt }}</text>
                                        </view>
                                        <view class="approval-comment">
                                            <text class="comment-label">审查意见：</text>
                                            <text class="comment-text">{{ record.comment }}</text>
                                        </view>
                                   
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </view>

        <!-- 审查意见弹窗 -->
        <uv-popup ref="reviewPopup" mode="center" :mask-click="false" border-radius="16">
            <view class="review-popup">
                <view class="popup-content">
                    <uv-textarea autoHeight v-model="reviewOpinion" placeholder="请输入审查意见（不通过时必填）" maxlength="500"
                        border="surround" :show-confirm-bar="false" count></uv-textarea>
                </view>
                <view class="popup-buttons">
                    <view class="uv-btn">
                        <uv-button text="不通过" type="error" plain size="normal" @click="handleReview(false)"
                            customStyle="margin-right: 16rpx;"></uv-button>
                    </view>
                    <view class="uv-btn">
                        <uv-button text="通过" type="primary" size="normal" @click="handleReview(true)"></uv-button>
                    </view>
                </view>
            </view>
        </uv-popup>

        <footer-bar :buttons="footerButtons" @cancel="footerCancel" @submit="showReviewPopup">
        </footer-bar>

    </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import footerBar from '@/components/footerBar.vue';
import contractApi from '@/api/contract/index.js';

const active = ref(0);
const tabs = ref(['审查结果', '审查记录']);
// 页面参数
const pageParams = ref({
    id: null,
    type: null,
    detailInfo: null // 新增：接收详情信息
});
const result = ref('');
// 新增：存储详情信息
const detailInfo = ref(null);
// 审查意见相关
const reviewOpinion = ref('');
const reviewPopup = ref(null);
// AI审查结果状态控制
const aiReviewCompleted = ref(false);

// 底部按钮配置
const footerButtons = computed(() => [
    {
        text: '返回',
        type: 'cancel',
        slotName: 'cancel',
        bgColor: '#f5f5f5',
        textColor: '#666'
    },
    {
        text: '提交',
        type: 'submit',
        slotName: 'submit',
        bgColor: aiReviewCompleted.value ? '#1a73e8' : '#cccccc',
        textColor: '#fff',
        disabled: !aiReviewCompleted.value
    }
]);

// 审查记录数据
const reviewRecords = ref([]);


// 调用AI审查接口
const callAiReview = async () => {
    try {
        uni.showLoading({
            title: 'AI审查中...',
            mask: true
        });

        // 根据类型调用不同的AI接口
        if (pageParams.value.type === 'decision' || pageParams.value.type === 'supplemental') {
            const params = {
                // 可以根据需要添加其他参数
                reviewId: pageParams.value.id,
                reviewType: pageParams.value.type.toUpperCase()
            };
            result.value = await contractApi.aiContractOther(params);
        }

        uni.hideLoading();

        // 处理AI审查结果
        if (result.value) {
            aiReviewCompleted.value = true;
            uni.showToast({
                title: 'AI审查完成',
                icon: 'success'
            });
        }
    } catch (error) {
        uni.hideLoading();
        aiReviewCompleted.value = false;
        console.error('AI审查失败:', error);
        uni.showToast({
            title: 'AI审查失败',
            icon: 'none'
        });
    }
};

// 获取页面参数并调用AI审查
onMounted(async () => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    console.log('currentPage.options:', currentPage.options);

    if (currentPage.options) {
        pageParams.value.id = currentPage.options.id;
        pageParams.value.type = currentPage.options.type;

        // 新增：解析传入的详情信息
        if (currentPage.options.detailInfo) {
            try {
                detailInfo.value = JSON.parse(decodeURIComponent(currentPage.options.detailInfo));
            } catch (error) {
                console.error('解析详情信息失败:', error);
            }
        }

        // 如果有合同ID，调用AI审查接口和获取审查记录
        if (pageParams.value.id) {
            // 根据类型决定是否调用AI审查
            if (pageParams.value.type === 'decision' || pageParams.value.type === 'supplemental') {
                await callAiReview();
            } else {
                // 对于其他类型，直接标记为完成
                aiReviewCompleted.value = true;
            }
            await loadReviewRecords();
        }
    }
});

// 加载审查记录
const loadReviewRecords = async () => {
    try {
        const params = {
            page: 0,
            size: 20,
            reviewId: pageParams.value.id,
            reviewType: pageParams.value.type.toUpperCase()
        };
        const result = await contractApi.queryAllOtherReviews(params);
        reviewRecords.value = result.content
    } catch (error) {
        console.error('加载审查记录失败:', error);
        uni.showToast({
            title: '加载审查记录失败',
            icon: 'none'
        });
    }
};

// 显示审查弹窗
const showReviewPopup = () => {
    // 检查AI审查是否完成（仅对需要AI审查的类型进行检查）
    if ((pageParams.value.type === 'decision' || pageParams.value.type === 'supplemental') &&
        (!aiReviewCompleted.value || !result.value)) {
        uni.showToast({
            title: 'AI审查未完成，无法提交',
            icon: 'none'
        });
        return;
    }
    reviewOpinion.value = '';
    reviewPopup.value.open();
};

// 处理审查结果
const handleReview = async (conclusion) => {
    // 统一参数格式：将 boolean 转换为数字
    const conclusionValue = typeof conclusion === 'boolean' ? (conclusion ? 1 : 2) : conclusion;

    // 如果是不通过且没有输入意见，提示用户
    if (conclusionValue === 2 && !reviewOpinion.value.trim()) {
        uni.showToast({
            title: '不通过时必须输入意见',
            icon: 'none'
        });
        return;
    }

    try {
        uni.showLoading({
            title: '提交中...',
            mask: true
        });

        const processTypeMap = {
            'contract': 'CONTRACT',
            'decision': 'DECISION',
            'supplemental': 'SUPPLEMENTAL'
        };

        // 根据类型选择不同的接口和参数格式
        if (pageParams.value.type === 'decision' || pageParams.value.type === 'supplemental') {
            // 对于 DECISION 和 SUPPLEMENTAL 类型，使用 otherMessageReview 接口
            const params = {
                objectId: +pageParams.value.id,
                reviewType: processTypeMap[pageParams.value.type],
                comment: reviewOpinion.value.trim(),
                isApproved: conclusionValue === 1
            };
            await contractApi.otherMessageReview(params);
        } else {
            // 对于其他类型，使用 contractMessageReview 接口
            const params = {
                contractId: pageParams.value.id,
                content: result.value || detailInfo.value?.explain || '',
                opinion: reviewOpinion.value.trim(),
                conclusion: conclusionValue
            };
            await contractApi.contractMessageReview(params);
        }

        uni.hideLoading();
        reviewPopup.value.close();

        uni.showToast({
            title: conclusionValue === 1 ? '审查通过' : '审查不通过',
            icon: 'success'
        });

        // 可以根据需要跳转或刷新页面
        setTimeout(() => {
            uni.navigateBack(2);
        }, 500);

    } catch (error) {
        uni.hideLoading();
        console.error('提交审查结果失败:', error);
        uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
        });
    }
};

const footerCancel = () => {
    uni.navigateBack();
};

// 原来的footerSubmitReview改为showReviewPopup
// const footerSubmitReview = () => {
//     uni.showToast({
//         title: '已标记完成',
//         icon: 'success'
//     });
// };
</script>

<style lang="scss" scoped>
.compliance-regulations-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #F5F7FA;

    .nav-right {
        display: flex;
        align-items: center;
    }

    .download-text {
        font-size: 24rpx;
        color: #1A73E8;
        margin-left: 8rpx;
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .card {
        background-color: #FFFFFF;
        border-radius: 16rpx;
        margin: 32rpx;
        padding: 32rpx;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .title-section .title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        display: block;
        margin-bottom: 24rpx;
    }

    .status-row {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
    }

    .status-badge {
        background-color: #E6F4FF;
        color: #1A73E8;
        border-radius: 16rpx;
        padding: 8rpx 16rpx;
        font-size: 24rpx;
    }

    .version {
        font-size: 24rpx;
        color: #999999;
        margin-left: 16rpx;
    }

    .publisher {
        font-size: 24rpx;
        color: #999999;
    }

    .tab-bar {
        height: 88rpx;
        background-color: #FFFFFF;
        display: flex;
        margin: 0 32rpx;
    }

    .tab-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .tab-item.active {
        border-bottom: 2rpx solid #1A73E8;
    }

    .tab-text {
        font-size: 28rpx;
        font-weight: bold;
    }

    .tab-item.active .tab-text {
        color: #1A73E8;
    }

    .tab-item:not(.active) .tab-text {
        color: #666666;
    }

    .content-section {
        margin-top: 16rpx;
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
    }

    .tab-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .scroll-container {
        flex: 1;
        width: 100%;
        min-height: 0;
    }

    .content-container {
        padding: 20rpx;
        min-height: 600rpx;
    }

    .content-item {
        padding: 32rpx 0;
        border-bottom: 1px solid #E0E0E0;
    }

    .chapter-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        display: block;
        margin-bottom: 16rpx;
    }

    .content-text {
        font-size: 28rpx;
        color: #333333;
        line-height: 48rpx;
        display: block;
        margin-top: 16rpx;
    }

    .workflow-container {
        padding: 20rpx;
        min-height: 600rpx;
    }

    .workflow-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 30rpx;
        text-align: center;
    }

    .timeline {
        position: relative;
        padding-left: 40rpx;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 20rpx;
        top: 0;
        bottom: 0;
        width: 4rpx;
        background: #e0e0e0;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 40rpx;
        padding-left: 40rpx;
    }

    .timeline-dot {
        position: absolute;
        left: -28rpx;
        top: 10rpx;
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: #e0e0e0;
        border: 4rpx solid #fff;
        box-shadow: 0 0 0 2rpx #e0e0e0;
    }

    .timeline-dot.active {
        background: #007aff;
        box-shadow: 0 0 0 2rpx #007aff;
    }

    .timeline-content {
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 24rpx;
        border-left: 4rpx solid #007aff;
    }

    .approval-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
    }

    .approval-step {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
    }

    .approval-status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        color: #fff;
    }

    .approval-status.completed {
        background: #34c759;
    }

    .approval-description {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 12rpx;
    }

    .approval-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12rpx;
    }

    .approval-approver,
    .approval-time {
        font-size: 24rpx;
        color: #999;
    }

    .approval-comment {
        background: #fff;
        border-radius: 8rpx;
        padding: 16rpx;
        margin-top: 12rpx;
    }

    .comment-label {
        font-size: 24rpx;
        color: #666;
        font-weight: bold;
    }

    .comment-text {
        font-size: 26rpx;
        color: #333;
        line-height: 1.5;
        margin-top: 8rpx;
        display: block;
    }

    .report-file {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 32rpx;
        margin-top: 32rpx;
        border-top: 2rpx solid #F0F0F0;
    }

    .file-text {
        font-size: 28rpx;
        color: #1A73E8;
    }
}


// 新增弹窗样式
.review-popup {
    width: 600rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.popup-content {
    padding: 32rpx;
    background: #fafafa;
}

.popup-buttons {
    display: flex;
    padding: 24rpx 32rpx 32rpx;
    gap: 16rpx;
    background: #fafafa;
}

.opinion-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 16rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    font-size: 28rpx;
    line-height: 1.5;
    resize: none;
    box-sizing: border-box;
}

.char-count {
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
}

.uv-btn {
    flex: 1;
}
</style>
