<template>
	<view class="list-responsibility-container">
		<z-paging :auto="false" ref="paging" v-model="dataList" @query="queryList" :loading="loading">
			<template #top>
				<header-bar title="问题调查任务" shape="circle" prefixIcon="search" clearable :fixed="false"
					v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click.stop="searchClick">
								<!-- <uni-icons type="search" size="20" /> -->
								<text class="btn-sou">搜索</text>
							</view>
							<view class="nav-btn" v-if="!userStore.lawyer"
								@click="$tools.routeJump(`/pages/component/continuousOpt/surveyTask/addSurveyTask?type=add`)">
								<text class="btn-text">新增</text>
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags v-model="tagValue" valueKey="value" :fixed="false" :tags="tags"
					@change="handleTagChange" />
			</template>
			<view>
				<view class="content-container-list">
					<view v-for="(item, index) in dataList" :key="index" class="list-item" @click="handleItemClick(item)">
						<view class="item-content">
							<text class="item-title">{{ item.title }}</text>
							<view class="item-info">
								<text class="info-text" :data-status="item.status">
									{{ getStatusName(item.status) }}
								</text>
								<text class="info-text" :data-level="item.level" style="margin-left: 20rpx;">
									{{ getLevelName(item.level) }}
								</text>
								<text class="info-tig" style="margin-left: 20rpx;">{{ getTypeName(item.investigateType) }}</text>
								<text class="info-tig" style="margin-left: 20rpx;">{{ getSourceName(item.investigateSource) }}</text>
							</view>
							<view class="item-info">
								<text class="info-tig">开始时间：{{ item.startDate }}</text>
								<text class="info-tig" style="margin-left: 20rpx;">完成时间：{{ item.finishDate }}</text>
							</view>
							<view class="item-info">
								<text class="info-tig">负责人ID：{{ item.dutyEmployeeId }}</text>
								<text class="info-tig" style="margin-right: 20rpx;">协调人ID：{{ item.coordinateEmployeeId }}</text>
							</view>
						</view>
						<uni-icons type="right" size="16" color="#999" />
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
	import {onShow} from '@dcloudio/uni-app';
	import headerBar from '@/components/headerBar.vue'
	import filterTags from '@/components/filterTags.vue'
	import {ref,nextTick} from 'vue';
	import taskApi from '@/api/violation/task.js'
	import {
		useUserStore
	} from '@/store/pinia.js';
	const userStore = useUserStore();

	const tagValue = ref(null)
	const form = ref({})
	
	// 状态映射
	const statusMap = ref({
		"NO_START": '未开始',
		"PROGRESSING": '进行中',
		"FINISHED": '已完成',
		"PAUSED": '已暂停',
		"CANCELED": '已取消'
	});
	
	// 优先级映射
	const levelMap = ref({
		"LOW": '低风险',
		"MIDDLE": '中风险',
		"HIGH": '高风险'
	});
	
	// 调查类型映射
	const typeMap = ref({
		"ADVERTISING_COMPLIANCE": '广告合规',
		"SUPPLIER_MANAGEMENT": '供应商管理',
		"EMPLOYEE_TRAINING": '员工培训',
		"FINANCIAL_AUDITING": '财务审计'
	});
	
	// 调查来源映射
	const sourceMap = ref({
		"MARKETING": '市场部',
		"PROCUREMENT": '采购部',
		"HR": '人力资源部',
		"FINANCE": '财务部'
	});

	function getStatusName(status) {
		return statusMap.value[status] || status;
	}
	
	function getLevelName(level) {
		return levelMap.value[level] || level;
	}
	
	function getTypeName(type) {
		return typeMap.value[type] || type;
	}
	
	function getSourceName(source) {
		return sourceMap.value[source] || source;
	}
	
	const tags = ref([{
			name: '全部',
			value: null
		}, {
			name: '未开始',
			value: 'NO_START'
		},
		{
			name: '进行中',
			value: 'PROGRESSING'
		},
		{
			name: '已完成',
			value: 'FINISHED'
		},
		{
			name: '已暂停',
			value: 'PAUSED'
		},
		{
			name: '已取消',
			value: 'CANCELED'
		}
	]);

	onShow(() => {
		reload();
	});
	
	const handleTagChange = () => {
		paging.value.reload();
	}

	const handleItemClick = (item) => {
		// 点击调查任务项逻辑 - 跳转到详情页面
		console.log('点击调查任务项:', item);
		uni.navigateTo({
			url: `/pages/component/continuousOpt/surveyTask/surveyDetail?type=detail&id=${item.id}`
		})
	};

	const searchValue = ref("")
	const paging = ref(null)
	const dataList = ref([])
	const loading = ref(false)

	function reload() {
		nextTick(() => {
			paging?.value.reload();
		})
	}

	function searchClick() {
		reload()
	}
	
	// 搜索方法
	const search = () => {
		reload()
	}

	// 列表查询
	const queryList = (pageNo, pageSize) => {
		loading.value = true;
		var params = {
			page: pageNo - 1,
			size: pageSize,
			status: tagValue.value,
			title: searchValue.value
		}

		taskApi.getInvestigateTaskList(params).then((res) => {
			paging.value.complete(res.content || res);
			loading.value = false;
		}).catch((err) => {
			paging.value.complete(false);
			loading.value = false;
		})
	}
</script>

<style lang="scss" scoped>
	@import '/static/css/nav.scss';

	.list-responsibility-container {
		display: flex;
		flex-direction: column;
		min-height: 100%;
		background-color: #F5F7FA;

		.content-container {
			flex: 1;
		}

		.content-container-list {
			padding: 20rpx 32rpx;
		}

		.list-item {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			margin-bottom: 16rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		}

		.item-content {
			flex: 1;
		}

		.item-title {
			font-size: 32rpx;
			font-weight: normal;
			color: #1a1a1a;
			margin-bottom: 8rpx;
		}

		.item-margin {
			margin-left: 20px;
		}

		.item-info {
			display: flex;
			margin-top: 10rpx;
			align-items: center;
		}

		.info-text {
			display: inline-block;
			padding: 6rpx 16rpx;
			color: white;
			font-weight: normal;
			border-radius: 6rpx;
			text-align: center;
			font-size: 24rpx;
		}

		/* 状态样式 */
		.info-text[data-status="NO_START"] {
			background-color: rgb(245, 245, 245);
			color: #666;
		}

		.info-text[data-status="PROGRESSING"] {
			background-color: rgb(230, 247, 255);
			color: #1890ff;
		}

		.info-text[data-status="FINISHED"] {
			background-color: rgb(246, 255, 237);
			color: #52c41a;
		}

		.info-text[data-status="PAUSED"] {
			background-color: rgb(255, 247, 230);
			color: #fa8c16;
		}

		.info-text[data-status="CANCELED"] {
			background-color: rgb(255, 242, 240);
			color: #ff4d4f;
		}

		/* 高优先级样式 - 红色背景 */
		.info-text[data-level="HIGH"] {
			background-color: rgb(255, 234, 234);
			color: #ff7875;
		}

		/* 中优先级样式 - 橙色背景 */
		.info-text[data-level="MIDDLE"] {
			background-color: rgb(255, 244, 229);
			color: #ffc069;
		}

		/* 低优先级样式 - 绿色背景 */
		.info-text[data-level="LOW"] {
			background-color: rgb(230, 255, 250);
			color: rgb(49, 151, 149);
		}

		.info-dot {
			font-size: 24rpx;
			color: #666;
			margin: 0 8rpx;
		}

		.info-tig {
			font-size: 24rpx;
			color: #666;
		}
	}
</style>