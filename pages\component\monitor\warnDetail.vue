<template>
    <view class="warn-detail-container">
        <!-- 抽屉容器 -->
        <view class="drawer-container">
            <!-- 拖拽柄 -->
            <!-- <view class="drag-handle-container">
                <view class="drag-handle"></view>
            </view> -->

            <!-- 标题区 -->

            <!-- 内容区 -->
            <view class="content">
                <!-- 基本信息卡 -->
                <view class="info-card">
                    <view class="info-header">
                        <view class="warning-icon">
                            <uni-icons type="info" size="24" color="#1A73E8"></uni-icons>
                        </view>
                        <view class="info-title">
                            <text class="warning-title">付款延迟过久</text>
                            <view class="tag-container">
                                <text class="danger-tag">高危</text>
                                <text class="time">2025-04-19 10:30</text>
                            </view>
                        </view>
                        <text class="category-tag">财务类</text>
                    </view>

                    <view class="info-grid">
                        <view class="grid-item">
                            <text class="grid-label">来源</text>
                            <text class="grid-value">合规驾驶舱</text>
                        </view>
                        <view class="grid-item">
                            <text class="grid-label">状态</text>
                            <text class="grid-value">未读</text>
                        </view>
                        <view class="grid-item">
                            <text class="grid-label">优先级</text>
                            <text class="grid-value">P1</text>
                        </view>
                    </view>
                </view>

                <!-- 预警描述 -->
                <view class="description-card">
                    <view class="description-header">
                        <view class="description-icon">
                            <uni-icons type="info" size="16" color="#1A73E8"></uni-icons>
                        </view>
                        <text class="description-title">预警详情</text>
                        <text class="id-tag">ID: WN2025041901</text>
                    </view>

                    <view class="description-content">
                        <view class="description-item">
                            <text class="bullet">•</text>
                            <text class="description-text">
                                合同编号 <text class="highlight">CT20250418001</text> 付款流程已延迟 <text
                                    class="danger-highlight">7 个工作日</text>
                            </text>
                        </view>
                        <view class="description-item">
                            <text class="bullet">•</text>
                            <text class="description-text">
                                涉及金额 <text class="highlight">1,250,000 元</text>，付款方为 <text
                                    class="highlight">上海某科技有限公司</text>
                            </text>
                        </view>
                        <view class="description-item">
                            <text class="bullet">•</text>
                            <text class="description-text">
                                根据财务制度，超过 <text class="highlight">5 个工作日</text> 需上报财务总监
                            </text>
                        </view>
                        <view class="description-item">
                            <text class="bullet">•</text>
                            <text class="description-text">
                                业务负责人：<text class="highlight">张明 13800138000</text>
                            </text>
                        </view>
                        <view class="description-item">
                            <text class="bullet">•</text>
                            <text class="description-text">
                                需在 <text class="danger-highlight">24 小时内</text> 提交书面说明
                            </text>
                        </view>
                    </view>
                </view>

                <!-- 关联项 -->
                <view class="related-card">
                    <view class="related-header">
                        <view class="related-icon">
                            <uni-icons type="link" size="16" color="#1A73E8"></uni-icons>
                        </view>
                        <text class="related-title">关联项</text>
                    </view>

                    <view class="related-list">
                        <view class="related-item" @click="handleRelatedItem(1)">
                            <view class="item-left">
                                <view class="item-icon contract-icon">
                                    <uni-icons type="document" size="16" color="#1A73E8"></uni-icons>
                                </view>
                                <view class="item-info">
                                    <text class="item-title">合同审查 #12345</text>
                                    <text class="item-time">2025-04-15</text>
                                </view>
                            </view>
                            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
                        </view>

                        <view class="related-item" @click="handleRelatedItem(2)">
                            <view class="item-left">
                                <view class="item-icon risk-icon">
                                    <uni-icons type="shield" size="16" color="#FF6D00"></uni-icons>
                                </view>
                                <view class="item-info">
                                    <text class="item-title">风险识别 #54321</text>
                                    <text class="item-time">2025-04-10</text>
                                </view>
                            </view>
                            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
                        </view>
                        <go-navitor url="/pages/component/monitor/report" />
                    </view>
                </view>
            </view>

            <!-- 操作区 -->
            <footer-bar :buttons="buttons" @cancel="handleClose" @draft="handlePreview">
                <template #preview="{ btn }">
                    <uni-icons type="eye" size="14" />
                    {{ btn.text }}
                </template>
            </footer-bar>
        </view>
    </view>
</template>

<script setup>
import goNavitor from '@/components/goNavitor.vue';
import { ref } from 'vue';
import footerBar from '@/components/footerBar.vue';

const buttons = ref([
    {
        text: '关闭',
        type: 'cancel',
        slotName: 'cancel',
        bgColor: '#f5f5f5',
        textColor: '#666'
    },
    {
        text: '标记已读',
        type: 'draft',
        slotName: 'draft',
        bgColor: '#fff',
        textColor: '#1a73e8',
        border: '1px solid #1a73e8'
    },
    {
        text: '删除',
        type: 'submit',
        slotName: 'submit',
        bgColor: '#1a73e8',
        textColor: '#fff'
    }
])

const handlePreview = () => {
    console.log('预览');
}

const handleBack = () => {
    uni.navigateBack();
};

const handleMarkRead = () => {
    uni.showToast({
        title: '已标记为已读',
        icon: 'success'
    });
};

const handleClose = () => {
    buttons.value[1].text = '已标记';
};

const handleDelete = () => {
    uni.showModal({
        title: '删除预警',
        content: '确定要删除此预警吗？',
        success: (res) => {
            if (res.confirm) {
                uni.showToast({
                    title: '删除成功',
                    icon: 'success'
                });
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            }
        }
    });
};

const handleRelatedItem = (id) => {
    uni.navigateTo({
        url: `/pages/related/detail?id=${id}`
    });
};
</script>

<style lang="scss" scoped>
.warn-detail-container {
    height: auto;
    background-color: #f8f8f8;

    .drawer-container {
        position: relative;
        height: 100%;
        background-color: #fff;
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;
        overflow: hidden;
    }

    .drag-handle-container {
        display: flex;
        justify-content: center;
        padding-top: 12rpx;
    }

    .drag-handle {
        width: 64rpx;
        height: 8rpx;
        border-radius: 4rpx;
        background-color: #ccc;
    }

    .content {
        // height: calc(100% - 240rpx);
        // padding: 0 30rpx;
        padding-bottom: 88rpx;
    }

    .info-card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    }

    .info-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30rpx;
    }

    .warning-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 16rpx;
        background-color: rgba(26, 115, 232, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
    }

    .info-title {
        flex: 1;
    }

    .warning-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
    }

    .tag-container {
        display: flex;
        align-items: center;
    }

    .danger-tag {
        font-size: 24rpx;
        color: #E53E3E;
        background-color: rgba(229, 62, 62, 0.1);
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
        margin-right: 16rpx;
    }

    .time {
        font-size: 24rpx;
        color: #999;
    }

    .category-tag {
        font-size: 24rpx;
        color: #666;
        background-color: #f5f5f5;
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
    }

    .info-grid {
        display: flex;
        justify-content: space-between;
        margin-top: 30rpx;
    }

    .grid-item {
        flex: 1;
        background-color: #f9f9f9;
        border-radius: 16rpx;
        padding: 20rpx;
        margin-right: 20rpx;
    }

    .grid-item:last-child {
        margin-right: 0;
    }

    .grid-label {
        font-size: 24rpx;
        color: #999;
        display: block;
        margin-bottom: 10rpx;
    }

    .grid-value {
        font-size: 28rpx;
        color: #333;
        display: block;
    }

    .description-card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    }

    .description-header {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
    }

    .description-icon {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        background-color: rgba(26, 115, 232, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
    }

    .description-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        flex: 1;
    }

    .id-tag {
        font-size: 24rpx;
        color: #666;
        background-color: #f5f5f5;
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
    }

    .description-content {
        margin-left: 10rpx;
    }

    .description-item {
        display: flex;
        margin-bottom: 20rpx;
    }

    .bullet {
        color: #1A73E8;
        margin-right: 16rpx;
        font-size: 32rpx;
        line-height: 40rpx;
    }

    .description-text {
        font-size: 28rpx;
        color: #333;
        line-height: 40rpx;
        flex: 1;
    }

    .highlight {
        font-weight: 500;
        color: #333;
    }

    .danger-highlight {
        font-weight: 500;
        color: #E53E3E;
    }

    .related-card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 30rpx;
    }

    .related-header {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }

    .related-icon {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        background-color: rgba(26, 115, 232, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
    }

    .related-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        flex: 1;
    }

    .related-list {
        margin-top: 20rpx;
    }

    .related-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        background-color: #f9f9f9;
        border-radius: 12rpx;
        margin-bottom: 16rpx;
    }

    .item-left {
        display: flex;
        align-items: center;
    }

    .item-icon {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
    }

    .contract-icon {
        background-color: rgba(26, 115, 232, 0.1);
    }

    .risk-icon {
        background-color: rgba(255, 109, 0, 0.1);
    }

    .item-info {
        flex: 1;
    }

    .item-title {
        font-size: 28rpx;
        color: #333;
        display: block;
    }

    .item-time {
        font-size: 24rpx;
        color: #999;
        display: block;
        margin-top: 6rpx;
    }
}
</style>