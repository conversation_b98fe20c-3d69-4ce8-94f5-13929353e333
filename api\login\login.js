import myHttp from '@/utils/request'
function request(url, params, method) {
	return myHttp({
		url: url,
		method: method ? method : 'POST',
		data: params
	})
}
import {
	config
} from '@/config'

//登录相关api
const login = {
		// 微信解绑
	unbindWechat(params) {
        return request(`/services/whiskerguardorgservice/api/employees/unbind/openid?openId=${params.openId}`, {}, 'put')
	},
	//获取openid
   code2session(params){
	 return request(
			`/services/whiskerguardgeneralservice/api/wechat/miniapp/auth/code2session?code=${params.code}`,
			{}, 'get')
   },
	// 小程序-微信登录
	xcx_appletLogin(params) {
		return myHttp({
			url: `/services/whiskerguardauthservice/api/sessions/login`,
			method: 'post',
			data: params,
			isToken: true,
		})
	},
	// 根据员工获取承诺书（检查是否已签约）
	checkEmployeeCommitment() {
		return request(
			`/services/whiskerguardorgservice/api/letter/commitments/employee`,
			{}, 'get')
	},
	// 创建新的承诺书记录
	createCommitment(params) {
		return request(
			`/services/whiskerguardorgservice/api/letter/commitments`,
			params, 'post')
	},
	logout(token) {
		return request(
			`/services/whiskerguardauthservice/api/auth/logout?token=${token}`,
			{}, 'POST')
	},
	// 发送验证码
	sendCode(params) {
		return new Promise((resolve, reject) => {
			uni.request({
				url: `${config.baseUrl}/services/whiskerguardgeneralservice/api/verification/code/send`,
				method: 'POST',
				data: params,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					if (res.statusCode === 200) {
						resolve(res.data);
					} else {
						reject(res);
					}
				},
				fail: (err) => {
					reject(err);
				}
			});
		});
	},
	// 修改密码
	changePwd(params) {
		return request(
			`/services/whiskerguardorgservice/api/employees/password/reset`,
			params, 'put')
	},

	// 小程序-获取手机号
	// xcx_GetPhone(params) {
	// 	return myHttp({
	// 		url: `/login/GetPhone`,
	// 		method: 'post',
	// 		data: params
	// 	})
	// },
	// 小程序 - 用户信息
	// xcx_userInfo(params) {
	// 	return myHttp({
	// 		url: `/AppletUser/info`,
	// 		method: 'post',
	// 		data: params,
	// 		isToken: true,
	// 	})
	// },
	// 小程序 - 用户信息-修改
	// xcx_userEdit(params) {
	// 	return myHttp({
	// 		url: `/AppletUser/edit`,
	// 		method: 'post',
	// 		data: params
	// 	})
	// },
}
export default login