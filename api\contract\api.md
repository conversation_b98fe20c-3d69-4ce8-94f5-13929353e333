---
title: 猫伯伯智能合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯智能合规管家

Base URLs:

# Authentication

# 08-合同管理服务/合规审查管理

## POST 创建合规审查记录

POST /whiskerguardcontractservice/api/compliance/reviews

描述：创建一个新的合规审查记录。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "objectId": 0,
  "reviewType": "CONTRACT",
  "auditedOpinion": "string",
  "auditedConclusion": 0,
  "handleSuggestion": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "complianceReviewList": [
    {
      "id": 0,
      "reviewId": 0,
      "itemId": 0,
      "isCompliant": 0,
      "remark": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "itemName": "string"
    }
  ],
  "complianceReviewAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewId": 0,
      "regulationId": 0,
      "regulationName": "string",
      "regulationTerm": "string",
      "regulationExplain": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|body|body|[ComplianceReviewDTO](#schemacompliancereviewdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewId": 0,
  "reviewType": "",
  "auditedOpinion": "",
  "auditedConclusion": 0,
  "handleSuggestion": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "complianceReviewList": [
    {
      "id": 0,
      "reviewId": 0,
      "itemId": 0,
      "isCompliant": 0,
      "remark": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "complianceReviewAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewId": 0,
      "regulationId": 0,
      "regulationName": "",
      "regulationTerm": "",
      "regulationExplain": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceReviewDTO](#schemaresponseentitycompliancereviewdto)|

# 数据模型

<h2 id="tocS_ComplianceReviewAccordingDTO">ComplianceReviewAccordingDTO</h2>

<a id="schemacompliancereviewaccordingdto"></a>
<a id="schema_ComplianceReviewAccordingDTO"></a>
<a id="tocScompliancereviewaccordingdto"></a>
<a id="tocscompliancereviewaccordingdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewId": 0,
  "regulationId": 0,
  "regulationName": "string",
  "regulationTerm": "string",
  "regulationExplain": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewId|integer(int64)|true|none||审查对象ID|
|regulationId|integer(int64)|true|none||法规ID|
|regulationName|string|false|none||法规名称|
|regulationTerm|string|false|none||法规具体条款|
|regulationExplain|string|false|none||法规适用说明|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ComplianceReviewListDTO">ComplianceReviewListDTO</h2>

<a id="schemacompliancereviewlistdto"></a>
<a id="schema_ComplianceReviewListDTO"></a>
<a id="tocScompliancereviewlistdto"></a>
<a id="tocscompliancereviewlistdto"></a>

```json
{
  "id": 0,
  "reviewId": 0,
  "itemId": 0,
  "isCompliant": 0,
  "remark": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "itemName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|reviewId|integer(int64)|true|none||审查对象ID|
|itemId|integer(int64)|true|none||审查项ID|
|isCompliant|integer|false|none||是否合规：0 否 1 是|
|remark|string|false|none||备注|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|itemName|string|false|none||合规审查项|

<h2 id="tocS_ComplianceReviewDTO">ComplianceReviewDTO</h2>

<a id="schemacompliancereviewdto"></a>
<a id="schema_ComplianceReviewDTO"></a>
<a id="tocScompliancereviewdto"></a>
<a id="tocscompliancereviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "objectId": 0,
  "reviewType": "CONTRACT",
  "auditedOpinion": "string",
  "auditedConclusion": 0,
  "handleSuggestion": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "complianceReviewList": [
    {
      "id": 0,
      "reviewId": 0,
      "itemId": 0,
      "isCompliant": 0,
      "remark": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "itemName": "string"
    }
  ],
  "complianceReviewAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewId": 0,
      "regulationId": 0,
      "regulationName": "string",
      "regulationTerm": "string",
      "regulationExplain": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|objectId|integer(int64)|true|none||审查对象ID|
|reviewType|string|false|none||审查对象类型|
|auditedOpinion|string|false|none||审核意见|
|auditedConclusion|integer|false|none||审核结论：1表示通过 2表示有条件通过 3表示不通过|
|handleSuggestion|string|false|none||处理建议|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|complianceReviewList|[[ComplianceReviewListDTO](#schemacompliancereviewlistdto)]|false|none||合规审查项明细|
|complianceReviewAccordingList|[[ComplianceReviewAccordingDTO](#schemacompliancereviewaccordingdto)]|false|none||合规审查法规依据明细|

#### 枚举值

|属性|值|
|---|---|
|reviewType|CONTRACT|
|reviewType|DECISION|
|reviewType|SUPPLEMENTAL|
|reviewType|VIOLATION|
|reviewType|INVESTIGATE_TASK|
|reviewType|INVESTIGATE_RECORD|
|reviewType|INVESTIGATE_REPORT|
|reviewType|RESPONSIBILITY_CORRECTION|
|reviewType|RESPONSIBILITY_DEAL|
|reviewType|CONTINUOUS_EXPERIENCE|
|reviewType|CONTINUOUS_IMPROVE|
|reviewType|CONTINUOUS_REPORT|

<h2 id="tocS_ResponseEntityComplianceReviewDTO">ResponseEntityComplianceReviewDTO</h2>

<a id="schemaresponseentitycompliancereviewdto"></a>
<a id="schema_ResponseEntityComplianceReviewDTO"></a>
<a id="tocSresponseentitycompliancereviewdto"></a>
<a id="tocsresponseentitycompliancereviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "objectId": 0,
  "reviewType": "CONTRACT",
  "auditedOpinion": "string",
  "auditedConclusion": 0,
  "handleSuggestion": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "complianceReviewList": [
    {
      "id": 0,
      "reviewId": 0,
      "itemId": 0,
      "isCompliant": 0,
      "remark": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "itemName": "string"
    }
  ],
  "complianceReviewAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewId": 0,
      "regulationId": 0,
      "regulationName": "string",
      "regulationTerm": "string",
      "regulationExplain": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|objectId|integer(int64)|true|none||审查对象ID|
|reviewType|string|false|none||审查对象类型|
|auditedOpinion|string|false|none||审核意见|
|auditedConclusion|integer|false|none||审核结论：1表示通过 2表示有条件通过 3表示不通过|
|handleSuggestion|string|false|none||处理建议|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|complianceReviewList|[[ComplianceReviewListDTO](#schemacompliancereviewlistdto)]|false|none||合规审查项明细|
|complianceReviewAccordingList|[[ComplianceReviewAccordingDTO](#schemacompliancereviewaccordingdto)]|false|none||合规审查法规依据明细|

#### 枚举值

|属性|值|
|---|---|
|reviewType|CONTRACT|
|reviewType|DECISION|
|reviewType|SUPPLEMENTAL|

