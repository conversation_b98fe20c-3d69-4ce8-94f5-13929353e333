{"_from": "delayed-stream@~1.0.0", "_id": "delayed-stream@1.0.0", "_inBundle": false, "_integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "_location": "/delayed-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "delayed-stream@~1.0.0", "name": "delayed-stream", "escapedName": "delayed-stream", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/combined-stream"], "_resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "_shasum": "df3ae199acadfb7d440aaae0b29e2272b24ec619", "_spec": "delayed-stream@~1.0.0", "_where": "G:\\项目集合\\家客云_h5\\node_modules\\combined-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "bugs": {"url": "https://github.com/felixge/node-delayed-stream/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "Buffers events from a stream until you are ready to handle them.", "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/felixge/node-delayed-stream", "license": "MIT", "main": "./lib/delayed_stream", "name": "delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "scripts": {"test": "make test"}, "version": "1.0.0"}