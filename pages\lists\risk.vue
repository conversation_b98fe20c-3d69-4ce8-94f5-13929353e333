<template>
	<view class="lists-container">
		<z-paging ref="paging" :auto="false" v-model="dataList" @query="queryList" :loading="loading">
			<template #top>
				<header-bar title="风险识别清单" shape="circle" prefixIcon="search" clearable :fixed="false"
					v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click.stop="searchClick">
								<!-- <uni-icons type="search" size="24" color="#666666"></uni-icons> -->
								<text class="btn-sou">搜索</text>
							</view>
							<view class="nav-btn" v-if="!lawyer" @click="addRisk">
								<text class="btn-text">新增</text>
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags v-model="tagValue" :fixed="false" :tags="tags" valueKey="value"
					@change="handleTagChange" />
			</template>
			<view class="risk-list pl-32 pr-32">
				<view class="list-item" v-for="(item, index) in dataList" :key="index"
					@click="viewDetail(item.complianceRiskMainId)">
					<!-- <view class="item-content">
						<text class="item-title">{{ item.title }}</text>
						<view class="item-meta">
							<view :class="['badge', `badge-${item.level}`]">{{ item.levelText }}</view>
							<text class="meta-divider">·</text>
							<text class="meta-text">{{ item.department }}</text>
							<text class="meta-divider">·</text>
							<text class="meta-date">{{ item.date }}</text>
						</view>
					</view> -->
					<view class="item-content">
						<view class="item-t-box">
					<text class="item-title">{{ getBusinessTypeName(item.businessType) }}</text>
					<text class="info-status" :data-status="item.approvalStatus">{{ getStatusName(item.approvalStatus) }}</text>
				</view>
						<view class="item-info">
							<text class="info-text" :data-risk="item.riskLevelModel">
								{{ item.riskLevelModel === 'HIGH' ? '高危' : item.riskLevelModel === 'MEDIUM' ? '中危' : item.riskLevelModel === 'LOW' ? '低危' : '未知' }}
							</text>
							<!-- <text class="info-dot">·</text>
							<text>岗位：{{ getPositionName(item.postId) || '未设置' }}</text> -->
							<!-- <text class="info-dot">·</text> -->
							<text class="info-tig"
								style="margin-left: 20rpx;"><!-- 部门： -->{{ getDepartmentName(item.orgUnitId) || '未设置' }}</text>
							<!-- <text class="info-dot">·</text> -->
							<text class="info-tig" style="margin-left: 20rpx;">{{ item.createdAt }}</text>
						</view>
					</view>
					<uni-icons type="right" size="16" color="#999"/>
				</view>
			</view>
			<!-- </scroll-view> -->
		</z-paging>

	</view>
</template>

<script setup>
	import {
		ref,
		nextTick,
		onMounted
	} from 'vue';
	import {
		useUserStore
	} from '@/store/pinia.js';
	import headerBar from '@/components/headerBar.vue';
	import filterTags from '@/components/filterTags.vue'
	import homeApi from '@/api/home/<USER>'
	import getDictData from '@/utils/dict.js'
	const loading = ref(false)
	const userStore = useUserStore();
	const lawyer = ref(false);
	lawyer.value = userStore.lawyer
	const tagValue = ref(null);
	// 审批状态映射
	const approvalStatus = ref({
		"DRAFT": '草稿',
		"APPROVED": '审核通过',
		"REJECTED": '审核未通过',
		"PENDING": '审批中'
	});

	// 获取状态名称
	function getStatusName(status) {
		return approvalStatus.value[status] || status;
	}

	const tags = ref([{
		name: '全部',
		value: null
	}, {
		name: '高危',
		value: 'HIGH'
	}, {
		name: '中危',
		value: 'MEDIUM'
	}, {
		name: '低危',
		value: 'LOW'
	}, {
		name: '未知',
		value: 'UNKNOWN'
	}]);

	// 跳转到详情页面
	const viewDetail = (id) => {
		console.log('查看详情:', id);
		// 统一传入detail
		uni.navigateTo({
			url: `/pages/component/lists/riskDetail?type=detail&id=${id}`
		})
		// uni.navigateTo({
		// 	url: `/pages/component/lists/addRisk?type=detail&id=${id}`
		// })
	}

	function addRisk() {
		uni.navigateTo({
			url: '/pages/component/lists/addRisk?type=add'
		})
	}
	const handleTagChange = (index) => {
		reload();
	}

	function reload() {
		nextTick(() => {
			paging?.value.reload();
		})
	}

	const searchValue = ref("") //
	const paging = ref(null)
	const dataList = ref([])

	function searchClick() {
		reload();
	}

	// 存储业务类型字典数据
	const businessTypeDict = ref([]);

	onMounted(async () => {
		reload();
		const data = await getDictData('88', '')
		businessTypeDict.value = data || [];
	});

	// 根据部门ID获取部门名称
	function getDepartmentName(departmentId) {
		if (!departmentId) return '未设置';
		const departments = userStore.getDepartments();
		if (!departments) return departmentId;

		const department = departments.find(dept => dept.id === departmentId);
		return department ? department.name : departmentId;
	}

	// 根据业务类型ID获取业务类型名称
	function getBusinessTypeName(businessType) {
		if (!businessType) return '未设置';
		if (!businessTypeDict.value || businessTypeDict.value.length === 0) return businessType;

		const typeItem = businessTypeDict.value.find(item => item.value === businessType || item.id === businessType);
		return typeItem ? typeItem.name : businessType;
	}

	// 根据岗位ID获取岗位名称
	function getPositionName(positionId) {
		if (!positionId) return '未设置';
		const postList = userStore.getPostList();
		if (!postList) return positionId;

		const position = postList.find(post => post.id === positionId);
		return position ? position.name : positionId;
	}

	// 列表查询
	const queryList = (pageNo, pageSize) => {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
		const userStore = useUserStore();
		var params = {
			page: pageNo - 1,
			size: pageSize,
			riskLevelModel: tagValue.value,
			// tenantId: userStore.tenantId,
			businessTypeName: searchValue.value
		}

		homeApi.complianceRiskMains(params).then((res) => {
			let arr = res.content
			console.log('arr', arr)
			paging.value.complete(res.content);
			uni.hideLoading();
		}).catch((err) => {
			paging.value.complete(false);
			uni.hideLoading();
		})
	}
	const handleItemClick = (item) => {
		console.log('点击风险项:', item);
		// 这里可以添加跳转逻辑
		uni.navigateTo({
			url: `/pages/component/lists/addRisk?type=detail&id=${item.item.complianceRiskMainId}`
		})
	};
	// 暴露变量供其他页面访问
	defineExpose({
		reload
	});
</script>

<style lang="scss" scoped>
	.lists-container {
		// display: flex;
		// flex-direction: column;
		// height: 100%;
		// background-color: #F5F7FA;

		/* Header Styles */
		.nav-right {
			display: flex;
			align-items: center;

			.nav-btn {
				display: flex;
				align-items: center;
				margin-left: 20rpx;
			}

			.btn-text {
				font-size: 26rpx;
				color: #ffffff;
				background-color: #1A73E8;
				padding: 8rpx 14rpx;
				border-radius: 6rpx;
			}
			.btn-sou{
				font-size: 26rpx;
				color: #333333;
			}
		}

		.risk-list {
			display: flex;
			flex-direction: column;
			margin-top: 20rpx;
		}

		.list-item {
			display: flex;
			justify-content: space-between;
			/* 子元素左右排列 */
			align-items: center;
			/* 垂直居中对齐 */
			background-color: white;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
			margin-bottom: 16rpx;
		}

		.item-title {
			font-size: 32rpx;
			font-weight: normal;
			color: #1a1a1a;
			margin-bottom: 8rpx;
		}

		.item-meta {
			display: flex;
			align-items: center;
			font-size: 24rpx;
		}

		.badge {
			display: inline-flex;
			align-items: center;
			padding: 8rpx 16rpx;
			border-radius: 16rpx;
			font-size: 24rpx;
			font-weight: normal;
		}

		.badge-high {
			background-color: #FFEAEA;
			color: #E53E3E;
		}

		.badge-medium {
			background-color: #FFF4E5;
			color: #DD6B20;
		}

		.badge-low {
			background-color: #E6FFFA;
			color: #319795;
		}

		.meta-divider {
			color: #999999;
			margin: 0 8rpx;
		}

		.meta-text {
			color: #999999;
		}

		.meta-date {
			color: #CCCCCC;
		}

		.info-text {
			display: inline-block;
			/* 确保内边距和背景生效 */
			padding: 6rpx 16rpx;
			/* 上下5px 左右8px */
			color: white;
			/* 文字白色 */
			font-weight: normal;
			/* 加粗文字 */
			border-radius: 6rpx;
			/* 可选：轻微圆角使更美观 */
			text-align: center;
			/* 文字居中 */
			font-size: 24rpx;
			/* 统一字体大小 */
		}

		/* 高危样式 - 红色背景 */
		.info-text.high-risk,
		.info-text[data-risk="HIGH"] {
			background-color: rgb(255, 234, 234);
			color: #ff7875;
			/* 标准警告红 */
		}

		/* 中危样式 - 橙色背景 */
		.info-text.medium-risk,
		.info-text[data-risk="MEDIUM"] {
			background-color: rgb(255, 244, 229);
			color: #ffc069;
			/* 标准警告橙 */
		}

		/* 低危样式 - 绿色背景 */
		.info-text.low-risk,
		.info-text[data-risk="LOW"] {
			background-color: rgb(230, 255, 250);
			color: rgb(49, 151, 149);
			/* 标准成功绿 */
		}

		/* 未知风险样式 - 灰色背景 */
		.info-text.unknown-risk,
		.info-text[data-risk="UNKNOWN"] {
			background-color: #f5f5f5;
			color: #8c8c8c;
		}

		.info-dot {
			margin: 0 8px;
		}

		.info-tig {
			font-size: 24rpx;
			color: $uni-text-color-grey;
		}
	}
	.item-t-box {
		display: flex;
		align-items: center;
	}
	
	.info-status {
		margin-left: 20rpx;
		white-space: nowrap;
		padding: 2rpx 8rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
	}
	
	/* 状态颜色样式 */
	.info-status[data-status="DRAFT"] {
		background-color: #f3f7fd;
		color: #777777;
	}
	
	.info-status[data-status="PENDING"] {
		background-color: #fff7e6;
		color: #fa8c16;
	}
	
	.info-status[data-status="APPROVED"] {
		background-color: #f6ffed;
		color: #52c41a;
	}
	
	.info-status[data-status="REJECTED"] {
		background-color: #fff2f0;
		color: #ff4d4f;
	}
</style>