<!-- 聊天记录模式流式输出-类似chatGPT对话演示(vue)，加载更多聊天记录无闪动 -->
<template>
	<view class="content">
		<z-paging ref="paging" v-model="dataList" use-chat-record-mode
			safe-area-inset-bottom bottom-bg-color="#f8f8f8" empty-view-text="有什么可以帮忙的？" @query="queryList"
			@keyboardHeightChange="keyboardHeightChange" @hidedKeyboard="hidedKeyboard">
			<!-- 顶部模型选择器 -->
			<template #top>
				<view class="model-selector">
					<view class="model-label">智能模型选择</view>
					<picker @change="onModelChange" :value="currentModelIndex" :range="modelList" range-key="name">
						<view class="model-picker">
							<text>{{ currentModel.name || '请选择模型' }}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
			</template>

			<!-- for循环渲染聊天记录列表 -->
			<view v-for="(item, index) in dataList" :key="index" style="position: relative;">
				<!-- style="transform: scaleY(-1)"必须写，否则会导致列表倒置 -->
				<!-- 注意不要直接在chat-item组件标签上设置style，因为在微信小程序中是无效的，请包一层view -->
				<view style="transform: scaleY(-1);">
					<chat-item :item="item"></chat-item>
				</view>
			</view>
			<!-- 底部聊天输入框 -->
			<template #bottom>
				<chat-input-bar :disabled="isAnswering" ref="inputBar" @send="doSend" />
			</template>
		</z-paging>
	</view>
</template>

<script>
import https from '@/api/chatAI/index.js';
import threeAI from '@/api/threeAI/threeAI.js'
import uNavbar from '@/components/u-navbar/u-navbar.vue';
import { useUserStore } from '@/store/pinia.js';
export default {
	components: {
		uNavbar
	},
	data() {
		return {
			// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
			dataList: [],
			// 用户发送的提问信息
			askMsg: '',
			// 是否在回答中，回答中不允许用户发言，避免数据错乱
			isAnswering: false,
			// 模型列表
			modelList: [],
			// 当前选中的模型
			currentModel: {
				name: 'Kimi-Chat'
			},
			// 当前模型索引
			currentModelIndex: 0,
			// 当前会话ID
			sessionId: '',
			// AI对话会话ID
			conversationId: null,
			// 用户store
			userStore: useUserStore(),
			// 打字机效果相关
			typewriterTimer: null,
			// markdown渲染防抖定时器
			markdownDebounceTimer: null,
			// 从上一页传递过来的问题标题
			questionFromPrevPage: '',
		}
	},
	computed: {
		// 动态计算paging样式，确保在不同设备下都能正确适配
		// pagingStyle() {
		// 	const systemInfo = uni.getSystemInfoSync();
		// 	const statusBarHeight = systemInfo.statusBarHeight || 0;
		// 	// 模型选择器高度：状态栏高度 + 内容高度(约80rpx) + 边框
		// 	const selectorHeight = statusBarHeight + 40 + 2; // 转换为px
		// 	return {
		// 		'margin-top': selectorHeight + 'px'
		// 	};
		// }
	},
	async onLoad(options) {
		// 接收从上一页传递过来的问题标题参数
		if (options && options.questionTitle) {
			try {
				this.questionFromPrevPage = decodeURIComponent(options.questionTitle);
			} catch (error) {
				console.error('解码问题标题失败:', error);
				this.questionFromPrevPage = options.questionTitle;
			}
		}
		
		// 生成会话ID
		this.sessionId = 'session_' + Date.now();
		// 获取AI对话会话ID
		await this.getConversationId();
		// 加载模型列表
		this.loadModelList();
	},
	methods: {
		backClick() {
			uni.navigateBack({
				delta: 1
			})
		},
		// 加载模型列表
		async loadModelList() {
			try {
				const res = await https.getModelList();
				if (res && res.length > 0) {
					// 过滤出可用的模型
					this.modelList = res.filter(model => model.status === 'AVAILABLE' && model.isModel);
					if (this.modelList.length > 0) {
						// 查找DeepSeek-Coder模型作为默认选项
						const deepseekIndex = this.modelList.findIndex(model => 
							model.name === 'Kimi-Chat'
						);
						
						if (deepseekIndex !== -1) {
							// 如果找到DeepSeek-Coder模型，设置为默认
							this.currentModel = this.modelList[deepseekIndex];
							this.currentModelIndex = deepseekIndex;
							console.log('已设置DeepSeek-Coder为默认模型');
						} else {
							// 如果没有找到DeepSeek-Coder模型，使用第一个模型
							this.currentModel = this.modelList[0];
							this.currentModelIndex = 0;
							console.warn('未找到DeepSeek-Coder模型，使用第一个可用模型:', this.currentModel.name);
						}
					} else {
					console.warn('没有可用的模型');
				}
			} else {
				// 如果没有获取到模型列表，保持默认的DeepSeek-Coder设置
				console.warn('未获取到模型列表，使用默认DeepSeek-Coder模型');
			}
			
			// 如果有从上一页传递过来的问题，预填充到输入框
			if (this.questionFromPrevPage) {
				this.$nextTick(() => {
					setTimeout(() => {
						if (this.$refs.inputBar) {
							this.$refs.inputBar.setInputValue(this.questionFromPrevPage);
							// 清空问题，避免重复设置
							this.questionFromPrevPage = '';
						}
					}, 300);
				});
			}
		} catch (error) {
			console.error('加载模型列表失败:', error);
			// 网络错误时也保持默认的DeepSeek-Coder设置
			uni.showToast({
				title: '加载模型失败，使用默认模型',
				icon: 'none'
			});
			
			// 即使加载失败，也要预填充问题
			if (this.questionFromPrevPage) {
				this.$nextTick(() => {
					setTimeout(() => {
						if (this.$refs.inputBar) {
							this.$refs.inputBar.setInputValue(this.questionFromPrevPage);
							// 清空问题，避免重复设置
							this.questionFromPrevPage = '';
						}
					}, 300);
				});
			}
		}
		},
		// 模型切换
		onModelChange(e) {
			const index = e.detail.value;
			this.currentModelIndex = index;
			this.currentModel = this.modelList[index];
			uni.showToast({
				title: `已切换到${this.currentModel.name}`,
				icon: 'none'
			});
		},
		queryList(pageNo, pageSize) {
			// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
			// 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
			// 请求服务器获取聊天记录分页数据
			const params = {
				pageNo: pageNo - 1,
				pageSize: pageSize,
				// sessionId: this.sessionId,
				employeeId: this.userStore.userId,
				status: "SUCCESS",
			}
			
			// 使用模拟的聊天历史数据
			https.queryChatList(params).then(res => {
				// 将请求的结果数组传递给z-paging
				// 注意：聊天记录需要按时间倒序排列
				const chatList = res || [];
				
				// 按originalRequestId分组，确保问答对在一起
				const groupedChats = {};
				chatList.forEach(item => {
					const requestId = item.originalRequestId;
					if (!groupedChats[requestId]) {
						groupedChats[requestId] = [];
					}
					groupedChats[requestId].push(item);
				});
				
				// 将分组后的数据按时间排序并展平
				const sortedGroups = Object.values(groupedChats)
					.sort((a, b) => new Date(b[0].createdAt) - new Date(a[0].createdAt));
				
				const formattedList = [];
				sortedGroups.forEach(group => {
					// 在每组内部，用户消息在前，AI回复在后
					const sortedGroup = group.sort((a, b) => {
						// 用户消息(isUser: true)排在前面，AI回复(isUser: false)排在后面
						if (a.isUser && !b.isUser) return 1;
						if (!a.isUser && b.isUser) return -1;
						// 如果都是同类型，按时间排序
						return new Date(a.createdAt) - new Date(b.createdAt);
					});
					sortedGroup.forEach(item => {
						formattedList.push({
							time: new Date(item.createdAt).toLocaleString(),
							icon: item.isUser ? '/static/images/user.png' : 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png',
							name: item.isUser ? this.userStore.userDetail.realName : '猫伯伯',
							content: item.content,
							isMe: item.isUser
						});
					});
				});
				
				this.$refs.paging.complete(formattedList);
			}).catch(res => {
				// 如果请求失败写this.$refs.paging.complete(false);
				console.error('获取聊天历史失败:', res);
				this.$refs.paging.complete([]);
			})
		},
		// 监听键盘高度改变
		keyboardHeightChange(res) {
			this.$refs.inputBar.updateKeyboardHeightChange(res);
		},
		// 用户尝试隐藏键盘
		hidedKeyboard() {
			this.$refs.inputBar.hidedKeyboard();
		},
		// 发送新消息
		doSend(msg) {
			if (this.isAnswering) {
				// 如果在回答中，不允许发送新的消息，避免数据错乱
				return;
			}

			// if (!this.currentModel.toolKey) {
			// 	uni.showToast({
			// 		title: '请先选择模型',
			// 		icon: 'none'
			// 	});
			// 	return;
			// }

			this.askMsg = msg;
			const userMessage = {
				time: new Date().toLocaleString(),
				icon: '/static/images/user.png',
				name: this.userStore.userDetail.realName,
				content: msg,
				isMe: true
			};
			this.$refs.paging.addChatRecordData(userMessage);

			// 在用户发送新消息之后，开始回复消息
			this.doAnswer();
		},

		// 获取AI对话会话ID
		async getConversationId() {
			try {
				const sessionRes = await threeAI.getSessionId();
				this.conversationId = sessionRes?.conversationId || null;
				console.log('获取会话ID成功:', this.conversationId);
			} catch (error) {
				console.error('获取会话ID失败:', error);
				// 使用默认会话ID
				this.conversationId = null;
			}
		},
		// 回复消息
	  	doAnswer() {
			// 设置在回复中
			this.isAnswering = true;
			// 立刻添加一个思考中的回复
			const thinkingMessage = {
				time: new Date().toLocaleString(),
				icon: 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png',
				name: '猫伯伯',
				content: '思考中...',
				isMe: false
			};
			this.$refs.paging.addChatRecordData(thinkingMessage);
			
			const params = {
				// toolKey: this.currentModel.name,
				// tenantId: this.userStore.tenantId,
				toolKey: 'kimi',
				prompt: this.askMsg,
				employeeId: this.userStore.userId,
				"streaming": true,
				conversationId: this.conversationId,
				// "metadata": {
				//   "isModel": true
				// }
				// sessionId: this.sessionId
			}

			https.queryChatStream(params).then(res => {
				// 处理服务器返回的流式数据
				let fullAnswerStr = ''; // 完整的回答内容
				let displayedStr = ''; // 当前显示的内容
				let typewriterQueue = []; // 待显示的字符队列
				
				// 打字机效果函数
				const startTypewriter = () => {
					if (this.typewriterTimer) return; // 如果已经在运行，不重复启动
					
					this.typewriterTimer = setInterval(() => {
					if (typewriterQueue.length > 0) {
						// 智能批量处理，适应不同的内容块
						let batchContent = '';
						const batchSize = Math.min(2, typewriterQueue.length); // 每次处理最多2个块
						for (let i = 0; i < batchSize; i++) {
							batchContent += typewriterQueue.shift();
						}
						displayedStr += batchContent;
						
						// 使用防抖机制减少markdown组件的频繁更新
						if (this.markdownDebounceTimer) {
							clearTimeout(this.markdownDebounceTimer);
						}
						this.markdownDebounceTimer = setTimeout(() => {
							this.$nextTick(() => {
								this.dataList[0].content = displayedStr;
							});
						}, 80); // 80毫秒防抖延迟，与组件内部30毫秒防抖协调
					} else if (fullAnswerStr && displayedStr === fullAnswerStr) {
						// 所有内容都显示完毕，立即更新最终内容
						if (this.markdownDebounceTimer) {
							clearTimeout(this.markdownDebounceTimer);
							this.markdownDebounceTimer = null;
						}
						this.$nextTick(() => {
							this.dataList[0].content = displayedStr;
						});
						clearInterval(this.typewriterTimer);
						this.typewriterTimer = null;
						this.isAnswering = false;
					}
				}, 100); // 调整间隔时间到100毫秒，与防抖机制协调
				};
				
				// 处理流式数据
				this.handleStreamData(res, (content, isDone) => {
					if (content) {
						// 将新内容添加到完整答案中
						fullAnswerStr += content;
						// 智能批量添加字符到队列中
						if (content.length <= 5) {
							// 短内容直接添加
							for (const char of content) {
								typewriterQueue.push(char);
							}
						} else {
							// 长内容分块添加，减少渲染压力
							const chunks = content.match(/.{1,3}/g) || [];
							for (const chunk of chunks) {
								typewriterQueue.push(chunk);
							}
						}
						// 启动打字机效果
						startTypewriter();
					}
					
					// 如果流式传输结束，确保所有内容都能显示完
					if (isDone) {
						// 不在这里直接设置isAnswering = false，让打字机效果完成后再设置
					}
				});
			}).catch(err => {
				// 处理错误
				console.error(err);
				this.dataList[0].content = '抱歉，回答出现错误，请重试';
				this.isAnswering = false;
			});
		},
		// 处理流式数据
		handleStreamData(responseData, callback) {
			try {
				// 如果responseData是字符串，按行分割处理
				const lines = typeof responseData === 'string' ? responseData.split('\n') : [responseData];
				
				lines.forEach(line => {
					if (line.trim() && line.startsWith('data:')) {
						try {
							// 提取data:后面的JSON数据
							const jsonStr = line.substring(5).trim();
							const data = JSON.parse(jsonStr);
							
							// 提取content内容
							const content = data.content || '';
							const isDone = data.done || false;
							
							// 调用回调函数
							callback(content, isDone);
						} catch (parseError) {
							console.error('解析流式数据出错:', parseError, line);
						}
					}
				});
			} catch (error) {
				console.error('处理流式数据出错:', error);
				callback('', true); // 出错时结束流式传输
			}
		},
		// 模拟生成流式数据，根据一个已知字符串每150毫秒返回一个字符（备用方法）
		async streamTextAsync(text, callback, interval = 15) {
			for (const char of text) {
				callback(char); // 逐个返回字符
				await new Promise(resolve => setTimeout(resolve, interval)); // 等待
			}
		}
	},
	beforeDestroy() {
		// 清理打字机定时器
		if (this.typewriterTimer) {
			clearInterval(this.typewriterTimer);
			this.typewriterTimer = null;
		}
		// 清理markdown防抖定时器
		if (this.markdownDebounceTimer) {
			clearTimeout(this.markdownDebounceTimer);
			this.markdownDebounceTimer = null;
		}
	}
}
</script>

<style scoped>
.model-selector {
	padding: 20rpx;
	background-color: #fff;
	border-bottom: 1px solid #eee;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.model-label {
	font-size: 30rpx;
	color: #212529;
	font-weight:normal;
	letter-spacing: 0.5rpx;
}

.model-picker {
	padding: 12rpx 24rpx;
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #495057;
	display: flex;
	align-items: center;
	gap: 12rpx;
	min-width: 200rpx;
	transition: all 0.2s ease;
}

.model-picker:active {
	background-color: #e9ecef;
	transform: scale(0.98);
}

.picker-arrow {
	font-size: 20rpx;
	color: #6c757d;
	transition: transform 0.2s ease;
}

.header {
	background-color: red;
	font-size: 20rpx;
	padding: 20rpx;
	color: white;
}

.popup {
	position: absolute;
	top: -20px;
	height: 200rpx;
	width: 400rpx;
	background-color: red;
	z-index: 1000;
}
</style>
