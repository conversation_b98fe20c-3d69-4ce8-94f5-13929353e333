---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/企业内部法规制度收藏

## POST 添加内部法规制度收藏

POST /whiskerguardregulatoryservice/api/enterprise/regulation/collect

描述：创建新的企业内部法规制度收藏信息。

> Body 请求参数

```json
{
  "id": 0,
  "regulationId": 0,
  "regulationTitle": "string",
  "type": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0,
    "epochSecond": 0,
    "nano": 0
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[EnterpriseRegulationCollectDTO](#schemaenterpriseregulationcollectdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "regulationId": 0,
  "regulationTitle": "",
  "type": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseRegulationCollectDTO](#schemaresponseentityenterpriseregulationcollectdto)|

# 数据模型

<h2 id="tocS_ResponseEntityEnterpriseRegulationCollectDTO">ResponseEntityEnterpriseRegulationCollectDTO</h2>

<a id="schemaresponseentityenterpriseregulationcollectdto"></a>
<a id="schema_ResponseEntityEnterpriseRegulationCollectDTO"></a>
<a id="tocSresponseentityenterpriseregulationcollectdto"></a>
<a id="tocsresponseentityenterpriseregulationcollectdto"></a>

```json
{
  "id": 0,
  "regulationId": 0,
  "regulationTitle": "string",
  "type": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0,
    "epochSecond": 0,
    "nano": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|regulationId|integer(int64)|true|none||制度ID|
|regulationTitle|string|false|none||制度标题|
|type|integer|true|none||收藏类型：1、内部制度 2、法规|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0,
  "epochSecond": 0,
  "nano": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|epochSecond|integer(int64)|false|none||Gets the number of seconds from the Java epoch of 1970-01-01T00:00:00Z.<br /><p><br />The epoch second count is a simple incrementing count of seconds where<br />second 0 is 1970-01-01T00:00:00Z.<br />The nanosecond part is returned by{@link #getNano}.|
|nano|integer|false|none||Gets the number of nanoseconds, later along the time-line, from the start<br />of the second.<br /><p><br />The nanosecond-of-second value measures the total number of nanoseconds from<br />the second returned by{@link #getEpochSecond}.|

<h2 id="tocS_EnterpriseRegulationCollectDTO">EnterpriseRegulationCollectDTO</h2>

<a id="schemaenterpriseregulationcollectdto"></a>
<a id="schema_EnterpriseRegulationCollectDTO"></a>
<a id="tocSenterpriseregulationcollectdto"></a>
<a id="tocsenterpriseregulationcollectdto"></a>

```json
{
  "id": 0,
  "regulationId": 0,
  "regulationTitle": "string",
  "type": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0,
    "epochSecond": 0,
    "nano": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|regulationId|integer(int64)|true|none||制度ID|
|regulationTitle|string|false|none||制度标题|
|type|integer|true|none||收藏类型：1、内部制度 2、法规|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|



---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/企业内部法规制度收藏

## DELETE 取消收藏

DELETE /whiskerguardregulatoryservice/api/enterprise/regulation/collect/cancel/{type}/{regulationId}

描述：取消收藏指定的企业内部法规制度。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|path|integer| 是 |收藏类型，不能为空|
|regulationId|path|integer| 是 |企业内部法规制度ID，不能为空|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型


