---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 22-通用服务/通知中心管理

## GET 统计当前用户未读消息数量

GET /whiskerguardgeneralservice/api/notification-center/unread-count

统计当前用户未读消息数量

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 404 Response

```json
{
  "batchId": "",
  "status": "",
  "totalCount": 0,
  "sentCount": 0,
  "successCount": 0,
  "failedCount": 0,
  "startTime": {
    "seconds": 0,
    "nanos": 0
  },
  "completedTime": {
    "seconds": 0,
    "nanos": 0
  },
  "errorMessage": "",
  "progressPercentage": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|none|[ResponseEntityBatchNotificationStatusDTO](#schemaresponseentitybatchnotificationstatusdto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityBatchNotificationStatusDTO">ResponseEntityBatchNotificationStatusDTO</h2>

<a id="schemaresponseentitybatchnotificationstatusdto"></a>
<a id="schema_ResponseEntityBatchNotificationStatusDTO"></a>
<a id="tocSresponseentitybatchnotificationstatusdto"></a>
<a id="tocsresponseentitybatchnotificationstatusdto"></a>

```json
{
  "batchId": "string",
  "status": "string",
  "totalCount": 0,
  "sentCount": 0,
  "successCount": 0,
  "failedCount": 0,
  "startTime": {
    "seconds": 0,
    "nanos": 0
  },
  "completedTime": {
    "seconds": 0,
    "nanos": 0
  },
  "errorMessage": "string",
  "progressPercentage": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|batchId|string|false|none||批次ID|
|status|string|false|none||状态|
|totalCount|integer|false|none||总数量|
|sentCount|integer|false|none||已发送数量|
|successCount|integer|false|none||成功数量|
|failedCount|integer|false|none||失败数量|
|startTime|[Instant](#schemainstant)|false|none||开始时间|
|completedTime|[Instant](#schemainstant)|false|none||完成时间|
|errorMessage|string|false|none||错误信息|
|progressPercentage|number|false|none||进度百分比|

