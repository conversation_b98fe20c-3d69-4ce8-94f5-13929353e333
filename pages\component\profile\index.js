import myHttp from '@/utils/request'
function request(url, params, method) {
	return myHttp({
		url: url,
		method: method ? method : 'POST',
		data: params
	})
}
const baseUrl = '/services/whiskerguardorgservice/api/'
const wechatBaseUrl = '/services/whiskerguardgeneralservice/api/wechat/'
export default { 
    // 修改密码
    updatePwd(id, params) {
        return request(`${baseUrl}employees/${id}/password`, params, 'put')
    },
	// 更换手机号 verifyCode/phone
	updatePhone(params){
        return request(`${baseUrl}employees/phone/change`, params, 'put')
	},
	// 更新邮箱
	updateEmail(userId,params){
        return request(`${baseUrl}employees/${userId}`, params, 'patch')
	},
	// 我的收藏
	queryCollects(params) {
        return request(`/services/whiskerguardregulatoryservice/api/enterprise/regulation/collect`, params, 'get')
	},
	// 微信绑定
	bindWechat(params) {
        return request(`/services/whiskerguardorgservice/api/employees/bind/openid?openId=${params.openId}&unionId=${params.unionId}`, {}, 'put')
	},
	// 微信解绑
	unbindWechat(params) {
        return request(`/services/whiskerguardorgservice/api/employees/unbind/openid?openId=${params.openId}`, {}, 'put')
	},
	// 绑定消息模板
	bindMessageTemplate(params) {
        return request(`/services/whiskerguardgeneralservice/api/user/binding/templates`, params, 'POST')
	},
	// 获取所有用户绑定消息模板
	getUserBindingTemplates() {
        return request(`/services/whiskerguardgeneralservice/api/user/binding/templates`, {}, 'GET')
	},
	// 通过code获取用户信息 比如openid
	getUserInfoByCode(params) {
        return request(`/services/whiskerguardgeneralservice/api/wechat/miniapp/auth/code2session`, params, 'GET')
	},
	// 浏览记录
	viewRecord(params) {
        return request(`/services/whiskerguardregulatoryservice/api/enterprise/regulation/collect/browse/type`, params, 'GET')
	},
}