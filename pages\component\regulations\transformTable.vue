<template>
  <view class="transform-table">
    <view class="transform-table__form">
      <uv-form labelPosition="top" :labelStyle="labelStyle" :model="transformTable" :rules="rules" ref="form">
        <uv-form-item prop="content" label="审核意见" required>
          <view class="content-display">
            <rich-text
              :nodes="formatContent(transformTable.content)"
              class="rich-content"
            ></rich-text>
          </view>
        </uv-form-item>
        <uv-form-item prop="status" label="审核结果" required>
          <filter-tags v-model="transformTable.status" :top="0" :tags="[{name: '通过',id: 'PASS'}, {name: '不通过',id: 'NOPASS'}]" :fixed="false" />
        </uv-form-item>
        <uv-form-item prop="opinion" label="意见" :required="transformTable.status === 'NOPASS'" v-if="transformTable.status === 'NOPASS'">
          <uv-textarea autoHeight v-model="transformTable.opinion" placeholder="请输入不通过的意见"></uv-textarea>
        </uv-form-item>
      </uv-form>
    </view>
    <footer-bar @click="handleClick" :buttons="buttons" />
  </view>
</template>

<script setup>
import footerBar from '@/components/footerBar.vue'
import filterTags from '@/components/filterTags.vue'
import complianceApi from '@/api/compliance/index.js'
import { ref } from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {
		useUserStore
	} from '@/store/pinia.js';

const loading = ref(false)
const submitting = ref(false)
const form = ref(null)
const labelStyle = ref({
  bottom: '30rpx',
  width: '200rpx'
})

const buttons = ref([
  {
    text: '提交',
    type: 'submit',
    slotName: 'submit',
    bgColor: '#1890ff',
    textColor: '#fff',
  }
])

const userStore = useUserStore();
const transformTable = ref({
  content: '', // 审核意见
  status: '', // 审核结果
  opinion: '', // 意见
  regulationId: '', // 制度ID
  // tenantId: userStore.tenantId
})

const rules = ref({
  content: [
    { required: true, message: '审核意见不能为空', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择审核结果', trigger: 'blur' }
  ],
  opinion: [
    { 
      validator: (rule, value, callback) => {
        if (transformTable.value.status === 'NOPASS' && !value) {
          callback(new Error('请输入不通过的意见'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
})

onLoad(async (options) => {
  console.log('onLoad执行了，参数:', options)
  try {
    const obj = JSON.parse(decodeURIComponent(options.obj))
    console.log('解析后的对象:', obj)
    transformTable.value.regulationId = obj.id
    // 调用AI审查接口
    await loadAiReview(obj.id)
  } catch (error) {
    console.error('参数解析失败:', error)
    uni.showToast({
      title: '参数错误',
      icon: 'error'
    })
  }
})

// 加载AI审查结果
const loadAiReview = async (id) => {
  try {
    loading.value = true
    uni.showLoading({
      title: 'AI审查中...',
      mask: true
    })
    
    const response = await complianceApi.aiReview(id)
    // 将AI审查结果直接赋值给contents
    transformTable.value.content = response || '暂无审查审核意见'

    uni.hideLoading()
  } catch (error) {
    console.error('AI审查失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: 'AI审查失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 处理提交
async function handleSubmit() {
  // 防抖：如果正在提交，直接返回
  if (submitting.value) {
    return;
  }
  
  try {
    // 设置提交状态，禁用按钮
    submitting.value = true;
    
    uni.showLoading({
      title: '提交中...',
      mask: true
    });
    
    // 构造提交参数
    const params = {
      regulationId: transformTable.value.regulationId,
      status: transformTable.value.status,
      opinion: transformTable.value.opinion || '',
      contents: [
        {
          regulationId: transformTable.value.regulationId,
          content: transformTable.value.content
        }
      ]
    };
    
    console.log('提交参数:', params);
    
    // 调用审核接口
    await complianceApi.regulationReview(params);
    
    uni.hideLoading();
    uni.showToast({
      title: '提交成功',
      icon: 'success',
      duration: 1000
    });
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack({
        delta: 1
      });
      // submitting.value = false;
    }, 1000);
    
  } catch (error) {
    submitting.value = false;
    console.error('提交失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '提交失败',
      icon: 'error'
    });
  }
}

// 格式化审核意见为rich-text可识别的格式
const formatContent = (content) => {
  if (!content) return '';

  // 将换行符转换为<br>标签，并保持文本格式
  const formattedContent = content
    .replace(/\n/g, '<br/>')
    .replace(/\r/g, '')
    .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;'); // 制表符转换为空格

  return formattedContent;
}

function handleClick(buttonInfo) {
  form.value.validate().then(() => { 
    handleSubmit()
  }).catch(()=>{
    console.log('校验失败', transformTable.value)
  })
}
</script>

<style lang="scss" scoped>
  .transform-table {
    box-sizing: border-box;
    padding: 20rpx;
  }

  .content-display {
    border: 1rpx solid #e4e7ed;
    border-radius: 8rpx;
    background-color: #f8f9fa;
    min-height: 200rpx;
    padding: 20rpx;

    .rich-content {
      line-height: 1.6;
      font-size: 28rpx;
      color: #333;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }
</style>