<template>
    <view class="compliance-regulations-container">
        
        <!-- Main Content -->
        <view class="main-content">
            <!-- Report Info Card -->
            <view class="card title-section">
                <text class="title">{{ detailInfo?.name || '' }}</text>
                <view class="status-row">
                    <text class="version">审查类型：{{ detailInfo?.contractType || '' }}</text>
                    <view class="status-badge" :class="getStatusClass(detailInfo?.status)">{{ detailInfo?.status || '' }}</view>
                </view>
                <text class="publisher">
                    发起单位：{{ detailInfo?.createdBy || '' }} · 
                    审核人：{{ detailInfo?.auditBy || '' }} · 
                    申请时间：{{ detailInfo?.createdAt  }}
                </text>
            </view>

            <!-- Tab Navigation -->
            <view class="tab-bar">
                <view @click="active = index" class="tab-item" :class="{ active: active === index }"
                    v-for="(tab, index) in tabs" :key="index">
                    <text class="tab-text">{{ tab }}</text>
                </view>
            </view>
            
            <!-- Content Section -->
            <view class="card content-section">
                <!-- 审查结果 -->
                <view v-if="active === 0" class="tab-content">
                    <scroll-view 
                        class="scroll-container"
                        scroll-y="true"
                    >
                        <view class="content-container">
                            <view class="content-item">
                                <!-- <text class="chapter-title">第一章 总则</text> -->
                                <text class="content-text">{{ result }}</text>
                                <!-- <text class="content-text">第二条 本细则适用于公司总部及所有分支机构、子公司。</text> -->
                            </view>
                        </view>
                    </scroll-view>
                </view>

                <!-- 审查记录 -->
                <view v-if="active === 1" class="tab-content">
                    <scroll-view 
                        class="scroll-container"
                        scroll-y="true"
                    >
                        <view class="workflow-container">
                            <view class="workflow-title">审查记录</view>
                            <view class="timeline">
                                <view class="timeline-item" v-for="(record, index) in reviewRecords" :key="index">
                                    <view class="timeline-dot active"></view>
                                    <view class="timeline-content">
                                        <view class="approval-header">
                                            <!-- <text class="approval-step">{{ record.title }}</text> -->
                                            <text class="approval-status" :class="record.status">{{ record.statusText }}</text>
                                        </view>
                                        <text class="approval-description">{{ record.description }}</text>
                                        <view class="approval-comment">
                                            <text class="comment-label">创建人：{{ record.reviewer }}</text>
                                            <text class="comment-text">{{ record.time }}</text>
                                        </view>
                                        <view v-if="record.opinion" class="approval-comment">
                                            <text class="comment-label">审查意见：</text>
                                            <text class="comment-text">{{ record.opinion }}</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </view>
        
        <!-- 审查意见弹窗 -->
        <uv-popup ref="reviewPopup" mode="center" :mask-click="false" border-radius="16">
            <view class="review-popup">
                <view class="popup-content">
                    <uv-textarea 
                        autoHeight
                        v-model="reviewOpinion" 
                        placeholder="请输入审查意见（不通过时必填）" 
                        maxlength="500"
                        border="surround"
                        :show-confirm-bar="false"
                        count
                    ></uv-textarea>
                </view>
                <view class="popup-buttons">
                    <view class="uv-btn">
                        <uv-button 
                        text="不通过" 
                        type="error"
                        plain
                        size="normal"
                        @click="handleReview(2)"
                        customStyle="margin-right: 16rpx;"
                    ></uv-button>
                    </view>
                     <view class="uv-btn">
                        <uv-button 
                        text="通过" 
                        type="primary"
                        size="normal"
                        @click="handleReview(1)"
                    ></uv-button>
                     </view>
                </view>
            </view>
        </uv-popup>
        
        <footer-bar
          :buttons="footerButtons"
          @cancel="footerCancel"
          @submit="showReviewPopup"
         >
         </footer-bar>

    </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import footerBar from '@/components/footerBar.vue'; 
import contractApi from '@/api/contract/index.js';

const active = ref(0);
const tabs = ref(['审查结果','审查记录']);
// 页面参数
const pageParams = ref({
    id: null,
    type: null,
    detailInfo: null // 新增：接收详情信息
});
const result = ref('');
// 新增：存储详情信息
const detailInfo = ref(null);
// 审查意见相关
const reviewOpinion = ref('');
const reviewPopup = ref(null);
// AI审查结果状态控制
const aiReviewCompleted = ref(false);

// 底部按钮配置
const footerButtons = computed(() => [
    {
        text: '返回',
        type: 'cancel',
        slotName: 'cancel',
        bgColor: '#f5f5f5',
        textColor: '#666'
    },
    {
        text: '提交',
        type: 'submit',
        slotName: 'submit',
        bgColor: aiReviewCompleted.value ? '#1a73e8' : '#cccccc',
        textColor: '#fff',
        disabled: !aiReviewCompleted.value
    }
]);

// 审查记录数据
const reviewRecords = ref([]);


// 获取页面参数并调用AI审查
onMounted(async () => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage.options) {
        pageParams.value.id = currentPage.options.id;
        pageParams.value.type = currentPage.options.type;
        
        // 新增：解析传入的详情信息
        if (currentPage.options.detailInfo) {
            try {
                detailInfo.value = JSON.parse(decodeURIComponent(currentPage.options.detailInfo));
            } catch (error) {
                console.error('解析详情信息失败:', error);
            }
        }
        
        // 如果有合同ID，调用AI审查接口和获取审查记录
        if (pageParams.value.id) {
            await callAiContract();
            await loadReviewRecords();
        }
    }
});

// 调用AI合同审查接口
const callAiContract = async () => {
    try {
        uni.showLoading({
            title: 'AI审查中...',
            mask: true
        });
        
      result.value = await contractApi.aiContract(pageParams.value.id, {
            // 可以根据需要添加其他参数
        });
        
        uni.hideLoading();
        
        // 处理AI审查结果
        if (result.value) {
            aiReviewCompleted.value = true;
            uni.showToast({
                title: 'AI审查完成',
                icon: 'success'
            });
            // 可以根据需要更新页面数据
        }
    } catch (error) {
        uni.hideLoading();
        aiReviewCompleted.value = false;
        console.error('AI审查失败:', error);
        uni.showToast({
            title: 'AI审查失败',
            icon: 'none'
        });
    }
};

// 加载审查记录
const loadReviewRecords = async () => {
    try {
        const result = await contractApi.queryAllReviews(pageParams.value.id);
        
        if (result && Array.isArray(result)) {
            // 格式化审查记录数据以适配页面显示
            reviewRecords.value = result.map((record, index) => {
                // 根据完成时间判断状态
                const isCompleted = record.finishDate && record.finishDate.seconds;
                return {
                    // title: `审查记录 ${index + 1}`,
                    status: isCompleted ? 'completed' : 'pending',
                    statusText: isCompleted ? '已完成' : '进行中',
                    description: record.content || '暂无审查内容',
                    reviewer: record.createdBy || '系统',
                    time: record.finishDate || record.createdAt,
                    opinion: record.opinion || '暂无意见',
                    qualityScore: record.qualityScore || 0,
                    aiModels: record.aiModels || ''
                };
            });
        }
    } catch (error) {
        console.error('加载审查记录失败:', error);
        uni.showToast({
            title: '加载审查记录失败',
            icon: 'none'
        });
    }
};

// 显示审查弹窗
const showReviewPopup = () => {
    // 检查AI审查是否完成
    if (!aiReviewCompleted.value || !result.value) {
        uni.showToast({
            title: 'AI审查未完成，无法提交',
            icon: 'none'
        });
        return;
    }
    reviewOpinion.value = '';
    reviewPopup.value.open();
};

// 处理审查结果
const handleReview = async (conclusion) => {
    // 如果是不通过且没有输入意见，提示用户
    if (conclusion === 2 && !reviewOpinion.value.trim()) {
        uni.showToast({
            title: '不通过时必须输入意见',
            icon: 'none'
        });
        return;
    }
    
    try {
        uni.showLoading({
            title: '提交中...',
            mask: true
        });
        
        const params = {
            contractId: pageParams.value.id,// 合同主键ID
            content: result.value, // AI生成的内容
            opinion: reviewOpinion.value.trim(), // 审查意见
            conclusion: conclusion // 1代表通过，2代表不通过
        };
        
        await contractApi.contractMessageReview(params);
        
        uni.hideLoading();
        reviewPopup.value.close();
        
        uni.showToast({
            title: conclusion === 1 ? '审查通过' : '审查不通过',
            icon: 'success'
        });
        
        // 可以根据需要跳转或刷新页面
        setTimeout(() => {
            uni.navigateBack(2);
        }, 500);
        
    } catch (error) {
        uni.hideLoading();
        console.error('提交审查结果失败:', error);
        uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
        });
    }
};

const footerCancel = () => {
    uni.navigateBack();
};

// 根据状态返回对应的CSS类名
const getStatusClass = (status) => {
    if (!status) return '';
    
    const statusMap = {
        '草稿': 'status-draft',
        '待审核': 'status-pending', 
        '待审查': 'status-pending',
        '已发布': 'status-published',
        '已通过': 'status-published'
    };
    
    return statusMap[status] || 'status-pending';
};

// 原来的footerSubmitReview改为showReviewPopup
// const footerSubmitReview = () => {
//     uni.showToast({
//         title: '已标记完成',
//         icon: 'success'
//     });
// };
</script>

<style lang="scss" scoped>
.compliance-regulations-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #F5F7FA;

    .nav-right {
        display: flex;
        align-items: center;
    }

    .download-text {
        font-size: 24rpx;
        color: #1A73E8;
        margin-left: 8rpx;
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .card {
        background-color: #FFFFFF;
        border-radius: 16rpx;
        margin: 32rpx;
        padding: 32rpx;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .title-section .title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        display: block;
        margin-bottom: 24rpx;
    }

    .status-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;
    }

    .status-badge {
        border-radius: 16rpx;
        padding: 8rpx 16rpx;
        font-size: 24rpx;
        font-weight: bold;
    }

    .status-badge.status-draft {
        background-color: #FFF7E6;
        color: #FA8C16;
    }

    .status-badge.status-pending {
        background-color: #E6F4FF;
        color: #1A73E8;
    }

    .status-badge.status-published {
        background-color: #F6FFED;
        color: #52C41A;
    }

    .version {
        font-size: 24rpx;
        color: #999999;
    }

    .publisher {
        font-size: 24rpx;
        color: #999999;
    }

    .tab-bar {
        height: 88rpx;
        background-color: #FFFFFF;
        display: flex;
        margin: 0 32rpx;
    }

    .tab-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .tab-item.active {
        border-bottom: 2rpx solid #1A73E8;
    }

    .tab-text {
        font-size: 28rpx;
        font-weight: bold;
    }

    .tab-item.active .tab-text {
        color: #1A73E8;
    }

    .tab-item:not(.active) .tab-text {
        color: #666666;
    }

    .content-section {
        margin-top: 16rpx;
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
    }

    .tab-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .scroll-container {
        flex: 1;
        width: 100%;
        min-height: 0;
    }

    .content-container {
        padding: 20rpx;
        min-height: 600rpx;
    }

    .content-item {
        padding: 32rpx 0;
        border-bottom: 1px solid #E0E0E0;
    }

    .chapter-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        display: block;
        margin-bottom: 16rpx;
    }

    .content-text {
        font-size: 28rpx;
        color: #333333;
        line-height: 48rpx;
        display: block;
        margin-top: 16rpx;
    }

    .workflow-container {
        padding: 20rpx;
        min-height: 600rpx;
    }

    .workflow-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 30rpx;
        text-align: center;
    }

    .timeline {
        position: relative;
        padding-left: 40rpx;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 20rpx;
        top: 0;
        bottom: 0;
        width: 4rpx;
        background: #e0e0e0;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 40rpx;
        padding-left: 40rpx;
    }

    .timeline-dot {
        position: absolute;
        left: -28rpx;
        top: 10rpx;
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: #e0e0e0;
        border: 4rpx solid #fff;
        box-shadow: 0 0 0 2rpx #e0e0e0;
    }

    .timeline-dot.active {
        background: #007aff;
        box-shadow: 0 0 0 2rpx #007aff;
    }

    .timeline-content {
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 24rpx;
        border-left: 4rpx solid #007aff;
    }

    .approval-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
    }

    .approval-step {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
    }

    .approval-status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        color: #fff;
    }

    .approval-status.completed {
        background: #34c759;
    }

    .approval-description {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 12rpx;
    }

    .approval-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12rpx;
    }

    .approval-approver,
    .approval-time {
        font-size: 24rpx;
        color: #999;
    }

    .approval-comment {
        background: #fff;
        border-radius: 8rpx;
        padding: 16rpx;
        margin-top: 12rpx;
    }

    .comment-label {
        font-size: 24rpx;
        color: #666;
        font-weight: bold;
    }

    .comment-text {
        font-size: 26rpx;
        color: #333;
        line-height: 1.5;
        margin-top: 8rpx;
        display: block;
    }

    .report-file {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 32rpx;
        margin-top: 32rpx;
        border-top: 2rpx solid #F0F0F0;
    }

    .file-text {
        font-size: 28rpx;
        color: #1A73E8;
    }
}


// 新增弹窗样式
.review-popup {
    width: 600rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}
.popup-content {
    padding: 32rpx;
    background: #fafafa;
}

.popup-buttons {
    display: flex;
    padding: 24rpx 32rpx 32rpx;
    gap: 16rpx;
    background: #fafafa;
}

.opinion-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 16rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    font-size: 28rpx;
    line-height: 1.5;
    resize: none;
    box-sizing: border-box;
}

.char-count {
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
}

.uv-btn{
    flex: 1;
}
</style>
