<template>
	<view class="compliance-learn-container">
		<!-- 主要内容区域 -->
		<z-paging class="paging-content" ref="paging" v-model="list" @query="queryList">
			<template #top>
				<!-- 培训计划Banner -->
				<view class="banner">
					<view class="banner-content">
						<view class="completed-tip">
							<!-- <text class="completed-text">已完成：</text>
							<text class="completed-count">5/8</text> -->
						</view>
						<view class="box-btn">
							<view class="own-btn">
								<uv-button type="primary" @click="handleCertificate">
									进入考核中心
								</uv-button>
							</view>
							<view class="own-btn">
								<uv-button @click="handleAchievement">
									我的成就证书
								</uv-button>
							</view>

						</view>
					</view>
				</view>
				<header-bar @handleSearch="handleSearch" shape="circle" prefixIcon="search" disabled
					disabledColor="#fff" :fixed="false" />
				<!-- 分类Tab -->
				<view class="tab-container">
					<view v-for="(tab, index) in ['推荐', '热门', '最新']" :key="tab" class="tab"
						:class="{ active: activeTab === index }" @click="setActiveTab(index)">
						{{ tab }}
					</view>
				</view>

				<!-- 推荐课程横向滚动 -->
				<scroll-view class="course-scroll" scroll-x>
					<view class="course-list">
						<!-- 课程卡片 -->
						<view class="course-card" v-for="(course) in listLeft" :key="course.id"
							@click="courseDetail(course.id)">
							<image class="course-image" mode="aspectFill" :src="course.coverImageUrl">
							</image>
							<view class="course-info">
								<text class="course-name">{{ course.courseName }}</text>
							</view>
						</view>
					</view>
				</scroll-view>

			</template>
			<view class="main-content">

				<!-- 课程列表 -->
				<view class="course-list-container">
					<!-- 列表项1 -->
					<view class="list-item" v-for="(item, index) in list" :key="index">
						<view class="item-content" @click="courseDetail(item.id)">
							<image class="item-image" mode="aspectFill" :src="item.coverImageUrl">
							</image>
							<view class="item-info">
								<view class="item-header">
									<text class="item-title"> {{ item.courseName }}</text>
								</view>
								<text class="item-desc">{{ item.instructor }} · {{ item.durationMinutes }} 分钟</text>
							</view>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import {
	ref
} from 'vue';
import https from '../../api/study/index.js';
import headerBar from '../../components/headerBar.vue';
import {
	useUserStore
} from '@/store/pinia.js';
import uploads from '@/api/upload.js'
// 这里可以添加相关的逻辑代码
const userStore = useUserStore();
const paging = ref(null);
const list = ref([]);
const activeTab = ref(0);
const pageNo = ref(0);
const pageSize = ref(10);
const listLeft = ref([])
// 切换标签的方法
const setActiveTab = (i) => {
	activeTab.value = i;
};

function handleCertificate() {
	uni.navigateTo({
		url: '/pages/component/study/assess'
	});
}

function handleAchievement() {
	uni.navigateTo({
		url: '/pages/component/study/Certificate'
	});
}

function courseDetail(id) {
	uni.navigateTo({
		url: `/pages/component/study/courseDetail?id=${id}`
	});
}

function handleSearch() {
	uni.navigateTo({
		url: '/pages/component/study/studySearchPage'
	});
}

async function queryList(pageNo, pageSize) {
	var params = {
		page: pageNo - 1,
		size: pageSize,
		// keyword: keyword.value,
		// status: 1,
		// tenantId: userStore.tenantId
	}
	try {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
		const res = await https.courseList(params);
		// 为每个课程项获取真实的图片URL
		const processedContent = await Promise.all(res.content.map(async (item) => {
			if (item.coverImageUrl) {
				try {
					const imageUrlRes = await uploads.getFileUrl(item.coverImageUrl);
					item.coverImageUrl = imageUrlRes; // 用真实URL替换key
				} catch (error) {
					console.error('获取图片URL失败:', error);
					// 如果获取失败，保持原有的key或设置默认图片
				}
			}
			return item;
		}));
		paging.value.complete(processedContent);
		uni.hideLoading();
	} catch (err) {
		paging.value.complete(false);
		uni.hideLoading();
	}
}

// 初始化课程列表
(async () => {
	try {
		const res = await https.courseList({
			page: pageNo.value,
			size: pageSize.value,
			sort: '',
		});
		// 为每个课程项获取真实的图片URL
		const processedContent = await Promise.all(res.content.map(async (item) => {
			if (item.coverImageUrl) {
				try {
					const imageUrlRes = await uploads.getFileUrl(item.coverImageUrl);
					item.coverImageUrl = imageUrlRes; // 用真实URL替换key
				} catch (error) {
					console.error('获取图片URL失败:', error);
					// 如果获取失败，保持原有的key或设置默认图片
				}
			}
			return item;
		}));
		listLeft.value = processedContent;
	} catch (err) {
		console.error(err);
	}
})();
</script>

<style lang="scss" scoped>
.compliance-learn-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	position: relative;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	padding-top: 30rpx;
	padding-bottom: 112rpx;
}

/* Banner样式 */
.banner {
	padding: 20rpx 32rpx;
	margin-bottom: 20rpx;
	background-color: #FFFFFF;
	border-radius: 16rpx;
}

.banner-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	gap: 16rpx;
}


.completed-tip {
	display: flex;
	align-items: baseline;
	flex: 1;
}

.completed-text {
	font-size: 28rpx;
	color: #666;
}

.completed-count {
	font-size: 48rpx;
	color: #1A73E8;
	display: block;
	margin-top: 8rpx;
}

.detail-btn {
	background-color: #1A73E8;
	color: #FFFFFF;
	font-size: 28rpx;
	font-weight: bold;
	padding: 16rpx 32rpx;
	border-radius: 40rpx;
	margin: 0;
}

/* 分类Tab样式 */
.tab-container {
	margin-top: 16rpx;
	background-color: #FFFFFF;
	padding: 20rpx 32rpx;
	border-bottom: 1rpx solid #EEE;
	display: flex;
}

.tab {
	font-size: 28rpx;
	padding: 8rpx 28rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
	background-color: #EFEFF4;
	color: #666;
}

.tab.active {
	background-color: #1A73E8;
	color: #fff;
}

/* 课程横向滚动样式 */
.course-scroll {
	background-color: #FFFFFF;
	padding: 16rpx 0;
	white-space: nowrap;
}

.course-list {
	display: inline-flex;
	padding: 0 32rpx;
}

.course-card {
	width: 280rpx;
	background-color: #FFFFFF;
	border-radius: 16rpx;
	overflow: hidden;
	margin-right: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	flex-shrink: 0;
}

.course-image {
	width: 100%;
	height: 144rpx;
}

.course-info {
	padding: 16rpx;
}

.course-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.course-price {
	font-size: 24rpx;
	margin-top: 8rpx;
	display: block;
}

.course-price.free {
	color: #1A73E8;
}

.course-price.paid {
	color: #34A853;
}

/* 课程列表样式 */
.course-list-container {
	padding: 0 32rpx;
	margin-top: 32rpx;
}

.list-item {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.item-content {
	padding: 24rpx;
	display: flex;
	align-items: center;
}

.item-image {
	width: 160rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 24rpx;
	flex-shrink: 0;
}

.item-info {
	flex: 1;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.item-title {
	font-size: 32rpx;
	color: #333;
	margin-right: 16rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
	display: block;
}

.progress-bar {
	width: 100%;
	height: 4px;
	background-color: #EEE;
	border-radius: 2px;
	margin-top: 16rpx;
}

.progress-fill {
	height: 100%;
	background-color: #1A73E8;
	border-radius: 2px;
}
</style>