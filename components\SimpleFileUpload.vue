<template>
  <view class="simple-file-upload">
    <!-- 上传区域 - 当没有文件时显示 -->
    <view v-if="!hasFiles" class="upload-area" @click="handleUpload">
      <uni-icons type="plus" size="26" color="#409EFF"/>
      <text class="upload-text">{{ placeholder }}</text>
    </view>

    <!-- 已上传文件展示区域 - 当有文件时显示 -->
    <view v-else class="file-display">
      <view class="file-item" @click="previewFile">
        <!-- 文件图标 -->
        <view class="file-icon">
          <uni-icons :type="getFileIcon(fileName)" size="32" color="#409EFF" />
        </view>
        
        <!-- 文件信息 -->
        <view class="file-info">
          <text class="file-name">{{ fileName }}</text>
          <text class="file-status">上传完成</text>
        </view>
        
        <!-- 操作按钮 -->
        <view class="file-actions">
          <view class="action-btn preview-btn" @click.stop="previewFile">
            <uni-icons type="eye" size="16" color="#409EFF" />
            <text>预览</text>
          </view>
          <view class="action-btn delete-btn" @click.stop="removeFile">
            <uni-icons type="trash" size="16" color="#F56C6C" />
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提示文字 -->
    <view class="tip-text" v-if="tipText">
      {{ tipText }}
    </view>
  </view>
</template>

<script>
import { config } from '@/config'
import { useUserStore } from '@/store/pinia.js'
import uploads from '@/api/upload.js'

export default {
  name: 'SimpleFileUpload',
  props: {
    // v-model绑定的文件key
    modelValue: {
      type: String,
      default: ''
    },
    // 上传提示文字
    placeholder: {
      type: String,
      default: '点击上传文件'
    },
    // 底部提示文字
    tipText: {
      type: String,
      default: ''
    },
    // 文件类型筛选
    fileType: {
      type: String,
      default: 'all' // image/video/all
    },
    // 单个文件大小限制（MB）
    sizeLimit: {
      type: Number,
      default: 10
    },
    // 服务名称
    serviceName: {
      type: String,
      default: 'compliancelistservice'
    },
    // 分类名称
    categoryName: {
      type: String,
      default: 'ai'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      uploadUrl: '/services/whiskerguardgeneralservice/api/file/upload',
      fileName: '',
      isUploading: false
    }
  },
  
  computed: {
    hasFiles() {
      return !!this.modelValue
    }
  },
  
  watch: {
    modelValue: {
      handler(newValue) {
        if (newValue) {
          // 从key中提取文件名
          this.fileName = newValue.split('/').pop() || '未知文件'
        } else {
          this.fileName = ''
        }
      },
      immediate: true
    }
  },
  
  methods: {
    async handleUpload() {
      if (this.disabled || this.isUploading) return
      
      try {
        let res = null
        let files = null
        
        // 微信小程序环境处理
        //#ifdef MP-WEIXIN
        res = await new Promise((resolve, reject) => {
          wx.chooseMessageFile({
            count: 1,
            type: this.fileType === 'image' ? 'image' : 'all',
            success: resolve,
            fail: reject
          })
        })
        files = res.tempFiles.map(file => ({
          name: file.name,
          path: file.path,
          size: file.size,
          type: file.type
        }))
        // 其他平台处理
        //#else
        res = await uni.chooseFile({
          count: 1,
          type: this.fileType,
          extension: this.fileType === 'image' ? ['jpg', 'png', 'jpeg'] : undefined
        })
        files = res.tempFiles
        //#endif
        
        if (files && files.length > 0) {
          const file = files[0]
          
          // 验证文件大小
          if (file.size > this.sizeLimit * 1024 * 1024) {
            uni.showToast({ 
              title: `文件大小不能超过${this.sizeLimit}MB`, 
              icon: 'none' 
            })
            return
          }
          
          // 开始上传
          await this.uploadFile(file)
        }
      } catch (err) {
        console.log('文件选择取消', err)
      }
    },
    
    async uploadFile(file) {
      if (!file || !file.path) {
        console.error('文件路径不存在')
        return
      }
      
      const userStore = useUserStore()
      this.isUploading = true
      
      // 显示上传进度
      uni.showLoading({ title: '上传中...' })
      
      return new Promise((resolve, reject) => {
        const uploadTask = uni.uploadFile({
          url: config.baseUrl + this.uploadUrl + `?serviceName=${this.serviceName}&categoryName=${this.categoryName}`,
          filePath: file.path,
          name: 'file',
          formData: {},
          header: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${userStore.token ? userStore.token : null}`,
          },
          success: (uploadRes) => {
            try {
              // 解析返回结果
              const result = typeof uploadRes.data === 'string' 
                ? JSON.parse(uploadRes.data) 
                : uploadRes.data
              
              if (result && result.key) {
                // 更新v-model值为文件key
                this.$emit('update:modelValue', result.key)
                
                // 触发上传成功事件
                this.$emit('upload-success', {
                  file,
                  result,
                  key: result.key
                })
                
                uni.showToast({ title: '上传成功', icon: 'success' })
              } else {
                throw new Error('上传结果格式错误')
              }
              
              resolve(result)
            } catch (e) {
              console.error('解析上传结果失败', e)
              uni.showToast({ title: '上传失败', icon: 'none' })
              reject(e)
            } finally {
              uni.hideLoading()
              this.isUploading = false
            }
          },
          fail: (err) => {
            console.error('上传失败', err)
            uni.showToast({ title: '上传失败', icon: 'none' })
            uni.hideLoading()
            this.isUploading = false
            reject(err)
          }
        })
        
        // 监听上传进度
        uploadTask.onProgressUpdate && uploadTask.onProgressUpdate((res) => {
          console.log('上传进度:', res.progress + '%')
        })
      })
    },
    
    removeFile() {
      uni.showModal({
        title: '提示',
        content: '确定删除该文件吗？',
        success: (res) => {
          if (res.confirm) {
            this.$emit('update:modelValue', '')
            this.$emit('file-remove')
            uni.showToast({ title: '文件已删除', icon: 'success' })
          }
        }
      })
    },
    
    async previewFile() {
      if (!this.modelValue) {
        uni.showToast({ title: '文件不存在', icon: 'none' })
        return
      }
      
      try {
        uni.showLoading({ title: '加载中...' })
        
        // 获取真实文件地址
        const realUrl = await uploads.getFileUrl(this.modelValue)
        
        uni.hideLoading()
        
        // 判断文件类型
        const fileExt = this.fileName.split('.').pop()?.toLowerCase()
        
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExt)) {
          // 图片预览
          uni.previewImage({
            urls: [realUrl],
            current: realUrl,
            fail: () => {
              uni.showToast({ title: '预览失败', icon: 'none' })
            }
          })
        } else {
          // 其他文件类型下载后预览
          uni.downloadFile({
            url: realUrl,
            success: (res) => {
              if (res.statusCode === 200) {
                uni.openDocument({
                  filePath: res.tempFilePath,
                  fileType: fileExt,
                  showMenu: true,
                  fail: () => {
                    uni.showToast({ title: '此文件类型不支持预览', icon: 'none' })
                  }
                })
              }
            },
            fail: () => {
              uni.showToast({ title: '文件下载失败', icon: 'none' })
            }
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('获取文件地址失败:', error)
        uni.showToast({ title: '文件预览失败', icon: 'none' })
      }
    },
    
    getFileIcon(filename) {
      if (!filename) return 'file'
      
      const ext = filename.split('.').pop()?.toLowerCase()
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp']
      const docTypes = ['doc', 'docx']
      const xlsTypes = ['xls', 'xlsx']
      const pptTypes = ['ppt', 'pptx']
      const pdfType = ['pdf']
      
      if (imageTypes.includes(ext)) return 'image'
      if (docTypes.includes(ext)) return 'document'
      if (xlsTypes.includes(ext)) return 'stats'
      if (pptTypes.includes(ext)) return 'slides'
      if (pdfType.includes(ext)) return 'pdf'
      return 'file'
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-file-upload {
  .upload-area {
    width: 100%;
    height: 100rpx;
    border: 2rpx dashed #409EFF;
    border-radius: 12rpx;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
    
    .upload-text {
      font-size: 28rpx;
      color: #409EFF;
      margin-left: 10rpx;
    }
  }
  
  .file-display {
    .file-item {
      width: 100%;
      background: #ffffff;
      border: 1rpx solid #e4e7ed;
      border-radius: 12rpx;
      padding: 24rpx;
      display: flex;
      align-items: center;
      gap: 20rpx;
      
      .file-icon {
        flex-shrink: 0;
      }
      
      .file-info {
        flex: 1;
        
        .file-name {
          font-size: 28rpx;
          color: #606266;
          display: block;
          margin-bottom: 8rpx;
        }
        
        .file-status {
          font-size: 24rpx;
          color: #67C23A;
        }
      }
      
      .file-actions {
        display: flex;
        gap: 16rpx;
        
        .action-btn {
          display: flex;
          align-items: center;
          gap: 8rpx;
          padding: 8rpx 16rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          
          &.preview-btn {
            background: #ecf5ff;
            color: #409EFF;
          }
          
          &.delete-btn {
            background: #fef0f0;
            color: #F56C6C;
          }
        }
      }
    }
  }
  
  .tip-text {
    font-size: 24rpx;
    color: #909399;
    margin-top: 16rpx;
    text-align: center;
  }
}
</style>