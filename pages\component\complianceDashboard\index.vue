<template>
	<view class="compliance-dashboard">
		<!-- 顶部概览卡片区 -->
		<view class="overview-cards">
			<view class="card" v-for="(card, index) in overviewCards" :key="index"
				:style="{ backgroundColor: card.color }">
				<view class="card-content">
					<view class="card-text">
						<text class="card-value">{{ card.value }}</text>
						<text class="card-title">{{ card.title }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 风险分析 -->
		<view class="chart-section">
			<view class="chart-title">风险分析</view>
			<canvas canvas-id="UPcJhWRBPsttGaiEIxIjhcKmnszjRMEN" id="UPcJhWRBPsttGaiEIxIjhcKmnszjRMEN"
				class="charts pie-chart" @touchend="tap" />
		</view>
		<!-- 任务状态统计 -->
		<view class="chart-section">
			<view class="chart-header">
				<view class="chart-title">任务状态统计</view>
				<view class="time-tabs">
					<text class="time-tab" :class="{ active: activeTimeRange === '7d' }"
						@click="changeTimeRange('7d')">7天</text>
					<text class="time-tab" :class="{ active: activeTimeRange === '30d' }"
						@click="changeTimeRange('30d')">30天</text>
					<text class="time-tab" :class="{ active: activeTimeRange === '6m' }"
						@click="changeTimeRange('6m')">6个月</text>
					<text class="time-tab" :class="{ active: activeTimeRange === '1y' }"
						@click="changeTimeRange('1y')">1年</text>
				</view>
			</view>
			<canvas canvas-id="pGSmkHucnxeqPzMFMOTOocnBRlXexaxZ" id="pGSmkHucnxeqPzMFMOTOocnBRlXexaxZ"
				class="charts bar-chart" @touchend="tap" />
		</view>
	</view>
</template>

<script>
import uCharts from '../uCharts/u-charts.js';
import complianceDashboardApi from '@/api/complianceDashboard/index.js';
var uChartsInstance = {};
export default {
	data() {
		return {
			cWidth: 705,
			cHeight: 500,
			taskStatusData: [],
			riskDistributionData: [],
			loading: false,
			activeTimeRange: '7d',
			taskTrendData: [],
			// 顶部概览卡片数据
			overviewCards: [
				{ icon: 'compose', value: '0', title: '调查任务数', color: '#1890ff' },
				{ icon: 'list', value: '0', title: '整改任务数', color: '#fa8c16' },
				{ icon: 'warn', value: '0', title: '高风险任务', color: '#f5222d' },
				{ icon: 'checkmark', value: '0%', title: '完成率', color: '#52c41a' }
			]
		};
	},
	onReady() {
		//这里的 750 对应 css .charts 的 width
		this.cWidth = uni.upx2px(705);
		//这里的 500 对应 css .charts 的 height
		this.cHeight = uni.upx2px(500);
		this.getServerData();
		this.getRiskData();
		this.loadDashboardData();
	},
	methods: {
		async getServerData() {
			try {
				this.loading = true;
				const response = await complianceDashboardApi.queryTaskStatusTrend();
				const data = response;

				// 保存原始数据
				this.taskTrendData = data;

				// 根据当前选中的时间范围更新图表
				this.updateTaskStatusByTimeRange(this.activeTimeRange);
			} catch (error) {
				console.error('加载任务状态数据失败:', error);
				// 使用默认数据
				const defaultData = {
					series: [
						{
							data: [{ "name": "新建任务", "value": 25 }, { "name": "完成任务", "value": 68 }, { "name": "超期任务", "value": 12 }]
						}
					]
				};
				this.drawCharts('pGSmkHucnxeqPzMFMOTOocnBRlXexaxZ', defaultData, 'bar');
			} finally {
				this.loading = false;
			}
		},
		async getRiskData() {
			try {
				this.loading = true;
				const response = await complianceDashboardApi.queryRiskDistribution();
				const data = response;

				// 直接使用返回的数据格式，不需要映射
				const riskData = data.map(item => ({
					name: item.riskLevel,
					value: item.count || 0
				}));

				const chartData = {
					series: [
						{
							data: riskData
						}
					]
				};

				this.riskDistributionData = chartData;
				this.drawCharts('UPcJhWRBPsttGaiEIxIjhcKmnszjRMEN', chartData, 'pie');
			} catch (error) {
				console.error('加载风险分布数据失败:', error);
				// 使用默认数据
				const defaultData = {
					series: [
						{
							data: [{ "name": "高风险", "value": 15 }, { "name": "中风险", "value": 35 }, { "name": "低风险", "value": 50 }]
						}
					]
				};
				this.drawCharts('UPcJhWRBPsttGaiEIxIjhcKmnszjRMEN', defaultData, 'pie');
			} finally {
				this.loading = false;
			}
		},

		// 时间范围切换
		changeTimeRange(range) {
			// 立即更新选中状态，提供即时的视觉反馈
			this.activeTimeRange = range;
			
			// 清除之前的防抖定时器
			if (this.debounceTimer) {
				clearTimeout(this.debounceTimer);
			}
			
			// 设置防抖，延迟500ms执行图表更新，以最后一次点击为准
			this.debounceTimer = setTimeout(() => {
				// 只有当前选中的时间范围与要更新的范围一致时才更新
				if (this.activeTimeRange === range) {
					this.updateTaskStatusByTimeRange(range);
				}
			}, 1000);
		},
		// 根据时间范围更新任务状态数据
		updateTaskStatusByTimeRange(range) {
			if (this.taskTrendData.length === 0) return;

			// 时间范围到数组索引的映射
			const timeRangeMap = {
				'7d': 0,   // 最近7天
				'30d': 1,  // 最近30天
				'6m': 2,   // 最近6个月
				'1y': 3    // 最近1年
			};

			const index = timeRangeMap[range];
			if (index !== undefined && this.taskTrendData[index]) {
				const data = this.taskTrendData[index];

				// 转换数据格式为图表需要的格式
				const chartData = {
					series: [
						{
							data: [
								{ "name": "新建任务", "value": data.newCount || 0 },
								{ "name": "完成任务", "value": data.completedCount || 0 },
								{ "name": "超期任务", "value": data.overdueCount || 0 }
							]
						}
					]
				};

				this.taskStatusData = chartData;
				this.drawCharts('pGSmkHucnxeqPzMFMOTOocnBRlXexaxZ', chartData, 'bar');
			}
		},
		// 加载仪表板统计数据
		async loadDashboardData() {
			try {
				this.loading = true;
				const response = await complianceDashboardApi.queryComplianceDashboard();
				const data = response;
				// 更新概览卡片数据
				this.overviewCards[0].value = data.investigateTasks || '0';
				this.overviewCards[1].value = data.improvementTasks || '0';
				this.overviewCards[2].value = data.highRiskTasks || '0';
				this.overviewCards[3].value = data.completionRate || '0%';
			} catch (error) {
				console.error('加载仪表板数据失败:', error);
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		drawCharts(id, data, type) {
			const ctx = uni.createCanvasContext(id, this);
			if (type === 'bar') {
				uChartsInstance[id] = new uCharts({
					type: "mount",
					context: ctx,
					width: this.cWidth,
					height: this.cHeight,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
					padding: [15, 15, 15, 0],
					enableScroll: false,
					legend: {},
					xAxis: {
						disableGrid: true
					},
					yAxis: {
						data: [
							{
								min: 0
							}
						]
					},
					extra: {
						mount: {
							type: "bar",
							widthRatio: 0.8
						}
					}
				});
			} else if (type === 'pie') {
				uChartsInstance[id] = new uCharts({
					type: "pie",
					context: ctx,
					width: this.cWidth,
					height: this.cHeight,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					color: ["#EE6666", "#FAC858", "#91CB74", "#1890FF", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
					padding: [20, 20, 20, 20],
					enableScroll: false,
					extra: {
						pie: {
							activeOpacity: 0.5,
							activeRadius: 10,
							offsetAngle: 0,
							labelWidth: 15,
							border: true,
							borderWidth: 3,
							borderColor: "#FFFFFF"
						}
					}
				});
			}
		},
		tap(e) {
			uChartsInstance[e.target.id].touchLegend(e);
			uChartsInstance[e.target.id].showToolTip(e);
		}
	}
};
</script>

<style scoped>
.compliance-dashboard {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.chart-section {
	margin-bottom: 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.chart-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.time-tabs {
	display: flex;
}

.time-tab {
	font-size: 24rpx;
	color: #999;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	margin-left: 10rpx;
	transition: all 0.3s ease;
}

.time-tab.active {
	background-color: #1890ff;
	color: #fff;
}

.charts {
	width: 705rpx;
	height: 500rpx;
	display: block;
	margin: 0 auto;
}

.bar-chart {
	margin-bottom: 20rpx;
}

.pie-chart {
	margin-bottom: 20rpx;
}

/* 概览卡片样式 */
.overview-cards {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.card {
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-content {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.card-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.2);
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 20rpx;
}

.card-text {
	display: flex;
	flex-direction: column;
}

.card-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	line-height: 1.2;
}

.card-title {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}
</style>