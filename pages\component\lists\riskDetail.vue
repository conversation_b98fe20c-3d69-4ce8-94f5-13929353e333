<template>
	<view class="transform-table">
		<view class="transform-table__form">
			<!-- 状态标识区 -->
			<view class="card-section status-header">
				<view class="create-info-container">
					<view class="create-info-item">创建人：{{ riskDetail.createdBy }}</view>
					<view class="create-info-item">创建时间：{{ riskDetail.createdAt }}</view>
				</view>
				<view :class="['status-tag', statusClass]">{{ statusText }}</view>
			</view>

			<!-- 基础信息卡片 -->
			<view class="card-section">
				<view class="section-title">
					<text class="section-title-text">基础信息</text>
				</view>
				<view class="info-item">
					<text class="info-label">业务类型：</text>
					<text class="info-value">{{ getBusinessTypeName(riskDetail.businessType) }}</text>
				</view>
				<view class="info-item" v-if="riskDetail.orgUnitId">
					<text class="info-label">所属部门：</text>
					<text class="info-value">{{ getDepartmentName(riskDetail.orgUnitId) }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">风险等级：</text>
					<view class="risk-level">
						<view :class="['risk-dot', getRiskLevelClass(riskDetail.riskLevelModel)]"></view>
						<text class="risk-text">{{ getRiskLevelText(riskDetail.riskLevelModel) }}</text>
					</view>
				</view>
			</view>
			<!-- 风险信息卡片 -->
			<view class="card-section">
				<view class="section-title">
					<text class="section-title-text">风险信息区</text>
				</view>
				<view class="risk-section">
					<view class="subsection-title">合规风险描述</view>
					<view class="section-content">
						{{ riskDetail.riskDescription }}
					</view>
				</view>
				<view class="risk-section">
					<view class="subsection-title">风险产生原因</view>
					<view class="section-content">
						{{ riskDetail.riskCause }}
					</view>
				</view>
				<view class="risk-section">
					<view class="subsection-title">风险发生后果</view>
					<view class="section-content">
						{{ riskDetail.riskConsequence }}
					</view>
				</view>
			</view>
			<!-- 合规义务卡片 -->
			<view class="card-section">
				<view class="section-title">
					<text class="section-title-text">合规义务信息区/合规依据</text>
				</view>
				<view class="compliance-section">
					<view class="subsection-title">法律规定</view>
					<view class="section-content">
						{{ riskDetail.lawsRegulations }}
					</view>
				</view>
				<view class="compliance-section">
					<view class="subsection-title">监管规定</view>
					<view class="section-content">
						{{ riskDetail.regulatoryRequirements }}
					</view>
				</view>
				<view class="compliance-section">
					<view class="subsection-title">规章制度</view>
					<view class="section-content">
						{{ riskDetail.rulesRegulations }}
					</view>
				</view>
			</view>
			<!-- 管理措施卡片 -->
			<view class="card-section">
				<view class="section-title">
					<text class="section-title-text">管理信息区</text>
				</view>
				<view class="measure-section">
					<view class="subsection-title">风险管控措施</view>
					<view class="section-content">
						{{ riskDetail.controlMeasures }}
					</view>
				</view>
				<view class="department-section">
					<view class="subsection-title">归口部门</view>
					<view class="department-tags">
						<!-- 优先使用responsibleOrgUnitId，如果不存在则使用responsibleDepartments -->
						<template v-if="riskDetail.responsibleOrgUnitId && Array.isArray(riskDetail.responsibleOrgUnitId)">
							<text v-for="(name, index) in getDepartmentNames(riskDetail.responsibleOrgUnitId)" :key="index" class="department-tag">{{ name }}</text>
						</template>
						<template v-else>
							<text v-for="dept in formatDepartments(riskDetail.responsibleDepartments)" :key="dept.id" class="department-tag">{{ dept.name }}</text>
						</template>
					</view>
				</view>
				<view class="department-section">
					<view class="subsection-title">配合部门</view>
					<view class="department-tags">
						<!-- 优先使用cooperatingOrgUnitId，如果不存在则使用cooperatingDepartments -->
						<template v-if="riskDetail.cooperatingOrgUnitId && Array.isArray(riskDetail.cooperatingOrgUnitId)">
							<text v-for="(name, index) in getDepartmentNames(riskDetail.cooperatingOrgUnitId)" :key="index" class="department-tag">{{ name }}</text>
						</template>
						<template v-else>
							<text v-for="dept in formatDepartments(riskDetail.cooperatingDepartments)" :key="dept.id" class="department-tag">{{ dept.name }}</text>
						</template>
					</view>
				</view>
			</view>
		</view>
		<view v-if="isDataLoaded">
            <FooterBar @submit="handleFooterClick" :buttons="footerButtons" />
        </view>
	</view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/pinia.js';
import homeApi from '@/api/home/<USER>';
import FooterBar from '@/components/footerBar.vue'
import getDictData from '@/utils/dict.js'

const riskId = ref('');
const riskDetail = ref({});
const userStore = useUserStore();
const isDataLoaded = ref(false); // 数据加载状态
const lawyer = computed(() => userStore.lawyer); // 律师角色判断

// 存储业务类型字典数据
const businessTypeDict = ref([]);

const status = ref('active');

const footerButtons = computed(() => {
	// 如果状态为已通过，律师和非律师都不能操作
	if (riskDetail.value.approvalStatus === 'APPROVED') {
		return [];
	}
	
	// 律师角色且状态为待审核时可以操作
	if (lawyer.value && riskDetail.value.approvalStatus === 'PENDING') {
		return [
			{
				text: '编辑',
				type: 'submit',
				slotName: 'submit',
				bgColor: '#1a73e8',
				textColor: '#fff'
			}
		];
	}
	
	// 非律师角色且状态为草稿时可以操作
	if (!lawyer.value && riskDetail.value.approvalStatus === 'DRAFT') {
		return [
			{
				text: '编辑',
				type: 'submit',
				slotName: 'submit',
				bgColor: '#1a73e8',
				textColor: '#fff'
			}
		];
	}
	
	// 其他情况不显示按钮
	return [];
});

const statusText = computed(() => {
	switch (riskDetail.value.approvalStatus) {
		case 'APPROVED': return '已生效';
		case 'PENDING': return '待审核';
		case 'DRAFT': return '草稿';
		case 'REJECTED': return '已驳回';
		default: return '';
	}
});

const handleFooterClick = () => { 
	uni.navigateTo({
			url: `/pages/component/lists/addRisk?type=detail&id=${riskId.value}`
		})
};

const statusClass = computed(() => {
	switch (riskDetail.value.approvalStatus) {
		case 'APPROVED': return 'active';
		case 'PENDING': return 'pending';
		case 'DRAFT': return 'draft';
		case 'REJECTED': return 'rejected';
		default: return 'active';
	}
});

const getRiskLevelClass = (level) => {
	switch (level) {
		case 'HIGH': return 'high-risk';
		case 'MEDIUM': return 'medium-risk';
		case 'LOW': return 'low-risk';
		default: return 'high-risk';
	}
};

const getRiskLevelText = (level) => {
	switch (level) {
		case 'HIGH': return '高风险';
		case 'MEDIUM': return '中风险';
		case 'LOW': return '低风险';
		default: return '高风险';
	}
};

// 根据业务类型ID获取业务类型名称
const getBusinessTypeName = (businessType) => {
	if (!businessType) return '未设置';
	if (!businessTypeDict.value || businessTypeDict.value.length === 0) return businessType;
	
	const typeItem = businessTypeDict.value.find(item => item.value === businessType || item.id === businessType);
	return typeItem ? typeItem.name : businessType;
};

// 根据部门ID获取部门名称
const getDepartmentName = (departmentId) => {
	if (!departmentId) return '未设置';
	const departments = userStore.getDepartments();
	if (!departments) return departmentId;

	// 处理数字类型的ID
	const targetId = typeof departmentId === 'string' ? parseInt(departmentId) : departmentId;
	const department = departments.find(dept => {
		const deptId = typeof dept.id === 'string' ? parseInt(dept.id) : dept.id;
		return deptId === targetId;
	});
	return department ? department.name : departmentId;
};

// 根据部门ID数组获取部门名称数组
const getDepartmentNames = (departmentIds) => {
	if (!departmentIds || !Array.isArray(departmentIds)) return [];
	return departmentIds.map(id => getDepartmentName(id)).filter(name => name !== '未设置');
};

// 处理部门数组，将ID转换为名称
const formatDepartments = (departments) => {
	if (!departments || !Array.isArray(departments)) return [];
	
	return departments.map(dept => {
		// 如果已经是对象格式且包含name属性，直接返回
		if (typeof dept === 'object' && dept.name) {
			return dept;
		}
		// 如果是ID字符串或数字，进行映射
		const deptId = typeof dept === 'object' ? dept.id : dept;
		return {
			id: deptId,
			name: getDepartmentName(deptId)
		};
	});
};


const handleBack = () => {
	uni.navigateBack();
};
const actionSheet = ref();
const showActionSheet = () => {
	actionSheet.value.open();
};
const copyLink = () => {
	uni.showToast({
		title: '链接已复制',
		icon: 'success'
	});
	actionSheet.value.close();
};
const print = () => {
	uni.showToast({
		title: '调用打印功能',
		icon: 'none'
	});
	actionSheet.value.close();
};
const collect = () => {
	uni.showToast({
		title: '已收藏',
		icon: 'success'
	});
	actionSheet.value.close();
};
const report = () => {
	uni.navigateTo({
		url: '/pages/report/index'
	});
	actionSheet.value.close();
};
const deleteItem = () => {
	uni.showModal({
		title: '确认删除',
		content: '确定要删除该风险识别清单吗？',
		success: (res) => {
			if (res.confirm) {
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
			actionSheet.value.close();
		}
	});
};
const exportPDF = () => {
	uni.showLoading({
		title: '正在生成PDF...'
	});
	setTimeout(() => {
		uni.hideLoading();
		uni.showModal({
			title: 'PDF生成完成',
			content: '请选择下载或分享',
			showCancel: true,
			cancelText: '分享',
			confirmText: '下载',
			success: (res) => {
				if (res.confirm) {
					uni.showToast({
						title: '开始下载',
						icon: 'none'
					});
				} else if (res.cancel) {
					uni.showActionSheet({
						itemList: ['微信', 'QQ', '钉钉'],
						success: () => {
							uni.showToast({
								title: '分享成功',
								icon: 'success'
							});
						}
					});
				}
			}
		});
	}, 2000);
};

// 获取风险详情数据
const getRiskDetail = async (id) => {
	try {
		uni.showLoading({ title: '加载中...' });
		const response = await homeApi.complianceRiskMains({
			id: id
		}, 'info');
		if (response) {
			riskDetail.value = response;
		}
		uni.hideLoading();
		isDataLoaded.value = true; // 数据加载完成
	} catch (error) {
		console.error('获取风险详情失败:', error);
		uni.hideLoading();
		isDataLoaded.value = true; // 即使失败也设置为已加载，避免一直不显示按钮
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		});
	}
};

onMounted(async () => {
	// 获取业务类型字典数据
	const data = await getDictData('88', '');
	businessTypeDict.value = data || [];
});

onLoad((options) => {
	console.log('页面参数:', options);
	if (options.id) {
		riskId.value = options.id;
		getRiskDetail(options.id);
	} else {
		uni.showToast({
			title: '缺少必要参数',
			icon: 'none'
		});
	}
});
</script>
<style>
.transform-table {
	box-sizing: border-box;
	padding: 40rpx 0;
}

.transform-table__form {
	padding-bottom: 120rpx;
}

.subsection-title {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
	position: relative;
	padding-left: 24rpx;
}

.subsection-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 16px;
	background-color: #1890ff;
	border-radius: 2px;
}


.status-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.create-info-container {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.create-info-item {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	margin-bottom: 4rpx;
}

.create-info-item:last-child {
	margin-bottom: 0;
}

.info-item {
	display: flex;
	margin-bottom: 24rpx;
	font-size: 14px;
}

.info-label {
	color: #666;
	width: 170rpx;
	flex-shrink: 0;
}

.info-value {
	color: #333;
	flex: 1;
}

.department-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.department-tag {
	display: inline-block;
	padding: 6rpx 16rpx;
	background-color: #f0f0f0;
	border-radius: 4px;
	font-size: 12px;
	color: #666;
}

.risk-level {
	display: flex;
	align-items: center;
}

.risk-dot {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	margin-right: 8rpx;
}

.risk-dot.high-risk {
	background-color: #ff4d4f;
}

.risk-dot.medium-risk {
	background-color: #fa8c16;
}

.risk-dot.low-risk {
	background-color: #52c41a;
}

.risk-text {
	font-size: 14px;
	color: #333;
}

.risk-section {
	margin-bottom: 32rpx;
	padding: 24rpx;
	background-color: #f9f9f9;
	border-radius: 8px;
}

.compliance-section {
	margin-bottom: 32rpx;
}

.section-content {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	padding-left: 24rpx;
}

.compliance-card {
	background-color: #f9f9f9;
	padding: 24rpx;
	border-radius: 4px;
	margin-bottom: 16rpx;
}

.compliance-title {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
}

.compliance-info {
	font-size: 12px;
	color: #666;
	margin-bottom: 4rpx;
}

.compliance-clause {
	display: flex;
	margin-top: 16rpx;
}

.clause-line {
	width: 4rpx;
	background-color: #1890ff;
	margin-right: 16rpx;
	border-radius: 2rpx;
}

.clause-content {
	flex: 1;
	font-size: 12px;
	color: #666;
	font-style: italic;
}

.measure-section {
	margin-bottom: 24rpx;
}

.department-section {
	margin-bottom: 24rpx;
}


</style>