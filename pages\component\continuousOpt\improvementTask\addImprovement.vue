【<template>
  <view class="container">
    <uv-form labelPosition="top" label-width="200" :model="formData">
      <!-- 任务基本信息 -->
      <view class="section">
        <view class="section-title">任务基本信息</view>
        <uv-form-item label="任务名称">
          <uv-input v-model="formData.title" placeholder="请输入任务名称" clearable maxlength="50" show-word-limit />
        </uv-form-item>
        <uv-form-item label="整改类型">
          <picker-input v-model="formData.experienceType" :columns="[correctionTypes]" placeholder="请选择整改类型"
            display-key="text" value-key="value" />
        </uv-form-item>
        <uv-form-item label="风险等级">
          <FilterTags v-model="formData.level" :tags="riskLevels" :fixed="false" display-key="name" value-key="id" />
        </uv-form-item>
      </view>

      <!-- 人员配置 -->
      <view class="section">
        <view class="section-title">人员配置</view>
        <uv-form-item label="主责员工">
          <employee-picker v-model="formData.dutyEmployeeId" placeholder="请选择主责员工" @confirm="onDutyEmployeeSelect" />
        </uv-form-item>
        <uv-form-item label="主责组织">
          <picker-input v-model="formData.dutyEmployeeOrgId" :columns="[orgList]" placeholder="请选择主责组织"
            display-key="name" value-key="id" />
        </uv-form-item>
        <uv-form-item label="协作员工">
          <employee-picker v-model="formData.collaborationEmployeeId" placeholder="请选择协作员工"
            @confirm="onCollaborationEmployeeSelect" />
        </uv-form-item>
        <uv-form-item label="监督员工">
          <employee-picker v-model="formData.supervisionEmployeeId" placeholder="请选择监督员工"
            @confirm="onSupervisionEmployeeSelect" />
        </uv-form-item>
      </view>

      <!-- 整改说明 -->
      <view class="section">
        <view class="section-title">整改说明</view>
        <uv-form-item label="整改背景说明">
          <uv-textarea autoHeight v-model="formData.problemBackground" placeholder="请输入整改背景说明" maxlength="500" show-word-limit />
        </uv-form-item>
        <uv-form-item label="事件经过">
          <uv-textarea autoHeight v-model="formData.eventCourse" placeholder="请输入事件经过" maxlength="500" show-word-limit />
        </uv-form-item>
        <uv-form-item label="问题分析">
          <uv-textarea autoHeight v-model="formData.problemAnalysis" placeholder="请输入问题分析" maxlength="500" show-word-limit />
        </uv-form-item>
        <uv-form-item label="改进方案">
          <uv-textarea autoHeight v-model="formData.improvementSuggestion" placeholder="请输入改进方案" maxlength="500" show-word-limit />
        </uv-form-item>
        <uv-form-item label="经验教训">
          <uv-textarea autoHeight v-model="formData.experienceLearned" placeholder="请输入经验教训" maxlength="500" show-word-limit />
        </uv-form-item>
      </view>

      <!-- 底部按钮 -->
      <view class="footer">
        <uv-button type="primary" block :loading="loading" :disabled="loading" @click="handleSubmit">
          {{ loading ? '创建中...' : '发起整改任务' }}
        </uv-button>
      </view>
    </uv-form>
  </view>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import PickerInput from '@/components/picker-input.vue'
import EmployeePicker from '@/components/employee-picker.vue'
import FilterTags from '@/components/filterTags.vue'
import taskApi from '@/api/violation/task.js'
import codeApi from '@/api/common/index.js'

onMounted(async () => {
  try {
    const type = await codeApi.getCode('CONTINUOUS_EXPERIENCE');
    formData.experienceCode = type;
  } catch (error) {
  }
});

const formData = reactive({
  title: '', // 任务名称 -> API的title字段
  experienceCode: '', // 编号
  experienceType: 'PROCESS_ISSUE', // 整改类型 -> API的experienceType字段
  experienceSource: 'AUDIT_FINDING', // 来源
  level: 'MIDDLE', // 风险等级
  sourceCode: '', // 来源编号
  dutyEmployeeOrgId: '', // 主责组织
  label: '', // 标签
  status: 'DRAFT', // 状态
  problemBackground: '', // 整改背景说明 -> API的problemBackground字段
  eventCourse: '', // 事件经过
  problemAnalysis: '', // 问题分析
  reasonCategory: '', // 原因分类
  experienceLearned: '', // 经验教训
  improvementSuggestion: '', // 改进方案 -> API的improvementSuggestion字段
  // investigateId: 0, // 保留的调查ID
  // 人员相关字段（页面显示用）
  dutyEmployeeId: '',
  dutyEmployeeName: '',
  dutyEmployeeOrgName: '',
  collaborationEmployeeId: '',
  collaborationEmployeeName: '',
  supervisionEmployeeId: '',
  supervisionEmployeeName: ''
})

const loading = ref(false)

// 风险等级选项
const riskLevels = [
  { name: '低', id: 'LOW' },
  { name: '中', id: 'MIDDLE' },
  { name: '高', id: 'HIGH' }
]

const correctionTypes = [
  { value: 'PROCESS_ISSUE', text: '流程问题' },
  { value: 'PERSONNEL_ISSUE', text: '人员问题' },
  { value: 'INSTITUTIONAL_ISSUE', text: '制度问题' },
  { value: 'SYSTEM_ISSUE', text: '系统问题' },
  { value: 'OTHER', text: '其他' }
]

const orgList = [
  { id: '1', name: '集团总部' },
  { id: '2', name: '财务部' },
  { id: '3', name: '人力资源部' },
  { id: '4', name: '采购部' },
  { id: '5', name: '市场部' },
  { id: '6', name: '技术部' }
]

function onDutyEmployeeSelect(employee) {
  formData.dutyEmployeeName = employee.name
  console.log('选择主责员工：', employee)
}

function onCollaborationEmployeeSelect(employee) {
  formData.collaborationEmployeeName = employee.name
  console.log('选择协作员工：', employee)
}

function onSupervisionEmployeeSelect(employee) {
  formData.supervisionEmployeeName = employee.name
  console.log('选择监督员工：', employee)
}

const handleSubmit = async () => {
  // 表单验证
  if (!formData.title) {
    uni.showToast({
      title: '请输入任务名称',
      icon: 'none'
    })
    return
  }

  if (!formData.experienceType) {
    uni.showToast({
      title: '请选择整改类型',
      icon: 'none'
    })
    return
  }

  if (!formData.dutyEmployeeOrgId) {
    uni.showToast({
      title: '请选择主责组织',
      icon: 'none'
    })
    return
  }

  loading.value = true

  try {
    // 构造提交数据
    const submitData = {
      title: formData.title,
      experienceCode: formData.experienceCode,
      experienceType: formData.experienceType,
      experienceSource: formData.experienceSource,
      level: formData.level,
      sourceCode: formData.sourceCode,
      dutyEmployeeOrgId: Number(formData.dutyEmployeeOrgId),
      status: formData.status,
      problemBackground: formData.problemBackground,
      eventCourse: formData.eventCourse,
      problemAnalysis: formData.problemAnalysis,
      reasonCategory: formData.reasonCategory,
      experienceLearned: formData.experienceLearned,
      improvementSuggestion: formData.improvementSuggestion,
      attachmentList: []
    }

    // 调用 API
    const result = await taskApi.createContinuousImprovementDeal(submitData)
    
    if (result && result.id) {
      uni.showToast({ 
        title: '持续改进任务创建成功', 
        icon: 'success',
        duration: 1500
      })
      
      // 延迟返回上级页面，并传递continuousImprovementId
      setTimeout(() => {
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        if (prevPage && prevPage.route.includes('processReport')) {
          prevPage.$vm.continuousImprovementId = result.id
        }
        uni.navigateBack()
      }, 1500)
    }
 
  } catch (error) {
    console.error('创建持续改进任务失败:', error)
    uni.showToast({
      title: '创建失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

onLoad(() => {
  // 如果有来源调查任务ID，可以在这里获取
  // const eventChannel = this.getOpenerEventChannel();
  // eventChannel.on('acceptDataFromOpenerPage', (data) => {
  //   this.investigateId = data.investigateId;
  // });
})
</script>

<style scoped>
.container {
  background: #f0f2f5;
  padding: 16px;
  min-height: 100vh;
}

.section {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}

.footer {
  padding: 16px 0;
  background: #fff;
}
</style>