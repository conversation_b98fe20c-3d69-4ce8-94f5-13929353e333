import http from '@/utils/request'
function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}

const baseUrl = `/compliancelistservice/api/`

const checklist = { 
     // 字典信息管理
    dict(params, key) {
        let ser = `/services/whiskerguardregulatoryservice/api/`
        switch (key) {
            case 'info':
            // return 
            default: //获取所有字典信息
                return request(
                    `${ser}dictionary/all`,
                    params, 'GET')
        }
    },
    //获取职责列表
    getDutyList(params, key) {
        switch (key) {
            case 'info':
            // return request(`/setting/info`, params)
            default:
                return request(
                    `${baseUrl}/duty_mains`,
                    params, 'GET')
        }
    },
}

export default checklist