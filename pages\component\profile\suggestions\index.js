import http from '@/utils/request'
function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
const baseUrl = '/services/whiskerguardorgservice/api/'
// 获取投诉与建议列表
export default {
    querySuggestions(params) {
        return request(`${baseUrl}complaint/suggestions`, params, 'get')
    },
    // 创建新的投诉建议
    createSuggestion(params) {
        return request(`${baseUrl}complaint/suggestions`, params, 'post')
    }
} 