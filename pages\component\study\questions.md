---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/考试管理

## GET 获取错题解析

GET /whiskerguardtrainingservice/api/exam/management/wrong/analysis/{examRecordId}

获取错题解析
{@code GET  /exam/management/wrong/analysis/{examRecordId}} : Get wrong question analysis.
获取错题解析
获取指定考试记录的错题解析

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|examRecordId|path|integer| 是 |考试记录ID 考试记录ID|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 500 Response

```json
[
  {
    "questionId": 0,
    "questionOrder": 0,
    "questionContent": "",
    "optionA": "",
    "optionB": "",
    "optionC": "",
    "optionD": "",
    "correctAnswer": "",
    "userAnswer": "",
    "questionAnalysis": "",
    "difficulty": ""
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **500**

*状态为 {@code 200 (OK)} 的响应实体，包含错题解析列表
获取错题解析成功
考试尚未完成
考试记录不存在
获取错题解析失败*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListWrongQuestionAnalysisDTO](#schemalistwrongquestionanalysisdto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含错题解析列表<br />获取错题解析成功<br />考试尚未完成<br />考试记录不存在<br />获取错题解析失败|
|» questionId|integer(int64)|false|none||题目ID|
|» questionOrder|integer|false|none||题目序号|
|» questionContent|string|false|none||题目内容|
|» optionA|string|false|none||选项A|
|» optionB|string|false|none||选项B|
|» optionC|string|false|none||选项C|
|» optionD|string|false|none||选项D|
|» correctAnswer|string|false|none||正确答案|
|» userAnswer|string|false|none||用户答案|
|» questionAnalysis|string|false|none||题目解析|
|» difficulty|string|false|none||题目难度|

# 数据模型

<h2 id="tocS_ListWrongQuestionAnalysisDTO">ListWrongQuestionAnalysisDTO</h2>

<a id="schemalistwrongquestionanalysisdto"></a>
<a id="schema_ListWrongQuestionAnalysisDTO"></a>
<a id="tocSlistwrongquestionanalysisdto"></a>
<a id="tocslistwrongquestionanalysisdto"></a>

```json
{
  "questionId": 0,
  "questionOrder": 0,
  "questionContent": "string",
  "optionA": "string",
  "optionB": "string",
  "optionC": "string",
  "optionD": "string",
  "correctAnswer": "string",
  "userAnswer": "string",
  "questionAnalysis": "string",
  "difficulty": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|questionId|integer(int64)|false|none||题目ID|
|questionOrder|integer|false|none||题目序号|
|questionContent|string|false|none||题目内容|
|optionA|string|false|none||选项A|
|optionB|string|false|none||选项B|
|optionC|string|false|none||选项C|
|optionD|string|false|none||选项D|
|correctAnswer|string|false|none||正确答案|
|userAnswer|string|false|none||用户答案|
|questionAnalysis|string|false|none||题目解析|
|difficulty|string|false|none||题目难度|

