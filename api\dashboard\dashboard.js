import http from '@/utils/request'

function request(url, params, method) {
    return http({
        url: url,
        method: method ? method : 'POST',
        data: params
    })
}
const baseUrl = `/services`
export default {
    getStatistics() {
        return request(`${baseUrl}/whiskerguardapprovalservice/api/todo/events/statistics`,
            {}, 'get')
    },
    // 获取我的岗位职责统计
    getDutyPositionStats() {
        return request(`${baseUrl}/compliancelistservice/api/duty/mains/my/stats`,
            {}, 'GET')
    },
    // 获取今日待办事件
    getTodoEvents() {
        return request(`${baseUrl}/whiskerguardapprovalservice/api/todo/events/today`,
            {}, 'GET')
    },
    // 获取指定员工集合的任务统计数据
    getDutyPositionStatsByIds(params) {
        return request(`${baseUrl}/compliancelistservice/api/duty/mains/stats`,
            params, 'POST')
    },
       // 获取用户学习进度概览
    getLearningProgressOverview(params = {}) {
        return request(
            `/services/whiskerguardtrainingservice/api/learning/progress/statistics/overview`,
            params, 'GET')
    },

}