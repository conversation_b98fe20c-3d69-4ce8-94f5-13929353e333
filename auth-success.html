<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>授权成功</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .success-container {
            background: white;
            border-radius: 16px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .success-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 50px;
            animation: successPulse 2s ease-in-out infinite;
        }
        
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .features {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .features h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .features h3::before {
            content: '🎉';
            margin-right: 8px;
        }
        
        .feature-list {
            list-style: none;
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .feature-list li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .feature-list li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            position: absolute;
            left: 0;
            width: 16px;
            height: 16px;
            background: #d4edda;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            flex: 1;
            border: none;
            border-radius: 12px;
            padding: 14px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1A73E8, #4285F4);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(26, 115, 232, 0.3);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 13px;
            color: #856404;
            text-align: left;
        }
        
        .tips-title {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .tips-title::before {
            content: '💡';
            margin-right: 6px;
        }
        
        .footer {
            font-size: 12px;
            color: #999;
            margin-top: 20px;
        }
        
        @media (max-width: 480px) {
            .success-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .subtitle {
                font-size: 14px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✓</div>
        <h1 class="title">授权成功！</h1>
        <p class="subtitle">您已成功授权微信服务号消息推送</p>
        
        <div class="features">
            <h3>您现在可以享受以下服务：</h3>
            <ul class="feature-list">
                <li>及时接收任务提醒和截止日期通知</li>
                <li>获取重要业务流程状态更新</li>
                <li>接收系统公告和政策变更提醒</li>
                <li>获得合规培训和考试通知</li>
                <li>接收紧急事件和风险预警</li>
            </ul>
        </div>
        
        <div class="tips">
            <div class="tips-title">温馨提示</div>
            <div>
                • 消息推送将通过微信服务号发送<br>
                • 您可以随时在设置中管理消息推送偏好<br>
                • 我们承诺不会发送垃圾信息或广告
            </div>
        </div>
        
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="backToApp()">返回应用</button>
            <button class="btn btn-secondary" onclick="closeWindow()">关闭页面</button>
        </div>
        
        <div class="footer">
            <p>感谢您的信任，我们将为您提供更好的服务体验</p>
        </div>
    </div>

    <script>
        // 返回应用
        function backToApp() {
            if (window.opener) {
                // 向父页面发送消息
                window.opener.postMessage({
                    type: 'wechat_auth_complete',
                    action: 'back_to_app'
                }, '*');
                window.close();
            } else {
                // 尝试跳转到小程序或应用
                const userAgent = navigator.userAgent.toLowerCase();
                if (userAgent.includes('micromessenger')) {
                    // 在微信中，可以尝试跳转到小程序
                    window.location.href = 'weixin://';
                } else {
                    // 其他情况，跳转到主页
                    window.location.href = '/';
                }
            }
        }
        
        // 关闭窗口
        function closeWindow() {
            if (window.opener) {
                window.close();
            } else {
                // 如果无法关闭，显示提示
                alert('请手动关闭此页面');
            }
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 向父页面发送授权成功消息
            if (window.opener) {
                window.opener.postMessage({
                    type: 'wechat_auth_success',
                    timestamp: Date.now()
                }, '*');
            }
            
            // 5秒后自动提示用户
            setTimeout(() => {
                if (confirm('授权已完成，是否返回应用？')) {
                    backToApp();
                }
            }, 5000);
        });
        
        // 监听来自父页面的消息
        window.addEventListener('message', function(event) {
            console.log('收到消息:', event.data);
            
            if (event.data.type === 'close_auth_page') {
                closeWindow();
            }
        });
        
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面重新可见时，可以刷新状态或发送消息
                console.log('授权成功页面重新可见');
            }
        });
    </script>
</body>
</html>
