<template>
	<view class="account-security-container">
		<view class="function-list">
			<view class="function-item" @click="showPasswordModal">
				<view class="function-text">修改密码</view>
				<view class="function-tag">
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view class="function-item" @click="showPhoneModal">
				<view class="function-text">手机号</view>
				<view class="function-tag">
					<view class="function-text-r">{{ userStore.userDetail.phone }}</view>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view class="function-item" @click="showEmailModal">
				<view class="function-text">邮箱</view>
				<view class="function-tag">
					<view class="function-text-r">{{ userStore.userDetail.email }}</view>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view class="function-item" @click="goToMessageAuth">
				<view class="function-text">小程序消息通知</view>
				<view class="function-tag">
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view class="function-item" @click="unbind">
				<view class="function-text">小程序解除绑定</view>
				<view class="function-tag">
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
		<!-- <view class="function-list">
			<view class="function-item">
				<view class="function-text">其他绑定</view>
				<view class="function-tag">
					<view class="function-text-r">138999999999</view>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view> -->

		<!-- 修改密码弹窗 -->
		<uv-modal ref="passwordModalRef" title="修改密码" :zoom="false" :showConfirmButton="false" :showCancelButton="false"
			@close="closeModal" :customStyle="{ padding: '20px 15px 15px 15px' }">
			<view class="modal-content">
				<uv-form ref="passwordFormRef" labelPosition="top" :model="passwordForm" :rules="passwordRules">
					<view class="input-group">
						<view class="input-label">当前密码</view>
						<uv-form-item prop="currentPassword">
							<uv-input v-if="!passwordVisible.currentPassword" v-model="passwordForm.currentPassword" placeholder="请输入当前密码" border="surround"
							type="password" :clearable="false">
							<template #suffix>
								<uv-icon name="eye-off"
									@click="togglePasswordVisible('currentPassword')"></uv-icon>
							</template>
						</uv-input>
						<uv-input v-else v-model="passwordForm.currentPassword" placeholder="请输入当前密码" border="surround"
							type="text" :clearable="false">
							<template #suffix>
								<uv-icon name="eye-fill"
									@click="togglePasswordVisible('currentPassword')"></uv-icon>
							</template>
						</uv-input>
						</uv-form-item>
					</view>
					<view class="input-group">
						<view class="input-label">新密码</view>
						<uv-form-item prop="newPassword">
							<uv-input v-if="!passwordVisible.newPassword" v-model="passwordForm.newPassword" placeholder="请输入新密码" border="surround"
							type="password" :clearable="false">
							<template #suffix>
								<uv-icon name="eye-off"
									@click="togglePasswordVisible('newPassword')"></uv-icon>
							</template>
						</uv-input>
						<uv-input v-else v-model="passwordForm.newPassword" placeholder="请输入新密码" border="surround"
							type="text" :clearable="false">
							<template #suffix>
								<uv-icon name="eye-fill"
									@click="togglePasswordVisible('newPassword')"></uv-icon>
							</template>
						</uv-input>
						</uv-form-item>
					</view>
					<view class="input-group">
						<view class="input-label">确认新密码</view>
						<uv-form-item prop="confirmPassword">
							<uv-input v-if="!passwordVisible.confirmPassword" v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" border="surround"
							type="password" :clearable="false">
							<template #suffix>
								<uv-icon name="eye-off"
									@click="togglePasswordVisible('confirmPassword')"></uv-icon>
							</template>
						</uv-input>
						<uv-input v-else v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" border="surround"
							type="text" :clearable="false">
							<template #suffix>
								<uv-icon name="eye-fill"
									@click="togglePasswordVisible('confirmPassword')"></uv-icon>
							</template>
						</uv-input>
						</uv-form-item>
					</view>
				</uv-form>

				<!-- 自定义按钮 -->
				<view class="modal-buttons">
					<uv-button type="info" size="normal" text="取消" @click="closeModal" class="cancel-btn"></uv-button>
					<uv-button type="primary" size="normal" text="确认" @click="submitPassword"
						class="confirm-btn"></uv-button>
				</view>
			</view>
		</uv-modal>

		<!-- 修改手机号弹窗 -->
		<uv-modal ref="phoneModalRef" title="修改手机号" :zoom="false" :showConfirmButton="false" :showCancelButton="false"
			@close="closePhoneModal" :customStyle="{ padding: '20px 15px 15px 15px' }">
			<view class="modal-content">
				<uv-form ref="phoneFormRef" labelPosition="top" :model="phoneForm" :rules="phoneRules">
					<view class="input-group">
						<view class="input-label">新手机号</view>
						<uv-form-item prop="phone">
							<uv-input v-model="phoneForm.phone" type="number" placeholder="请输入新手机号"
								border="surround"></uv-input>
						</uv-form-item>
					</view>
					<view class="input-group">
						<view class="input-label">验证码</view>
						<uv-form-item prop="verifyCode">
							<view class="code-input-wrapper">
								<uv-input v-model="phoneForm.verifyCode" type="number" placeholder="请输入验证码"
									border="surround" class="code-input"></uv-input>
								<uv-button type="primary" size="small"
									:text="phoneCountdown > 0 ? `${phoneCountdown}s后重新获取` : '获取验证码'"
									:disabled="phoneCountdown > 0" @click="getPhoneVerificationCode"
									class="get-code-btn"></uv-button>
							</view>
						</uv-form-item>
					</view>
				</uv-form>

				<!-- 自定义按钮 -->
				<view class="modal-buttons">
					<uv-button type="info" size="normal" text="取消" @click="closePhoneModal"
						class="cancel-btn"></uv-button>
					<uv-button type="primary" size="normal" text="确认" @click="submitPhone"
						class="confirm-btn"></uv-button>
				</view>
			</view>
		</uv-modal>

		<!-- 修改邮箱弹窗 -->
		<uv-modal ref="emailModalRef" title="修改邮箱" :zoom="false" :showConfirmButton="false" :showCancelButton="false"
			@close="closeEmailModal" :customStyle="{ padding: '20px 15px 15px 15px' }">
			<view class="modal-content">
				<uv-form ref="emailFormRef" labelPosition="top" :model="emailForm" :rules="emailRules">
					<view class="input-group">
						<view class="input-label">新邮箱</view>
						<uv-form-item prop="email">
							<uv-input v-model="emailForm.email" type="text" placeholder="请输入新邮箱"
								border="surround"></uv-input>
						</uv-form-item>
					</view>
				</uv-form>

				<!-- 自定义按钮 -->
				<view class="modal-buttons">
					<uv-button type="info" size="normal" text="取消" @click="closeEmailModal"
						class="cancel-btn"></uv-button>
					<uv-button type="primary" size="normal" text="确认" @click="submitEmail"
						class="confirm-btn"></uv-button>
				</view>
			</view>
		</uv-modal>
	</view>
</template>
<script setup>
import { ref } from 'vue';
import { useUserStore } from '@/store/pinia.js';
import profileApi from './index.js';

const userStore = useUserStore();

// 弹窗引用
const passwordModalRef = ref(null);
const passwordFormRef = ref(null);
const phoneModalRef = ref(null);
const phoneFormRef = ref(null);
const emailModalRef = ref(null);
const emailFormRef = ref(null);

// 密码表单数据
const passwordForm = ref({
	currentPassword: '',
	newPassword: '',
	confirmPassword: ''
});

// 手机号表单数据
const phoneForm = ref({
	phone: '',
	verifyCode: ''
});

// 手机号验证码倒计时
const phoneCountdown = ref(0);
let phoneTimer = null;

// 邮箱表单数据
const emailForm = ref({
	email: ''
});

// 密码可见性控制
const passwordVisible = ref({
	currentPassword: false,
	newPassword: false,
	confirmPassword: false
});

// 切换密码可见性
const togglePasswordVisible = (field) => {
	passwordVisible.value[field] = !passwordVisible.value[field];
};

// 表单验证规则
const passwordRules = ref({
	currentPassword: [
		{ required: true, message: '请输入当前密码', trigger: 'blur' }
	],
	newPassword: [
		{ required: true, message: '请输入新密码', trigger: 'blur' },
		{ min: 6, message: '新密码长度不能少于6位', trigger: 'blur' }
	],
	confirmPassword: [
		{ required: true, message: '请再次输入新密码', trigger: 'blur' },
		{
			validator: (rule, value, callback) => {
				if (value !== passwordForm.value.newPassword) {
					callback(new Error('两次输入的密码不一致'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		}
	]
});

// 手机号表单验证规则
const phoneRules = ref({
	phone: [
		{ required: true, message: '请输入手机号', trigger: 'blur' },
		{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
	],
	verifyCode: [
		{ required: true, message: '请输入验证码', trigger: 'blur' },
		{ len: 6, message: '验证码长度为6位', trigger: 'blur' }
	]
});

// 邮箱表单验证规则
const emailRules = ref({
	email: [
		{ required: true, message: '请输入邮箱', trigger: 'blur' },
		{ pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的邮箱格式', trigger: 'blur' }
	]
});

// 显示修改密码弹窗
const showPasswordModal = () => {
	passwordModalRef.value.open();
};

// 关闭修改密码弹窗
const closeModal = () => {
	passwordModalRef.value.close();
	// 清空表单
	passwordForm.value = {
		currentPassword: '',
		newPassword: '',
		confirmPassword: ''
	};
	// 重置表单验证状态
	passwordFormRef.value?.resetFields();
};

// 提交密码修改（第一次确认）
const submitPassword = async () => {
	try {
		// 使用uv-form验证
		await passwordFormRef.value.validate();

		// 验证通过，显示确认弹窗
		uni.showModal({
			title: '确认修改',
			content: '确定要修改密码吗？',
			success: (res) => {
				if (res.confirm) {
					// 用户点击确定，执行密码修改
					confirmChangePassword();
				}
				// 用户点击取消，不做任何操作
			}
		});
	} catch (error) {
		// 验证失败，弹窗不关闭，uv-form会自动显示错误信息
		console.log('表单验证失败:', error);
		// 不关闭弹窗，让用户修正输入
	}
};

// 确认修改密码（调用API）
const confirmChangePassword = async () => {
	try {
		uni.showLoading({
			title: '修改中...'
		});

		const params = {
			currentPassword: passwordForm.value.currentPassword,
			newPassword: passwordForm.value.newPassword
		};

		const result = await profileApi.updatePwd(userStore.userDetail.id, params);

		uni.hideLoading();
		uni.showToast({
			title: '密码修改成功',
			icon: 'success'
		});

		// 关闭密码弹窗
		passwordModalRef.value.close();

		// 清空表单
		passwordForm.value = {
			currentPassword: '',
			newPassword: '',
			confirmPassword: ''
		};
		// 重置表单验证状态
		passwordFormRef.value?.resetFields();

		// 延迟跳转到登录页面
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/login/login'
			});
		}, 1500);

	} catch (error) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || '修改失败，请重试',
			icon: 'none'
		});
	}
};

// 显示修改手机号弹窗
const showPhoneModal = () => {
	phoneModalRef.value.open();
};

// 关闭修改手机号弹窗
const closePhoneModal = () => {
	phoneModalRef.value.close();
	// 清空表单
	phoneForm.value = {
		phone: '',
		verifyCode: ''
	};
	// 重置表单验证状态
	phoneFormRef.value?.resetFields();
	// 清除倒计时
	if (phoneTimer) {
		clearInterval(phoneTimer);
		phoneTimer = null;
	}
	phoneCountdown.value = 0;
};

// 获取手机号验证码
const getPhoneVerificationCode = async () => {
	if (!phoneForm.value.phone) {
		uni.showToast({
			title: '请先输入手机号',
			icon: 'none'
		});
		return;
	}

	// 验证手机号格式
	const phonePattern = /^1[3-9]\d{9}$/;
	if (!phonePattern.test(phoneForm.value.phone)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return;
	}

	try {
		uni.showLoading({
			title: '发送中...'
		});

		// 这里可以调用发送验证码的API
		// await profileApi.sendVerificationCode(phoneForm.value.phone);

		uni.hideLoading();
		uni.showToast({
			title: '验证码已发送',
			icon: 'success'
		});

		// 开始倒计时
		startPhoneCountdown();

	} catch (error) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || '发送失败，请重试',
			icon: 'none'
		});
	}
};

// 开始手机号验证码倒计时
const startPhoneCountdown = () => {
	phoneCountdown.value = 60;
	phoneTimer = setInterval(() => {
		phoneCountdown.value--;
		if (phoneCountdown.value <= 0) {
			clearInterval(phoneTimer);
			phoneTimer = null;
		}
	}, 1000);
};

// 提交手机号修改
const submitPhone = async () => {
	try {
		// 使用uv-form验证
		await phoneFormRef.value.validate();

		// 验证通过，显示确认弹窗
		uni.showModal({
			title: '确认修改',
			content: '确定要修改手机号吗？',
			success: (res) => {
				if (res.confirm) {
					// 用户点击确定，执行手机号修改
					confirmChangePhone();
				}
				// 用户点击取消，不做任何操作
			}
		});
	} catch (error) {
		// 验证失败，弹窗不关闭，uv-form会自动显示错误信息
		console.log('表单验证失败:', error);
		// 不关闭弹窗，让用户修正输入
	}
};

// 确认修改手机号（调用API）
const confirmChangePhone = async () => {
	try {
		uni.showLoading({
			title: '修改中...'
		});

		const params = {
			phone: phoneForm.value.phone,
			verifyCode: phoneForm.value.verifyCode
		};

		const result = await profileApi.updatePhone(params);

		uni.hideLoading();
		uni.showToast({
			title: '手机号修改成功',
			icon: 'success'
		});

		// 关闭手机号弹窗
		closePhoneModal();

		// 更新用户信息
		userStore.userDetail.phone = phoneForm.value.phone;

	} catch (error) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || '修改失败，请重试',
			icon: 'none'
		});
	}
};

// 显示修改邮箱弹窗
const showEmailModal = () => {
	emailModalRef.value.open();
};

// 关闭修改邮箱弹窗
const closeEmailModal = () => {
	emailModalRef.value.close();
	// 清空表单
	emailForm.value = {
		email: ''
	};
	// 重置表单验证状态
	emailFormRef.value?.resetFields();
};

// 提交邮箱修改
const submitEmail = async () => {
	try {
		// 使用uv-form验证
		await emailFormRef.value.validate();

		// 验证通过，显示确认弹窗
		uni.showModal({
			title: '确认修改',
			content: '确定要修改邮箱吗？',
			success: (res) => {
				if (res.confirm) {
					// 用户点击确定，执行邮箱修改
					confirmChangeEmail();
				}
				// 用户点击取消，不做任何操作
			}
		});
	} catch (error) {
		// 验证失败，弹窗不关闭，uv-form会自动显示错误信息
		console.log('表单验证失败:', error);
		// 不关闭弹窗，让用户修正输入
	}
};

// 确认修改邮箱（调用API）
const confirmChangeEmail = async () => {
	try {
		uni.showLoading({
			title: '修改中...'
		});

		const params = {
			email: emailForm.value.email
		};

		const result = await profileApi.updateEmail(userStore.userDetail.id, params);

		uni.hideLoading();
		uni.showToast({
			title: '邮箱修改成功',
			icon: 'success'
		});

		// 关闭邮箱弹窗
		closeEmailModal();

		// 更新用户信息
		userStore.userDetail.email = emailForm.value.email;

	} catch (error) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || '修改失败，请重试',
			icon: 'none'
		});
	}
};

// 跳转到小程序消息通知设置
const goToMessageAuth = () => {
	uni.navigateTo({
		url: '/pages/component/profile/subscribe'
	});
};

// 小程序解除绑定
const unbind = () => {
	uni.showModal({
		title: '解除绑定',
		content: '解除绑定后，您将无法接收到小程序的消息推送，确定要解除绑定吗？',
		success: (res) => {
			if (res.confirm) {
				// 用户确认解除绑定
				confirmUnbind();
			}
		}
	});
};

// 确认解除绑定
const confirmUnbind = async () => {
	try {
		uni.showLoading({
			title: '解除绑定中...'
		});

		// 这里可以调用解除绑定的API
		// await profileApi.unbindMiniProgram();

		uni.hideLoading();
		uni.showToast({
			title: '解除绑定成功',
			icon: 'success'
		});

		// 延迟跳转到登录页面
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/login/login'
			});
		}, 1500);

	} catch (error) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || '解除绑定失败，请重试',
			icon: 'none'
		});
	}
};
</script>
<style lang="scss" scoped>
.account-security-container {
	background-color: #f8f8f8;
	height: 100vh;
}

.function-container {
	padding: 32rpx;
}

.function-title {
	font-weight: 600;
	padding: 32rpx 0;
}

.function-list {
	display: flex;
	flex-direction: column;
	margin-bottom: 15rpx;
}

.function-item {
	height: 112rpx;
	background-color: #fff;
	padding: 0 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 1rpx;
}

.function-name {
	flex: 1;
	margin-left: 32rpx;
	font-size: 32rpx;
	color: #444444;
}

.function-text {
	font-size: 32rpx;
	color: #444444;
}

.function-tag {
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 30rpx;
}

.function-text-r {
	color: #333333;
	font-size: 30rpx;
	font-weight: normal;
	padding-right: 10rpx;
}

// 弹窗样式
.modal-content {
	padding: 0 12rpx;
	width: 100%;
}

.input-group {
	margin-bottom: 30rpx;
}

.input-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.modal-buttons {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 48rpx;
	gap: 24rpx;
}

.cancel-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 12rpx;
}

.confirm-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 12rpx;
}

/* 验证码输入区样式 */
.code-input-wrapper {
	display: flex;
	gap: 16rpx;
	align-items: center;
}

.code-input {
	flex: 1;
}

.get-code-btn {
	width: 240rpx;
	height: 80rpx;
	font-size: 24rpx;
	font-weight: 500;
	border-radius: 12rpx;
	line-height: 1;
}
</style>