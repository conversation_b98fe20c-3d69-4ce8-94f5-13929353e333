<template>
  <view class="study-plan-container">
    <!-- Top Navigation Bar -->
    <header-bar :showIpt="false">
      <template #right>
        <view class="date-picker" @click="showMonthPicker = true">
          <uni-icons type="calendar" size="16" color="#1A73E8"></uni-icons>
          <text class="date-text">{{ currentYear }}-{{ currentMonth + 1 | padStart }}</text>
          <uni-icons type="down" size="12" color="#1A73E8"></uni-icons>
        </view>
      </template>
    </header-bar>
    <!-- <view class="nav-bar">
        <view class="nav-icon" @click="handlePrevMonth">
          <uni-icons type="left" size="20" color="#606266"></uni-icons>
        </view>
        <text class="nav-title">学习计划</text>
        <view class="date-picker" @click="showMonthPicker = true">
          <uni-icons type="calendar" size="16" color="#1A73E8"></uni-icons>
          <text class="date-text">{{ currentYear }}-{{ currentMonth + 1 | padStart }}</text>
          <uni-icons type="down" size="12" color="#1A73E8"></uni-icons>
        </view>
        <view class="nav-icon" @click="handleNextMonth">
          <uni-icons type="right" size="20" color="#606266"></uni-icons>
        </view>
      </view> -->

    <!-- Month Picker -->
    <!-- <view class="month-picker">
          <view class="picker-header">
            <text class="picker-title">选择月份</text>
            <uni-icons type="close" size="20" color="#909399" @click="showMonthPicker = false"></uni-icons>
          </view>
          <picker-view class="picker-body" :value="[currentYear - 2020, currentMonth]" @change="handleMonthChange">
            <picker-view-column>
              <view class="picker-item" v-for="year in years" :key="year">{{ year }}年</view>
            </picker-view-column>
            <picker-view-column>
              <view class="picker-item" v-for="month in 12" :key="month">{{ month }}月</view>
            </picker-view-column>
          </picker-view>
        </view> -->

    <!-- Main Content -->
    <view class="content">
      <!-- Calendar Section -->
      <view class="calendar-section">
        <view class="weekdays">
          <text class="weekday" v-for="day in weekdays" :key="day">{{ day }}</text>
        </view>
        <view class="days-grid">
          <view class="day-item" v-for="(day, index) in calendarDays" :key="index" :class="{
            'other-month': !day.isCurrentMonth,
            'current-day': day.isToday,
            'selected-day': day.isSelected
          }" @click="selectDay(day)">
            <text class="day-number">{{ day.date }}</text>
            <view class="day-dot" v-if="day.hasTask"></view>
          </view>
        </view>
      </view>

      <!-- Today's Tasks -->
      <view class="tasks-section">
        <text class="section-title">今日计划：{{ selectedDayTasks.length }} 节</text>
        <view class="task-list">
          <view class="task-item" v-for="(task, index) in selectedDayTasks" :key="index">
            <text class="task-bullet">•</text>
            <text class="task-text">{{ task }}</text>
          </view>
        </view>
        <button  class="start-button" type="primary" v-if="selectedDayTasks.length > 0">开始学习</button>
        <view class="empty-tips" v-else>
          <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
          <text class="empty-text">今日无学习计划</text>
        </view>
      </view>

      <!-- Future Plans -->
      <view class="future-plans">
        <view class="plan-item" v-for="(plan, index) in futurePlans" :key="index">
          <view class="plan-header">
            <text class="plan-date">{{ plan.date | formatDate }} {{ plan.day }}</text>
            <uni-icons type="right" size="14" color="#C0C4CC"></uni-icons>
          </view>
          <view class="plan-content">
            <text class="task-bullet">•</text>
            <text class="plan-text">{{ plan.task }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import headerBar from '@/components/headerBar.vue'
import { ref, computed } from 'vue';

// 星期显示
const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

// 当前日期控制
const currentDate = ref(new Date());
const currentYear = ref(currentDate.value.getFullYear());
const currentMonth = ref(currentDate.value.getMonth());
const selectedDate = ref(new Date());
const showMonthPicker = ref(false);

// 年份范围
const years = computed(() => {
  const startYear = 2020;
  const endYear = 2030;
  return Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
});

// 生成日历数据
const calendarDays = computed(() => {
  const days = [];
  const firstDay = new Date(currentYear.value, currentMonth.value, 1);
  const lastDay = new Date(currentYear.value, currentMonth.value + 1, 0);

  // 上个月补全天数
  const prevMonthDays = firstDay.getDay();
  for (let i = prevMonthDays - 1; i >= 0; i--) {
    const date = new Date(currentYear.value, currentMonth.value, -i);
    days.push({
      date: date.getDate(),
      fullDate: date,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date()),
      isSelected: isSameDay(date, selectedDate.value),
      hasTask: hasTaskForDate(date)
    });
  }

  // 本月天数
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(currentYear.value, currentMonth.value, i);
    days.push({
      date: i,
      fullDate: date,
      isCurrentMonth: true,
      isToday: isSameDay(date, new Date()),
      isSelected: isSameDay(date, selectedDate.value),
      hasTask: hasTaskForDate(date)
    });
  }

  // 下个月补全天数
  const nextMonthDays = 6 - lastDay.getDay();
  for (let i = 1; i <= nextMonthDays; i++) {
    const date = new Date(currentYear.value, currentMonth.value + 1, i);
    days.push({
      date: i,
      fullDate: date,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date()),
      isSelected: isSameDay(date, selectedDate.value),
      hasTask: hasTaskForDate(date)
    });
  }

  return days;
});

// 模拟任务数据
const taskData = {
  '2025-04-04': [
    '1-1. 课程导入：企业合规管理概述',
    '2-1. 风险清单介绍与编制方法',
    '3-2. 合规审查流程实操演练'
  ],
  '2025-04-10': [
    '1-2. 合规管理体系构建',
    '2-2. 风险识别与评估'
  ],
  '2025-04-20': [
    '合规审查流程（2 课时）'
  ],
  '2025-04-21': [
    '三张清单管理（1 课时）'
  ],
  '2025-04-22': [
    '合规风险评估（1.5 课时）'
  ],
  '2025-04-23': [
    '合规培训与文化建设（1 课时）'
  ]
};

// 未来计划数据
const futurePlans = [
  { date: '2025-04-20', day: '周日', task: '合规审查流程（2 课时）' },
  { date: '2025-04-21', day: '周一', task: '三张清单管理（1 课时）' },
  { date: '2025-04-22', day: '周二', task: '合规风险评估（1.5 课时）' },
  { date: '2025-04-23', day: '周三', task: '合规培训与文化建设（1 课时）' }
];

// 计算选中的日期任务
const selectedDayTasks = computed(() => {
  const dateStr = formatDate(selectedDate.value);
  return taskData[dateStr] || [];
});

// 辅助函数：判断是否是同一天
const isSameDay = (date1, date2) => {
  return date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate();
};

// 辅助函数：格式化日期
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 辅助函数：判断日期是否有任务
const hasTaskForDate = (date) => {
  const dateStr = formatDate(date);
  return taskData[dateStr] && taskData[dateStr].length > 0;
};

// 选择日期
const selectDay = (day) => {
  selectedDate.value = day.fullDate;
};

// 上个月
const handlePrevMonth = () => {
  if (currentMonth.value === 0) {
    currentYear.value--;
    currentMonth.value = 11;
  } else {
    currentMonth.value--;
  }
};

// 下个月
const handleNextMonth = () => {
  if (currentMonth.value === 11) {
    currentYear.value++;
    currentMonth.value = 0;
  } else {
    currentMonth.value++;
  }
};

// 月份选择变化
const handleMonthChange = (e) => {
  const [yearIndex, monthIndex] = e.detail.value;
  currentYear.value = 2020 + yearIndex;
  currentMonth.value = monthIndex;
  showMonthPicker.value = false;
};

// 过滤器：格式化日期
const formatDateFilter = (dateStr) => {
  const date = new Date(dateStr);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
};

// 过滤器：补零
const padStartFilter = (value) => {
  return String(value).padStart(2, '0');
};

// 暴露过滤器
defineExpose({
  filters: {
    formatDate: formatDateFilter,
    padStart: padStartFilter
  }
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F5F7FA;
}

/* Navigation Bar */
.nav-bar {
  height: 88rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  background-color: #F0F7FF;
}

.date-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1A73E8;
  margin: 0 8rpx;
}

/* Month Picker */
.month-picker {
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  height: 60vh;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}

.picker-body {
  height: calc(100% - 80rpx);
}

.picker-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #303133;
}

/* Main Content */
.content {
  flex: 1;
  padding: 16rpx 32rpx;
  box-sizing: border-box;
  margin-top: 88rpx;
}

/* Calendar Section */
.calendar-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.weekdays {
  display: flex;
  margin-bottom: 16rpx;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 24rpx;
  color: #909399;
  min-width: 0;
  /* 防止挤压 */
}

.days-grid {
  display: flex;
  flex-wrap: wrap;
}

.day-item {
  width: calc(100% / 7);
  aspect-ratio: 1;
  /* 保持正方形 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.day-number {
  font-size: 28rpx;
  color: inherit;
  margin-bottom: 4rpx;
}

/* 其他样式保持不变 */
.day-item.other-month {
  color: #C0C4CC;
}

.day-item.current-day {
  color: #1A73E8;
  font-weight: bold;
}

.day-item.selected-day {
  background-color: #E6F4FF;
  border-radius: 50%;
}

.day-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #1A73E8;
  position: absolute;
  bottom: 12rpx;
}

/* Future Plans */
.future-plans {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.plan-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.plan-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #303133;
}

.plan-content {
  display: flex;
  align-items: center;
}

.plan-text {
  font-size: 28rpx;
  color: #303133;
}
</style>