<!-- picker.vue -->
<template>
  <view>
    <uv-input
      v-model="selectedLabel"
      @tap="handleClick"
      :suffix-icon="isPickerVisible ? 'arrow-up' : 'arrow-down'"
      :placeholder="placeholder"
      readonly
    ></uv-input>

    <uv-picker
      ref="pickerRef"
      :columns="columns"
      :keyName="displayKey"
      :valueKey="valueKey"
      @confirm="handleConfirm"
      @close="handleClose"
    ></uv-picker>
  </view>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, defineExpose, computed } from 'vue'

const props = defineProps({
  // 数据源
  columns: {
    type: Array,
    default: () => []
  },
  // 显示使用的字段名
  displayKey: {
    type: String,
    default: 'label'
  },
  // 值使用的字段名
  valueKey: {
    type: String,
    default: 'id'
  },
  // 其他原有props...
  placeholder: {
    type: String,
    default: '请选择'
  },
  border: {
    type: Boolean,
    default: true
  },
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'close'])

const pickerRef = ref(null)
const isPickerVisible = ref(false)
const selectedLabel = ref('')

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  const item = props.columns.flat().find(i => i[props.valueKey] === newVal)
  selectedLabel.value = item ? item[props.displayKey] : ''
})

// 监听columns变化，当数据源更新时重新查找显示标签
watch(() => props.columns, () => {
  if (props.modelValue) {
    const item = props.columns.flat().find(i => i[props.valueKey] === props.modelValue)
    selectedLabel.value = item ? item[props.displayKey] : ''
  }
}, { deep: true })

// 暴露方法
defineExpose({ 
  open: () => pickerRef.value?.open(),
  close: () => pickerRef.value?.close()
})

const handleClick = () => {
  pickerRef.value?.open()
  isPickerVisible.value = true
}

const handleConfirm = (item) => {
  pickerRef.value?.close()
  isPickerVisible.value = false
  const selectedItem = item.value[0] // 获取第一列选中项
  selectedLabel.value = selectedItem[props.displayKey]
  // selectedLabel.value = item.value[0][props.displayKey] // 使用动态key
  emit('update:modelValue', selectedItem[props.valueKey])
  emit('confirm', selectedItem)
}

const handleClose = () => {
  isPickerVisible.value = false
  emit('close')
}

// 初始化显示
const findLabel = computed(() => {
  return props.columns.flat().find(item => item[props.valueKey] === props.modelValue)?.[props.displayKey] || ''
})
selectedLabel.value = findLabel.value
</script>