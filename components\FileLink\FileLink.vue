<template>
  <view class="file-link-container">
    <text class="file-name" @click="handlePreview">{{ displayFileName }}</text>
  </view>
</template>

<script setup>
import uploads from '@/api/upload.js'
import { computed } from 'vue'

// 定义props
const props = defineProps({
  fileUrl: {
    type: String,
    required: true,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 计算显示的文件名
const displayFileName = computed(() => {
  if (props.fileName) {
    return props.fileName
  }
  if (props.fileUrl) {
    const urlParts = props.fileUrl.split('/')
    return urlParts[urlParts.length - 1] || '未知文件'
  }
  return '未知文件'
})

// 文件预览功能
const handlePreview = async () => {
  const filePath = props.fileUrl
  if (!filePath) {
    uni.showToast({ title: '文件路径不存在', icon: 'none' })
    return
  }

  try {
    const fileUrl = await uploads.getFileUrl(filePath)
    if (!fileUrl) {
      uni.showToast({ title: '文件路径不存在', icon: 'none' })
      return
    }

    // 根据文件扩展名判断文件类型
    const fileExtension = filePath.split('.').pop()?.toLowerCase()
    const previewableTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif']

    if (!fileExtension || !previewableTypes.includes(fileExtension)) {
      uni.showToast({ title: '该文件类型不支持预览', icon: 'none' })
      return
    }

    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
      uni.previewImage({ urls: [fileUrl], current: fileUrl })
    } else {
      uni.showLoading({ title: '加载中...' })
      uni.downloadFile({
        url: fileUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: fileExtension,
              showMenu: true,
              success: () => {
                if (fileExtension === 'txt') {
                  uni.showToast({ title: 'txt文件预览成功', icon: 'success' })
                }
              },
              fail: () => {
                uni.showToast({ title: '预览失败', icon: 'none' })
              }
            })
          }
          uni.hideLoading()
        },
        fail: () => {
          uni.hideLoading()
          uni.showToast({ title: '文件下载失败', icon: 'none' })
        }
      })
    }
  } catch (error) {
    console.error('文件预览失败:', error)
    uni.showToast({ title: '文件预览失败', icon: 'none' })
  }
}
</script>

<style lang="scss" scoped>
.file-link-container {
  display: inline-block;
}

.file-name {
  color: #1A73E8;
  cursor: pointer;
  font-size: 28rpx;
  text-decoration: none;
}

.file-name:hover {
  opacity: 0.8;
  text-decoration: underline;
}
</style>