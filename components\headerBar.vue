<!-- search-header.vue -->
<template>
	<view class="search-header" @tap="handleSearch" :style="{ top: computedTop,position: fixed?'fixed':'relative' }">
		<!-- 搜索输入区域 -->
			<!-- <view class="search-icon">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
			</view>
			<input class="search-input" :placeholder="placeholder" :value="value"
				@input="$emit('update:modelValue', $event.detail.value)" @confirm="$emit('search', value)"
				placeholder-class="placeholder" /> -->
			<uv-input v-if="showIpt" :placeholder="props.placeholder"  v-model="inputValue" v-bind="attrs" @confirm="$emit('confirm')"></uv-input>
		<!-- 右侧操作区域 -->
		<view class="action-area">
			<slot name="right"></slot>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		useAttrs,
		defineEmits
	} from 'vue';
	const props = defineProps({
		value: {
			type: String,
			default: ''
		},
		showIpt: {
			type: Boolean,
			default: true
		},
		placeholder: {
			type: String,
			default: '请输入搜索内容'
		},
		top: {
			type: Number,
			default: 0
		},
		fixed: {
			type: Boolean,
			default: true
		},
		
	});
	const emit = defineEmits(['update:modelValue', 'confirm', 'search', 'back', 'handleSearch']);
	const attrs = useAttrs();
	const inputValue = computed({
		get: ()=> props.modelValue,
		set: (val) => {
			emit('update:modelValue', val);
		}
	})
	const computedTop = computed(() => {
		return `${props.top}rpx`;
	});
	function handleSearch() {
		emit('handleSearch');
	}
</script>

<style lang="scss" scoped>
	.search-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;
		display: flex;
		align-items: center;
		height: 88rpx;
		padding: 10rpx 24rpx;
		margin: 12rpx 0;
		// background: #ffffff;
		// box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);

		// .search-input-wrapper {
		// 	flex: 1;
		// 	position: relative;
		// 	height: 64rpx;
		// 	background: #f5f5f5;
		// 	border-radius: 32rpx;
		// 	transition: all 0.3s ease;

		// 	&:active {
		// 		background: #eee;
		// 	}

		// 	.search-icon {
		// 		position: absolute;
		// 		left: 24rpx;
		// 		top: 50%;
		// 		transform: translateY(-50%);
		// 		z-index: 2;
		// 	}

		// 	.search-input {
		// 		width: 100%;
		// 		height: 100%;
		// 		padding: 0 72rpx 0 64rpx;
		// 		font-size: 28rpx;
		// 		color: #333;
		// 		box-sizing: border-box;

		// 		&-focus {
		// 			background: #fff;
		// 			box-shadow: 0 0 0 2rpx #2979FF;
		// 		}
		// 	}

		// 	.placeholder {
		// 		color: #999;
		// 		font-size: 28rpx;
		// 	}

		// 	&.hideIpt {
		// 		visibility: hidden;
		// 	}
		// }

		.action-area {
			flex-shrink: 0;
			display: flex;
			align-items: center;
			gap: 24rpx;
			margin-left: 24rpx;

			/* 操作按钮基础样式 */
			.action-btn {
				padding: 8rpx 16rpx;
				font-size: 28rpx;
				color: #2979FF;
				border-radius: 8rpx;
				transition: all 0.2s ease;

				&:active {
					background: rgba(41, 121, 255, 0.1);
				}
			}
		}
	}
</style>