<template>
	<view class="lists-container">
		<z-paging ref="paging" :auto="false" v-model="dataList" @query="queryList" :loading="loading">
			<template #top>
				<header-bar title="我的收藏" shape="circle" prefixIcon="search" clearable :fixed="false"
					v-model="searchValue" @search="search()">
					<template #right>
						<view class="nav-right">
							<view class="nav-btn" @click.stop="searchClick">
								<uni-icons type="search" size="24" color="#666666"></uni-icons>
							</view>
						</view>
					</template>
				</header-bar>
				<filter-tags v-model="tagValue" :fixed="false" :tags="tags" valueKey="value"
					@change="handleTagChange" />
			</template>
			<view class="collect-list pl-32 pr-32">
				<view class="list-item" v-for="(item, index) in dataList" :key="index"
					@click="viewDetail(item)">
					<view class="item-content">
						<text class="item-title">{{ item.regulationTitle }}</text>
						<view class="item-info">
							<text class="info-text" :class="getTypeClass(item.type)">
								{{ getTypeName(item.type) }}
							</text>
							<text class="info-tig" style="margin-left: 20rpx;">{{ item.createdBy }}</text>
							<text class="info-tig" style="margin-left: 20rpx;">{{ item.createdAt }}</text>
						</view>
					</view>
					<uni-icons type="right" size="16" color="#999" />
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import {
	ref,
	nextTick,
	onMounted
} from 'vue';
import {
	useUserStore
} from '@/store/pinia.js';
import headerBar from '@/components/headerBar.vue';
import filterTags from '@/components/filterTags.vue'
import profileApi from './index.js'

const loading = ref(false)
const userStore = useUserStore();
const tagValue = ref(null);
const tags = ref([{
	name: '全部',
	value: null
}, {
	name: '内部制度',
	value: 1
}, {
	name: '法规',
	value: 2
}]);

// 跳转到详情页面
const viewDetail = (item) => {
	console.log('查看详情:', item);
	if (item.type === 1) {
		// 内部制度
		uni.navigateTo({
			url: `/pages/component/regulations/detail?id=${item.regulationId}`
		})
	} else if (item.type === 2) {
		// 法规
		uni.navigateTo({
			url: `/pages/component/regulations/regulationDetail?id=${item.regulationId}`
		})
	}
}

const handleTagChange = (index) => {
	reload();
}

function reload() {
	nextTick(() => {
		paging?.value.reload();
	})
}

const searchValue = ref("") //
const paging = ref(null)
const dataList = ref([])

function searchClick() {
	reload();
}

onMounted(async () => {
	reload();
});

// 获取类型名称
function getTypeName(type) {
	switch (type) {
		case 1:
			return '内部制度';
		case 2:
			return '法规';
		default:
			return '未知';
	}
}

// 获取类型样式类名
function getTypeClass(type) {
	switch (type) {
		case 1:
			return 'type-internal';
		case 2:
			return 'type-regulation';
		default:
			return 'type-unknown';
	}
}

// 列表查询
const queryList = (pageNo, pageSize) => {
	uni.showLoading({
		title: '加载中...',
		mask: true
	});
	console.log('tagValue.value', tagValue.value)
	var params = {
		page: pageNo - 1,
		size: pageSize
	}
	if(tagValue.value){
		params.type = tagValue.value;
	}

	profileApi.queryCollects(params).then((res) => {
		let arr = res.content || []
		// 根据类型过滤
		if (tagValue.value !== null) {
			arr = arr.filter(item => item.type === tagValue.value);
		}
		// 根据搜索关键词过滤
		if (searchValue.value) {
			arr = arr.filter(item => 
				item.regulationTitle && 
				item.regulationTitle.toLowerCase().includes(searchValue.value.toLowerCase())
			);
		}
		console.log('收藏列表', arr)
		paging.value.complete(arr);
		uni.hideLoading();
	}).catch((err) => {
		console.error('获取收藏列表失败:', err);
		paging.value.complete(false);
		uni.hideLoading();
	})
}

// 暴露变量供其他页面访问
defineExpose({
	reload
});
</script>

<style lang="scss" scoped>
.lists-container {
	/* Content Styles */
	.collect-list {
		display: flex;
		flex-direction: column;
		margin-top: 20rpx;
	}

	.list-item {
		background-color: white;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		display: flex;
		margin-bottom: 16rpx;
		justify-content: space-between;
		align-items: center;
	}

	.item-title {
		font-size: 32rpx;
		font-weight: normal;
		color: #1a1a1a;
		margin-bottom: 8rpx;
	}

	.item-info {
		display: flex;
		align-items: center;
		font-size: 24rpx;
	}

	.info-text {
		display: inline-block;
		padding: 6rpx 16rpx;
		color: white;
		font-weight: normal;
		border-radius: 6rpx;
		text-align: center;
		font-size: 24rpx;
	}

	/* 内部制度样式 - 蓝色背景 */
	.info-text.type-internal {
		background-color: rgb(230, 247, 255);
		color: #1890ff;
	}

	/* 法规样式 - 绿色背景 */
	.info-text.type-regulation {
		background-color: rgb(230, 255, 250);
		color: rgb(49, 151, 149);
	}

	/* 未知类型样式 - 灰色背景 */
	.info-text.type-unknown {
		background-color: rgb(245, 245, 245);
		color: #666666;
	}

	.info-tig {
		font-size: 24rpx;
		color: $uni-text-color-grey;
	}
}
</style>