<template>
	<view class="contract-review-detail-container">
		<!-- 基本信息卡片 -->
		<view class="info-card">
			<text class="card-title">合同名称：{{ detailInfo.name || '暂无' }}</text>
			<text class="card-title">状态：{{ getStatusText(detailInfo.status) }}</text>
			<text class="card-title">审查类型：{{ getContractTypeText(detailInfo.contractType) }}</text>
			<text class="card-title">风险等级：{{ getLevelText(detailInfo.level) }}</text>
			<text class="card-title">所属部门：{{ detailInfo.department || '暂无' }}</text>
			<text class="card-title">审核人：{{ detailInfo.auditBy || '暂无' }}</text>
			<text class="card-title">发起人：{{ detailInfo.createdBy || '暂无' }} （{{ detailInfo.createdBy }}）</text>
			<text class="card-title">创建日期：{{ detailInfo.createdAt || '暂无' }}</text>
			<!-- <text class="card-title">截止日期：{{ $uv.timeFormat(detailInfo.deadlineDate, 'yyyy.mm.dd') || '暂无' }}</text> -->
		</view>

		<!-- 说明信息 -->
		<view class="info-card">
			<text class="card-title">说明信息</text>
			<view class="bullet-point">{{ detailInfo.explain || '暂无内容' }}</view>
		</view>

		<!-- 风险描述 -->
		<view class="info-card">
			<text class="card-title">风险描述</text>
			<view class="bullet-point">{{ detailInfo.riskDesc || '暂无内容' }}</view>
		</view>

		<!-- 合同信息 -->
		<view class="info-card" v-if="detailInfo.contractMessage">
			<text class="card-title">合同信息</text>
			<view class="contract-info">
				<text class="info-item">甲方：{{ detailInfo.contractMessage.firstParty || '暂无' }}</text>
				<text class="info-item">乙方：{{ detailInfo.contractMessage.secondParty || '暂无' }}</text>
				<text class="info-item" v-if="detailInfo.contractMessage.thirdParty">丙方：{{
					detailInfo.contractMessage.thirdParty }}</text>
				<text class="info-item">合同金额：{{ detailInfo.contractMessage.money ? '￥' +
					detailInfo.contractMessage.money.toLocaleString() : '暂无' }}</text>
				<text class="info-item">签署日期：{{ detailInfo.contractMessage.signDate || '暂无' }}</text>
				<text class="info-item">履约期限：{{ detailInfo.contractMessage.performancePeriodStart || '暂无' }} 至 {{
					detailInfo.contractMessage.performancePeriodEnd || '暂无' }}</text>
				<text class="info-item">付款方式：{{ getPayWayText(detailInfo.contractMessage.payWay) }}</text>
				<text class="info-item">争议解决：{{ getSolveWayText(detailInfo.contractMessage.solveWay) }}</text>
			</view>
		</view>

		<!-- 合同内容 -->
		<view class="info-card" v-if="detailInfo.contractMessage && detailInfo.contractMessage.content">
			<text class="card-title">合同内容</text>
			<view class="bullet-point">{{ detailInfo.contractMessage.content }}</view>
		</view>

		<!-- 违约责任 -->
		<view class="info-card" v-if="detailInfo.contractMessage && detailInfo.contractMessage.defaultResponsibility">
			<text class="card-title">违约责任</text>
			<view class="bullet-point">{{ detailInfo.contractMessage.defaultResponsibility }}</view>
		</view>

		<!-- 违约责任 -->
		<view class="info-card" v-if="detailInfo.contractMessage && detailInfo.contractMessage.defaultResponsibility">
			<text class="card-title">合同附件</text>
			<FileLink :fileUrl="detailInfo.contractMessage.documentUrl" />
		</view>

		<!-- 附件列表 -->
		<view class="info-card" v-if="detailInfo.contractAttachments && detailInfo.contractAttachments.length > 0">
			<view class="attachment-header">
				<text class="card-title">附件列表 ({{ detailInfo.contractAttachments?.length || 0 }})</text>
			</view>
			<view class="divider"></view>
			<view class="attachment-item" v-for="item in detailInfo.contractAttachments" :key="item.id">
				<view class="attachment-left">
					<text class="attachment-name">{{ item.fileName || '未命名文件' }}</text>
					<text class="attachment-size">{{ item.fileSize || '未知大小' }}</text>
				</view>
				<view class="attachment-actions">
					<FilePreview :item="item" />
					<FileDownload :item="item" />
				</view>
			</view>
			<!-- <view class="empty-attachment"
				v-if="!detailInfo.contractAttachments || detailInfo.contractAttachments.length === 0">
				<text class="empty-text">暂无附件</text>
			</view> -->
		</view>

		<!-- 一键审核按钮 -->
		<view class="box-btn">
			<view class="own-btn">
				<uv-button :disabled="detailInfo.status === 'PUBLISHED'" type="primary" @click="startReview">
					一键审核
				</uv-button>
			</view>
			<view class="own-btn">
				<uv-button @click="reviewRecord">
					审查记录
				</uv-button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import FileLink from '@/components/FileLink/FileLink.vue'
import { onShow } from '@dcloudio/uni-app'
import FilePreview from '@/components/FilePreview/FilePreview.vue'
import FileDownload from '@/components/FileDownload/FileDownload.vue'
import complianceApi from '@/api/compliance/index.js'
import contractApi from '@/api/contract/index.js'

const detailInfo = ref({})

// 获取页面参数
const getPageParams = () => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	return currentPage.options
}

// 获取详情数据
const getDetailInfo = async () => {
	const params = getPageParams()
	if (!params.id) {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		return
	}

	uni.showLoading({
		title: '加载中...'
	})

	try {
		const res = await complianceApi.contractReviewDetail({
			id: params.id
		})
		detailInfo.value = res
		uni.hideLoading()
	} catch (err) {
		uni.hideLoading()
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	}
}

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		'MODIFY': '需修改',
		'PENDING': '待审查',
		'PUBLISHED': '发布',
		'REVIEWING': '审核中',
		'REVOKE': '已撤回'
	}
	return statusMap[status] || status
}

// 获取合同类型文本
const getContractTypeText = (type) => {
	const typeMap = {
		'DATA': '数据合同',
		'SERVICE': '服务合同',
		'PURCHASE': '采购合同',
		'SALES': '销售合同'
	}
	return typeMap[type] || '合同'
}

// 获取风险等级文本
const getLevelText = (level) => {
	const levelMap = {
		'GENERAL': '一般',
		'IMPORTANT': '重要',
		'CRITICAL': '关键'
	}
	return levelMap[level] || level
}

// 获取付款方式文本
const getPayWayText = (payWay) => {
	const payWayMap = {
		'CASH': '现金',
		'BANK_TRANSFER': '银行转账',
		'CHECK': '支票',
		'CREDIT_CARD': '信用卡',
		'OTHER': '其他'
	}
	return payWayMap[payWay] || payWay
}

// 获取争议解决方式文本
const getSolveWayText = (solveWay) => {
	const solveWayMap = {
		'ARBITRATION': '仲裁',
		'LITIGATION': '诉讼',
		'MEDIATION': '调解',
		'OTHER': '其他'
	}
	return solveWayMap[solveWay] || solveWay
}

// 检查审核权限
const checkAuditPermission = async () => {
	const params = getPageParams()
	if (!params.id) {
		return false
	}

	try {
		const res = await contractApi.getComplianceProcess({
			objectId: params.id,
			reviewType: 'CONTRACT'
		})
		// 根据接口返回的isAudit字段判断审核权限
		return res
	} catch (err) {
		console.error('检查审核权限失败:', err)
		return false
	}
}

// 审查记录
const reviewRecord = () => {
	if (detailInfo.value.complianceReview?.id === null) {
		uni.showToast({
			title: '暂无审查记录',
			icon: 'none'
		})
		return
	}
	const params = getPageParams()
	if (!params.id) {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		return
	}

	uni.navigateTo({
		url: `/pages/component/respond/reviewRecord?id=${params.id}&type=contract`
	})
}

// 一键审核
const startReview = async () => {
	const params = getPageParams()
	if (!params.id) {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		return
	}

	// 根据complianceReview字段判断跳转页面
	if (detailInfo.value.complianceReview?.id === null) {
		// complianceReview为null，进入initiate页面
		uni.navigateTo({
			url: `/pages/component/respond/initiate?id=${params.id}&type=contract`
		})
	} else {
		// complianceReview不为null，先检查审核权限
		const hasAuditPermission = await checkAuditPermission()
		if (!hasAuditPermission) {
			uni.showToast({
				title: '暂无审核权限',
				icon: 'none'
			})
			return
		}

		if (hasAuditPermission.isSubmit) {
			console.log('开始审核')
			uni.navigateTo({
				url: `/pages/component/respond/initiate?id=${params.id}&type=contract&complianceReview=${JSON.stringify(detailInfo.value.complianceReview)}`
			})
			return
		} else {
			if (!hasAuditPermission.isAudit) {
				uni.showToast({
					title: '暂无审核权限',
					icon: 'none'
				})
				return
			}
		}


		// 进入complianceRegulations页面
		uni.showModal({
			title: '确认操作',
			content: '确定要进行合规审查吗？',
			confirmText: '确定',
			cancelText: '取消',
			success: (res) => {
				if (res.confirm) {
					const detailObj = {
						name: detailInfo.value.name,
						status: getStatusText(detailInfo.value.status),
						contractType: getContractTypeText(detailInfo.value.contractType),
						level: getLevelText(detailInfo.value.level),
						department: detailInfo.value.department,
						auditBy: detailInfo.value.auditBy,
						createdBy: detailInfo.value.createdBy,
						createdAt: detailInfo.value.createdAt,
					}
					uni.navigateTo({
						url: `/pages/component/respond/complianceRegulations?id=${params.id}&type=contract&detailInfo=${encodeURIComponent(JSON.stringify(detailObj))}`
					})
				} else if (res.cancel) {
					// 用户点击了取消
					// 不进行智能审查操作
				}
			}
		})
	}
}

// onMounted(async () => {
// 	await getDetailInfo()
// })

// 每次进入页面都重新获取最新数据
onShow(async () => {
	await getDetailInfo()
})
</script>

<style lang="scss" scoped>
.contract-review-detail-container {
	padding: 32rpx;
	background: #F5F5F5;
	min-height: 100vh;

	.info-card {
		background: white;
		border-radius: 12rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		.card-title {
			font-size: 28rpx;
			font-weight: bold;
			color: #1A73E8;
			margin-bottom: 16rpx;
			display: block;
		}

		.bullet-point {
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
			padding-left: 24rpx;
			position: relative;

			&::before {
				content: '•';
				position: absolute;
				left: 0;
				color: #1A73E8;
				font-weight: bold;
			}
		}

		.contract-info {
			.info-item {
				font-size: 26rpx;
				color: #666;
				margin-bottom: 12rpx;
				display: block;
				line-height: 1.5;
			}
		}

		.attachment-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
		}

		.divider {
			height: 2rpx;
			background: #f0f0f0;
			margin-bottom: 24rpx;
		}

		.attachment-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16rpx 0;
			border-bottom: 1rpx solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			.attachment-left {
				flex: 1;
				display: flex;
				flex-direction: column;

				.attachment-name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 8rpx;
				}

				.attachment-size {
					font-size: 24rpx;
					color: #999;
				}
			}

			.attachment-actions {
				display: flex;
				gap: 16rpx;
			}
		}

		.empty-attachment {
			text-align: center;
			padding: 40rpx 0;

			.empty-text {
				font-size: 28rpx;
				color: #999;
			}
		}
	}
}
</style>