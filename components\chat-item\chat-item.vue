<!-- z-paging聊天item -->

<template>
	<view class="chat-item">
		<text class="chat-time" v-if="item.time&&item.time.length">
			{{item.time}}
		</text>
		<view :class="{'chat-container':true,'chat-location-me':item.isMe}">
			<view class="chat-icon-container">
				<image class="chat-icon" :src="item.icon" mode="aspectFill" />
			</view>
			<view class="chat-content-container">
				<text :class="{'chat-user-name':true,'chat-location-me':item.isMe}">
					{{item.name}}
				</text>
				<view class="chat-text-container-super" :style="[{justifyContent:item.isMe?'flex-end':'flex-start'}]">
					<view :class="{'chat-text-container':true,'chat-text-container-me':item.isMe}">
						<!-- AI回复使用markdown解析 -->
						<zero-markdown-view v-if="!item.isMe" :markdown="processedContent" class="markdown-content" />
						<!-- 用户消息使用普通文本 -->
						<text v-else :class="{'chat-text':true,'chat-text-me':item.isMe}">{{item.content}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import zeroMarkdownView from '@/uni_modules/zero-markdown-view/components/zero-markdown-view/zero-markdown-view.vue'
	
	export default {
		name:"chat-item",
		components: {
			'zero-markdown-view': zeroMarkdownView
		},
		props: {
			item: {
				type: Object,
				default: function() {
					return {
						time: '',
						icon: '',
						name: '',
						content: '',
						isMe: false
					}
				}
			}
		},
		computed: {
			// 流式输出时代码块处理，防止markdown渲染闪烁
			processedContent() {
				// 使用更精确的方法检测未闭合的代码块
				return this.msgContent(this.item.content);
			}
		},
		methods: {
			msgContent(content) {
				if (!content) return '';
				
				// 检查是否有未闭合的代码块
				const codeBlockRegex = /```[\s\S]*?```/g;
				const matches = content.match(codeBlockRegex) || [];
				
				// 计算所有```的总数
				const totalBackticks = (content.match(/```/g) || []).length;
				
				// 如果总数为奇数，说明有未闭合的代码块
				if (totalBackticks % 2 === 1) {
					// 检查最后一个```是否是开始标记
					const lastBacktickIndex = content.lastIndexOf('```');
					if (lastBacktickIndex !== -1) {
						// 检查这个```之后是否还有完整的代码块
						const afterLastBacktick = content.substring(lastBacktickIndex);
						const hasClosingBacktick = afterLastBacktick.indexOf('```', 3) !== -1;
						
						if (!hasClosingBacktick) {
							// 确实有未闭合的代码块，添加换行符
							return content + '\n';
						}
					}
				}
				
				return content;
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	.chat-item {
		display: flex;
		flex-direction: column;
		padding: 20rpx;
	}
	.chat-time {
		padding: 4rpx 0rpx;
		text-align: center;
		font-size: 22rpx;
		color: #aaaaaa;
	}
	.chat-container {
		display: flex;
		flex-direction: row;
	}
	.chat-location-me {
		flex-direction: row-reverse;
		text-align: right;
	}
	.chat-icon-container {
		margin-top: 12rpx;
	}
	.chat-icon{
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #eeeeee;
	}
	.chat-content-container {
		margin: 0rpx 15rpx;
	}
	.chat-user-name{
		font-size: 26rpx;
		color: #888888;
	}
	.chat-text-container {
		text-align: left;
		background-color: #f1f1f1;
		border-radius: 8rpx;
		padding: 10rpx 15rpx;
		margin-top: 10rpx;
		/* #ifndef APP-NVUE */
		max-width: 500rpx;
		/* #endif */
	}
	.chat-text-container-me {
		background-color: #007AFF;
	}
	.chat-text-container-super {
		display: flex;
		flex-direction: row;
	}
	.chat-text {
		font-size: 28rpx;
		/* #ifndef APP-NVUE */
		word-break: break-all;
		/* #endif */
		/* #ifdef APP-NVUE */
		max-width: 500rpx;
		/* #endif */
	}
	.chat-text-me {
		color: white;
	}
	
	/* Markdown内容样式 */
	.markdown-content {
		font-size: 28rpx;
		line-height: 1.6;
		/* #ifndef APP-NVUE */
		word-break: break-all;
		/* #endif */
		/* #ifdef APP-NVUE */
		max-width: 500rpx;
		/* #endif */
		/* 防止markdown渲染时背景闪烁 */
		background-color: transparent !important;
		transition: none !important;
	}
	
	/* 为markdown组件内的元素设置稳定样式 */
	.markdown-content {
		background-color: transparent !important;
		transition: none !important;
	}
	
	/* 特别处理代码块背景 */
	.markdown-content pre,
	.markdown-content code {
		background-color: #f5f5f5 !important;
		border-radius: 4rpx;
		padding: 8rpx;
		transition: none !important;
	}
</style>