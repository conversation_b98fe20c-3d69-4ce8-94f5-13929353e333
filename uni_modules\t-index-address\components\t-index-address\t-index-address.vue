<template>
	<view class="t-index-address">
		<scroll-view class="t-index-address__scroll-view" :scroll-into-view="scrollview" :scroll-y="true"
			:enable-flex="true">
			<!-- <view class="css_functionaldomain">
				<view class="title">
					我的关注
				</view>
				<view class="box">
					<u-grid :border="false" col="4" @click="navchange($event)">
						<u-grid-item v-for="(item, index) in FeatureList" :index="index" :key="index">
							<view style="width: 72rpx;height: 72rpx;">
								<u--image @load="load($event,item)" :showLoading="true" :src="item.iconUrl"
									width="72rpx" height="72rpx">
									<template v-slot:loading>
										<u-loading-icon color="red"></u-loading-icon>
									</template>
								</u--image>
							</view>
							<text class="color14 f28 mt-16">{{ item.title }}</text>
						</u-grid-item>
					</u-grid>
				</view>
			</view> -->
			<view :id="group.initial" v-for="group in cityList" :key="group.initial">
				<view class="t-index-address__anchor">
					<text>{{ group.initial }}</text>
				</view>
				<view class="t-index-address__list">
					<view class="t-index-address__cell" v-for="(city, index) in group.list" :key="index"
						@click="$emit('select', city)">
						<view class="flex aic jcc">
							<view style="width: 60rpx;height: 60rpx;">
								<u--image :showLoading="true" :src="ImageProcessing(city.car_image)" width="60rpx"
									height="60rpx" radius="50%">
									<template v-slot:loading>
										<u-loading-icon color="red"></u-loading-icon>
									</template>
								</u--image>
							</view>
						</view>
						<view class="box">
							<text>{{ city.name }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="t-index-address__sidebar">
			<view class="t-index-address__index" v-for="group in cityList" :key="group.initial"
				@touchstart.stop.prevent="onTouchMove(group.initial)" @touchend.stop.prevent="onTouchStop"
				@touchcancel.stop.prevent="onTouchStop">
				<span>{{ group.initial }}</span>
			</view>
		</view>
		<view class="t-index-address__alert" v-if="touchmove">
			<text>{{ activeIndex }}</text>
		</view>
	</view>
</template>

<script>
	// import cityList from "./cities.json";

	export default {
		data() {
			return {
				scrollview: "A",
				activeIndex: "A",
				touchmove: false,
				ceshi: this.$store.state.ceshi,
				FeatureList: [],
			};
		},
		props: {
			cityList: {
				type: Array,
				default: []
			},
		},
		watch: {
			activeIndex(value) {
				this.scrollview = value;
			},
		},
		options: {
			styleIsolation: 'shared'
		},
		methods: {
			// 检测图片
			ImageProcessing(url) {
				console.log(this.$tools.Image(url), 9999999)
				return this.$tools.Image(url)
			},
			load(e) {
				// console.log(e, '123456')
			},
			// initCityList() {
			// 	this.cityList = cityList;
			// 	console.log(this.cityList, '测试数据')
			// },
			onTouchMove(index) {
				this.activeIndex = index;
				this.touchmove = true;
			},
			onTouchStop() {
				this.touchmove = false;
			},
		},
		mounted() {
			// this.initCityList();
		},
	};
</script>

<style lang="scss" scoped>
	.t-index-address {
		height: 100%;

		&__scroll-view {
			width: 100%;
			height: 100%;
			max-height: 100vh;
		}

		&__anchor {
			padding: 15rpx 30rpx;
			width: 100%;
			font-size: 32rpx;
			font-weight: 500;
			color: #606266;
			background-color: rgb(245, 245, 245);
		}

		&__list {
			padding: 0 70rpx 0 30rpx;
			background-color: #fff;
		}

		&__cell {
			display: flex;
			align-items: center;

			.box {
				margin-left: 24rpx;
				padding: 20rpx 0;
				width: 100%;
				height: 100%;
				border-bottom: 1rpx solid #f2f2f2;
				font-size: 28rpx;
				font-weight: 500;
			}

			// height: 100rpx;
			// line-height: 100rpx;
			// border-bottom: 1rpx solid #f2f2f2;

			// &:last-child {
			// 	border: none;
			// }
		}

		&__sidebar {
			position: absolute;
			top: 50%;
			right: 0;
			transform: translateY(-50%);
			z-index: 99;
			font-size: 28rpx;
		}

		&__index {
			padding: 10rpx 20rpx;
			font-size: 22rpx;
			font-weight: 500;
			line-height: 1;
		}

		&__alert {
			position: fixed;
			top: 50%;
			right: 90rpx;
			z-index: 99;
			margin-top: -60rpx;
			width: 120rpx;
			height: 120rpx;
			font-size: 50rpx;
			color: #fff;
			border-radius: 24rpx;
			background-color: rgba(0, 0, 0, 0.5);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	// .t-index-address__sidebar {
	// 	background-color: red;

	// }
	.css_functionaldomain {
		.title {
			padding: 16rpx 32rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #606266;
		}

		.box {
			padding-bottom: 20rpx;
			background-color: #fff;
			padding-right: 32rpx;

			.u-grid-item::v-deep {
				margin-top: 20rpx !important;
			}
		}
	}
</style>