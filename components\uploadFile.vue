<template>
  <view class="file-upload-container">
    <view class="form-item">
      <view class="file-uploader">
        <!-- 独立的上传按钮 -->
        <view class="upload-btn-wrap" v-if="!disabled && (modelValue?.length || 0) < limit">
          <view class="upload-btn" @click="handleUpload">
            <uni-icons type="plus" size="24" color="#409EFF" />
            <text class="upload-text" v-if="showUploadText">添加文件</text>
          </view>
        </view>
        <!-- 文件列表 -->
        <view class="file-list-wrap">
          <view v-for="(file, index) in modelValue" :key="file.url || index" class="file-item" @click="previewFile(file)">
            <!-- 文件类型图标 -->
            <view class="file-icon">
              <uni-icons :type="getFileIcon(file.type || file.name)" size="24" color="#409EFF" />
            </view>

            <!-- 文件信息 -->
            <view class="file-info">
              <text class="file-name">{{ truncateFileName(file.name || file.fileName) }}</text>
              <text class="file-size">{{ formatFileSize(file.size || file.fileSize) }}</text>
            </view>

            <!-- 上传状态 -->
            <view class="upload-status" v-if="file.status">
              <uni-icons v-if="file.status === 'uploading'" type="spinner-cycle" size="18" color="#409EFF" />
              <uni-icons v-else-if="file.status === 'success'" type="checkmarkempty" size="18" color="#67C23A" />
              <uni-icons v-else-if="file.status === 'error'" type="closeempty" size="18" color="#F56C6C" />
            </view>

            <!-- 删除按钮 -->
            <view class="delete-btn" @click.stop="handleRemove(index)" v-if="!disabled">
              <uni-icons type="close" size="14" color="#fff" />
            </view>
          </view>
        </view>

        <!-- 独立的上传按钮 -->
        <!-- <view 
          class="upload-btn-wrap"
          v-if="!disabled && (modelValue?.length || 0) < limit"
        >
          <view class="upload-btn" @click="handleUpload">
            <uni-icons type="plus" size="24" color="#409EFF" />
            <text class="upload-text" v-if="showUploadText">添加文件</text>
          </view>
        </view> -->
      </view>

      <!-- 提示文字 -->
      <view class="file-tip" v-if="tipText">{{ tipText }}</view>
    </view>
  </view>
</template>

<script>
import { config } from '@/config'
import { useUserStore } from '@/store/pinia.js';
export default {
  name: 'uni-file-upload',
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    // 文件类型筛选（参考uni.chooseFile参数）
    fileType: {
      type: String,
      default: 'all' // image/video/all
    },
    // 最大上传数量
    limit: {
      type: Number,
      default: 9
    },
    // 单个文件大小限制（MB）
    sizeLimit: {
      type: Number,
      default: 10
    },
    // 提示文字
    tipText: String,
    // 是否禁用
    disabled: Boolean,
    // 是否显示上传文字
    showUploadText: {
      type: Boolean,
      default: true
    },
    // 是否自动上传到服务器
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 租户ID
    tenantId: {
      type: String,
      default: ''
    },
    // 服务名称
    serviceName: {
      type: String,
      default: ''
    },
    // 分类名称
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadUrl: '/services/whiskerguardgeneralservice/api/file/upload',
      uploadQueue: [] // 上传队列
    }
  },
  methods: {
    async handleUpload() {
      if (this.disabled) return

      try {
        let res = null
        let files = null
        // 微信小程序环境处理
        //#ifdef MP-WEIXIN
        res = await new Promise((resolve, reject) => {
          wx.chooseMessageFile({
            count: this.limit - this.modelValue.length,
            type: this.fileType === 'image' ? 'image' : 'all',
            success: resolve,
            fail: reject
          })
        })
        console.log('res', res)
        files = res.tempFiles.map(file => ({
          name: file.name,
          path: file.path,
          size: file.size,
          type: file.type
        }))
        // 其他平台处理
        //#else
        res = await uni.chooseFile({
          count: this.limit - this.modelValue.length,
          type: this.fileType,
          extension: this.fileType === 'image' ? ['jpg', 'png', 'jpeg'] : undefined
        })
        files = res.tempFiles
        //#endif

        // 统一验证逻辑
        const validFiles = files.filter(file => {
          if (file.size > this.sizeLimit * 1024 * 1024) {
            uni.showToast({ title: `${file.name}超过大小限制`, icon: 'none' })
            return false
          }
          return true
        })

        // 添加状态标记
        const filesWithStatus = validFiles.map(file => ({
          ...file,
          status: this.autoUpload ? 'uploading' : '',
          uploadResult: null
        }))

        // 更新文件列表
        const newValue = [...this.modelValue, ...filesWithStatus]
        this.$emit('update:modelValue', newValue)

        // 自动上传
        if (this.autoUpload && filesWithStatus.length > 0) {
          // 将新添加的文件加入上传队列
          for (let i = 0; i < filesWithStatus.length; i++) {
            const fileIndex = this.modelValue.length + i
            await this.uploadFile(filesWithStatus[i], fileIndex)
          }
        }
      } catch (err) {
        console.log('文件选择取消', err)
      }
    },

    // 上传文件到服务器
    async uploadFile(file, index) {
      if (!file || !file.path) {
        console.error('文件路径不存在')
        return
      }
       const userStore = useUserStore()
      // 更新文件状态为上传中
      this.updateFileStatus(index, 'uploading')

      return new Promise((resolve, reject) => {
        const uploadTask = uni.uploadFile({
          url: config.baseUrl + this.uploadUrl + `?tenantId=${this.tenantId}&serviceName=${this.serviceName}&categoryName=${this.categoryName}`,
          filePath: file.path,
          name: 'file',
          formData: {},
          header: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${userStore.token ? userStore.token : null}`,
          },
          // 添加查询参数
          // data: {
          //   tenantId: this.tenantId,
          //   serviceName: this.serviceName,
          //   categoryName: this.categoryName
          // },
          success: (uploadRes) => {
            try {
              // 解析返回结果
              const result = typeof uploadRes.data === 'string' 
                ? JSON.parse(uploadRes.data) 
                : uploadRes.data
              
              // 更新文件状态和结果
              this.updateFileStatus(index, 'success', result)
              
              // 触发上传成功事件
              this.$emit('upload-success', {
                file,
                index,
                result
              })
              
              resolve(result)
            } catch (e) {
              console.error('解析上传结果失败', e)
              this.updateFileStatus(index, 'error')
              this.$emit('upload-error', {
                file,
                index,
                error: e
              })
              reject(e)
            }
          },
          fail: (err) => {
            console.error('上传失败', err)
            this.updateFileStatus(index, 'error')
            this.$emit('upload-error', {
              file,
              index,
              error: err
            })
            reject(err)
          }
        })

        // 监听上传进度
        uploadTask.onProgressUpdate((res) => {
          // 更新上传进度
          this.updateFileProgress(index, res.progress)
          // 触发进度事件
          this.$emit('upload-progress', {
            file,
            index,
            progress: res.progress
          })
        })
      })
    },

    // 更新文件状态
    updateFileStatus(index, status, result = null) {
      const files = [...this.modelValue]
      if (files[index]) {
        files[index] = {
          ...files[index],
          status,
          uploadResult: result
        }
        // 如果上传成功，将服务器返回的信息合并到文件对象中
        if (status === 'success' && result) {
          files[index] = {
            ...files[index],
            buketName: result.buketName,
            key: result.key,
            url: result.url,
            filePath: result.key,
            fileName: files[index].name,
            fileType: files[index].name.split('.').pop(),
            fileSize: files[index].size
          }
        }
        this.$emit('update:modelValue', files)
      }
    },

    // 更新文件上传进度
    updateFileProgress(index, progress) {
      const files = [...this.modelValue]
      if (files[index]) {
        files[index] = {
          ...files[index],
          progress
        }
        this.$emit('update:modelValue', files)
      }
    },

    // 手动触发上传
    startUpload() {
      const pendingFiles = this.modelValue.filter(file => !file.status || file.status === 'error')
      
      if (pendingFiles.length === 0) {
        uni.showToast({ title: '没有待上传的文件', icon: 'none' })
        return
      }

      // 上传所有待上传的文件
      pendingFiles.forEach(async (file, i) => {
        const index = this.modelValue.findIndex(f => f === file)
        if (index !== -1) {
          await this.uploadFile(file, index)
        }
      })
    },

    // 获取上传结果
    getUploadResults() {
      return this.modelValue
        .filter(file => file.status === 'success' && file.uploadResult)
        .map(file => file.uploadResult)
    },

    handleRemove(index) {
      uni.showModal({
        title: '提示',
        content: '确定删除该文件吗？',
        success: (res) => {
          if (res.confirm) {
            const newValue = [...this.modelValue]
            newValue.splice(index, 1)
            this.$emit('update:modelValue', newValue)
            // 触发删除事件
            this.$emit('file-remove', { index, file: this.modelValue[index] })
          }
        }
      })
    },

    previewFile(file) {
      if (this.disabled) return

      // 使用文件的url或path
      const fileUrl = file.url || file.path
      if (!fileUrl) {
        uni.showToast({ title: '文件路径不存在', icon: 'none' })
        return
      }

      // 判断文件类型
      const fileName = file.name || file.fileName || ''
      if (file.type?.startsWith('image') || /\.(png|jpe?g|gif|webp)$/i.test(fileName)) {
        uni.previewImage({
          urls: this.modelValue
            .filter(f => f.url || f.path)
            .map(f => f.url || f.path),
          current: fileUrl,
          fail: () => {
            uni.showToast({ title: '预览失败', icon: 'none' })
          }
        })
      } else {
        // 尝试打开文档
        uni.openDocument({
          filePath: fileUrl,
          showMenu: true,
          fail: () => {
            uni.showToast({ title: '此文件类型不支持预览', icon: 'none' })
          }
        })
      }
    },

    getFileIcon(filename) {
      if (!filename) return 'file'
      
      const ext = filename.split('.').pop().toLowerCase()
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp']
      const docTypes = ['doc', 'docx']
      const xlsTypes = ['xls', 'xlsx']
      const pptTypes = ['ppt', 'pptx']
      const pdfType = ['pdf']

      if (imageTypes.includes(ext)) return 'image'
      if (docTypes.includes(ext)) return 'document'
      if (xlsTypes.includes(ext)) return 'stats'
      if (pptTypes.includes(ext)) return 'slides'
      if (pdfType.includes(ext)) return 'pdf'
      return 'file'
    },

    truncateFileName(name, maxLength = 10) {
      if (!name) return '未命名文件'
      if (name.length <= maxLength) return name
      return `${name.substring(0, maxLength)}...${name.split('.').pop()}`
    },

    formatFileSize(bytes) {
      if (!bytes) return ''
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
    }
  }
}
</script>

<style lang="scss" scoped>
.file-upload-container {
  padding: 16rpx 0;

  .file-uploader {
    position: relative;
  }

  .file-list-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 20rpx;
  }

  .file-item {
    position: relative;
    flex: 0 0 calc(33.33% - 25rpx); // 3列布局，留出间距
    background: #f5f7fa;
    border-radius: 12rpx;
    padding: 16rpx;
    box-sizing: border-box;
    min-height: 160rpx;

    .file-icon {
      text-align: center;
      margin-bottom: 16rpx;
    }

    .file-info {
      text-align: center;

      .file-name {
        font-size: 24rpx;
        color: #606266;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }

      .file-size {
        font-size: 20rpx;
        color: #909399;
        margin-top: 8rpx;
      }
    }

    .upload-status {
      position: absolute;
      right: 10rpx;
      bottom: 10rpx;
      z-index: 1;
    }

    .delete-btn {
      position: absolute;
      right: -10rpx;
      top: -10rpx;
      width: 32rpx;
      height: 32rpx;
      background: #f56c6c;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
    }
  }

  .upload-btn-wrap {
    width: 100%;
    margin-bottom: 20rpx;
    .upload-btn {
      width: 120rpx;
      height: 120rpx;
      border: 2rpx dashed #409EFF;
      border-radius: 12rpx;
      background-color: #f5f7fa;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      // margin: 0 auto;
      transition: all 0.3s;

      &:active {
        opacity: 0.8;
        transform: scale(0.95);
      }

      .upload-text {
        font-size: 24rpx;
        color: #409EFF;
        margin-top: 8rpx;
      }
    }
  }

  .file-tip {
    font-size: 24rpx;
    color: #909399;
    margin-top: 16rpx;
  }
}
</style>