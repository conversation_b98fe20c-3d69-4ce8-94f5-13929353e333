<template>
  <view class="compliance-post-form-container">
    <!-- 头部 -->
    <!-- <view class="header">
          <view class="header-content">
              <uni-icons type="arrowleft" size="24" color="#6B7280" @click="goBack"></uni-icons>
              <text class="header-title">新增风险识别清单项</text>
              <text class="header-save" @click="handleSubmit">保存</text>
          </view>
      </view> -->

    <!-- 表单主体 -->
    <view class="content">
      <uni-forms ref="formRef" :model="formData" :rules="rules" validate-trigger="bind">
        <!-- 1. 部门 -->
        <uni-forms-item name="department">
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">部门</text>
              <text class="required">*</text>
            </view>
            <input type="text" class="form-input" v-model="formData.department" placeholder="请输入风险名称" :inputBorder="false" />
          </view>
        </uni-forms-item>

        <!-- 2. 岗位 -->
        <uni-forms-item name="position">
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">岗位</text>
              <text class="required">*</text>
            </view>
            <view class="dropdown" @click="toggleDropdown">
              <view class="dropdown-toggle">
                <text>{{ formData.position || '请选择' }}</text>
                <uni-icons :type="showDropdown ? 'up' : 'down'" size="16" color="#6B7280" />
              </view>
              <view class="dropdown-menu" v-if="showDropdown">
                <view class="dropdown-item" v-for="level in riskLevels" :key="level" @click.stop="selectLevel(level)">
                  {{ level }}
                </view>
              </view>
            </view>
          </view>
        </uni-forms-item>

        <!-- 3. description -->
        <uni-forms-item name="description">
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">描述</text>
              <text class="required">*</text>
            </view>
            <textarea class="form-textarea" placeholder="请输入建议措施" v-model="formData.description"
              @input="updateCounter('description')" />
            <view class="counter">{{ formData.description.length }}/500</view>
          </view>
        </uni-forms-item>

        <!-- 4. 责任人 -->
        <uni-forms-item name="responsiblePerson">
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">责任人</text>
              <text class="required">*</text>
            </view>
            <input type="text" class="form-input" v-model="formData.responsiblePerson" placeholder="请输入风险名称" :inputBorder="false" />
          </view>
        </uni-forms-item>

        <!-- 5. 联系方式 -->
        <uni-forms-item name="contact">
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">联系方式</text>
              <text class="required">*</text>
            </view>
            <input class="form-input" type="text" v-model="formData.contact" placeholder="请输入风险名称" :inputBorder="false" />
          </view>
        </uni-forms-item>
      </uni-forms>

      <!-- 6. 附件上传 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">6. 附件上传</text>
        </view>
        <upload-files v-model="fileList" title="附件上传" :max-count="5" :max-size="5"
          :file-types="['image', 'document']" />
      </view>
      <view>
        <button  size="default" @click="handleSubmit" type="default" style="color:#ffffff;
              backgroundColor:#3B82F6;
              borderColor:#3B82F6" hover-class="is-hover">保存</button>
      </view>
    </view>
    <goNavitor url="/pages/component/homeSon/quickStart" />
  </view>
</template>

<script setup>
import goNavitor from '../../../components/goNavitor.vue'
import { ref } from 'vue'
import UploadFiles from '@/components/uploadFile.vue'

// 表单引用
const formRef = ref(null)

const fileList = ref([])

// 表单数据
const formData = ref({
  department: '',
  position: '',
  description: [],
  responsiblePerson: '',
  contact: '',
  files: []
})

// 验证规则
const rules = {
  department: {
    rules: [{ required: true, errorMessage: '请输入风险名称' }]
  },
  position: {
    rules: [{ required: true, errorMessage: '请选择风险等级' }]
  },
  //     description: {
  //   rules: [{
  //     validateFunction: (rule, value, callback) => {
  //       if (!value || value.length === 0) {
  //         callback(new Error('请至少选择一个影响范围'))
  //       } else {
  //         callback(null) // 必须明确传递null表示验证通过
  //       }
  //     }
  //   }]
  // },
  description: {
    rules: [
      { required: true, errorMessage: '请输入风险描述' },
      { maxLength: 500, errorMessage: '内容不能超过500字' }
    ]
  },
  responsiblePerson: {
    rules: [
      { required: true, errorMessage: '请输入建议措施' },
      { maxLength: 500, errorMessage: '内容不能超过500字' }
    ]
  }
}

// 下拉菜单状态
const showDropdown = ref(false)
const riskLevels = ['高', '中', '低']
const scopes = [
  { value: '财务', label: '财务' },
  { value: '人事', label: '人事' },
  { value: '生产', label: '生产' },
  { value: '销售', label: '销售' },
  { value: '研发', label: '研发' },
  { value: '其他', label: '其他' }
]

// 方法定义
const toggleDropdown = () => showDropdown.value = !showDropdown.value

const selectLevel = (level) => {
  formData.value.riskLevel = level
  showDropdown.value = false
  formRef.value.validateField('riskLevel')
}

const toggleScope = (scope) => {
  const index = formData.value.description.indexOf(scope)
  index === -1
    ? formData.value.description.push(scope)
    : formData.value.description.splice(index, 1)
  formRef.value.validateField('description')
}

const updateCounter = (field) => {
  if (formData.value[field].length > 500) {
    formData.value[field] = formData.value[field].substring(0, 500)
  }
}

const uploadFile = () => {
  uni.chooseFile({
    count: 5,
    type: 'all',
    success: (res) => {
      const files = res.tempFiles.map(file => ({
        name: file.name,
        url: file.path
      }))
      formData.value.files = [...formData.value.files, ...files]
    }
  })
}

const removeFile = (index) => formData.value.files.splice(index, 1)

const goBack = () => uni.navigateBack()

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    // 提交逻辑
    uni.showToast({ title: '保存成功', icon: 'success' })
    setTimeout(goBack, 1500)
  } catch (err) {
    uni.showToast({ title: '请完善表单信息', icon: 'none' })
  }
}
</script>

<style lang="scss">
/* 保持原有样式不变，添加表单错误提示样式 */
/* 调整表单元素间距 */
.form-item {
  margin-bottom: 40rpx !important;
}

.compliance-post-form-container {
  display: flex;
  flex-direction: column;
  // height: 100%;
  background-color: #F9FAFB;

  .content {
    background-color: inherit;
    flex: 1;
    padding: 32rpx 32rpx 100rpx;
  }

  .form-item {
    margin-bottom: 48rpx;
  }

  .form-label {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .label-text {
    font-size: 28rpx;
    color: #374151;
    font-weight: 500;
  }

  .required {
    color: #EF4444;
    margin-left: 8rpx;
  }

  .form-input {
    height: 80rpx;
    padding: 0 24rpx;
    border: 1px solid #e0e0e0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1a1a1a;
    background-color: #FFFFFF;
  }

  .form-input:focus {
    border-color: #3B82F6;
  }

  .dropdown {
    position: relative;
    width: 100%;
  }

  .dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    border: 1px solid #E5E7EB;
    border-radius: 8rpx;
    font-size: 28rpx;
    background-color: #FFFFFF;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    z-index: 10;
    margin-top: 8rpx;
  }

  .dropdown-item {
    padding: 20rpx 24rpx;
    font-size: 28rpx;
  }

  .dropdown-item:active {
    background-color: #F3F4F6;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
  }

  .tag-item {
    display: inline-flex;
    align-items: center;
    padding: 16rpx 32rpx;
    border-radius: 32rpx;
    background-color: #F3F4F6;
    font-size: 24rpx;
    transition: all 0.2s ease;
  }

  .tag-item.selected {
    background-color: #3B82F6;
    color: #FFFFFF;
    box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.3);
  }

  .form-textarea {
    width: 100%;
    box-sizing: border-box;
    min-height: 200rpx;
    padding: 24rpx;
    border: 1px solid #E5E7EB;
    background-color: #FFFFFF;
    border-radius: 8rpx;
    font-size: 28rpx;
  }

  .counter {
    text-align: right;
    font-size: 24rpx;
    color: #6B7280;
    margin-top: 8rpx;
  }

  .file-uploader {
    display: flex;
    align-items: center;
  }

  .upload-btn {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #3B82F6;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 24rpx;
    flex-shrink: 0;
  }

  .file-list {
    flex: 1;
    white-space: nowrap;
  }

  .file-item {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 16rpx;
    border-radius: 8rpx;
    background-color: #F3F4F6;
    margin-right: 16rpx;
    font-size: 24rpx;
  }

  .file-name {
    max-width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .delete-btn {
    position: absolute;
    top: -12rpx;
    right: -12rpx;
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    background-color: #EF4444;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .file-tip {
    font-size: 24rpx;
    color: #6B7280;
    margin-top: 16rpx;
  }
}
</style>