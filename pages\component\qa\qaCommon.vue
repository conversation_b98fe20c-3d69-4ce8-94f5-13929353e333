<template>
	<view class="common-questions-container">
		<!-- 常见问题列表 -->
		<view class="common-questions">
			<text class="section-title">常见问题</text>
			<view v-if="commonQuestions.length > 0" class="question-list">
				<view 
					class="question-item" 
					v-for="(item, index) in commonQuestions" 
					:key="item.id || index"
					@click="handleQuestionClick(item)"
				>
					<view class="question-content">
						<text class="question-text" style="margin-left: 0;">{{index+1}}.</text>
						<text class="question-text">{{item.questionTitle}}</text>
					</view>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view v-else-if="loading" class="loading">
				<text class="loading-text">加载中...</text>
			</view>
			<view v-else class="empty-questions">
				<text class="empty-text">暂无常见问题</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import qaApi from '@/api/chatAI/qa.js'

// 响应式数据
const commonQuestions = ref([])
const loading = ref(false)

// 获取常见问题列表
const fetchCommonQuestions = async () => {
	loading.value = true
	try {
		const response = await qaApi.queryCommonQuestions()
		if (response && Array.isArray(response)) {
			// 过滤启用的问题并按排序权重排序
			commonQuestions.value = response
				.filter(item => item.isEnabled)
				.sort((a, b) => {
					// 按排序权重降序，权重相同则按创建时间降序
					if (b.sortOrder !== a.sortOrder) {
						return b.sortOrder - a.sortOrder
					}
					return new Date(b.createdAt?.seconds * 1000) - new Date(a.createdAt?.seconds * 1000)
				})
		} else {
			commonQuestions.value = []
		}
	} catch (error) {
		console.error('获取常见问题失败:', error)
		commonQuestions.value = []
		uni.showToast({
			title: '获取常见问题失败',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 点击问题项
const handleQuestionClick = (question) => {
	uni.navigateTo({
		url: '/pages/component/qa/hotQuestions',
		events: {
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			acceptDataFromOpenedPage: function(data) {
				console.log('接收到数据:', data)
			}
		},
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据
			res.eventChannel.emit('acceptDataFromOpenerPage', { question })
		}
	})
}

// 页面加载时获取数据
onMounted(() => {
	fetchCommonQuestions()
})
</script>

<style lang="scss" scoped>
 @import '/static/css/aiChat.scss'; 
</style>